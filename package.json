{"name": "franchising-module", "homepage": "./", "version": "0.2.48-beta-crs", "private": true, "build": {"appId": "com.veridata.neo", "files": ["build/*", "build/**/*", "public/*", "public/**/*", "!**/*.map"]}, "dependencies": {"@date-io/date-fns": "1.3.13", "@date-io/moment": "1.3.13", "@devexpress/dx-react-core": "^4.0.11", "@devexpress/dx-react-grid": "^4.0.11", "@devexpress/dx-react-grid-material-ui": "^4.0.11", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@hims_client/hims-core": "git+https://oauth2:<EMAIL>/hims-backend/client/npm-library.git#upgrade-xdev-2025", "@mui/icons-material": "^5.16.7", "@mui/lab": "^5.0.0-alpha.125", "@mui/material": "^5.16.7", "@mui/styles": "^5.16.7", "@mui/x-date-pickers": "^7.0.0", "@react-pdf/renderer": "^4.0.0", "@reduxjs/toolkit": "1.9.7", "@testing-library/jest-dom": "4.2.4", "@testing-library/react": "9.5.0", "@testing-library/user-event": "7.2.1", "axios": "^1.3.1", "crypto": "npm:crypto-browserify@^3.12.0", "date-fns": "2.13.0", "dotenv": "^16.4.5", "eslint-plugin-react": "^7.35.0", "globals": "^15.9.0", "html-to-pdfmake": "2.0.1", "jquery": "3.5.0", "jquery-modal": "0.9.2", "js-file-download": "0.4.12", "moment": "^2.30.1", "path": "^0.12.7", "pdfmake": "^0.1.72", "react": "^19.1.1", "react-avatar-edit": "1.2.0", "react-dom": "^19.1.1", "react-dropzone": "^10.2.2", "react-error-overlay": "6.0.11", "react-idle-timer": "^5.7.2", "react-number-format": "^5.4.0", "react-redux": "9.1.2", "react-router-dom": "^5.3.3", "workbox-precaching": "^7.3.0"}, "scripts": {"start": "NODE_OPTIONS=--openssl-legacy-provider react-app-rewired start", "build": "react-app-rewired build", "build:xdev": "REACT_APP_API=xdev react-scripts build", "build:vni": "cross-env REACT_APP_API=vni react-scripts build", "build:vni_uat": "REACT_APP_API=vni_uat react-scripts build", "build:intellicare_uat": "REACT_APP_API=intellicare_uat react-scripts build", "build:production": "REACT_APP_API=production react-scripts build", "build:production_avega": "REACT_APP_API=production_avega react-scripts build", "build:production_intellicare": "REACT_APP_API=production_intellicare react-scripts build", "test": "react-app-rewired test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@eslint/js": "^9.8.0", "@types/jest": "24.9.1", "@types/jquery": "3.3.37", "@types/node": "12.12.36", "@types/pdfmake": "0.1.13", "@types/react": "^19.1.1", "@types/react-dom": "^19.1.1", "@types/react-redux": "^7.1.27", "@types/react-router-dom": "^5.1.4", "@types/webpack-env": "1.15.1", "ajv": "^7.2.4", "assert": "^2.1.0", "buffer": "^6.0.3", "crypto-browserify": "^3.10.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "process": "^0.11.10", "react-app-rewired": "^2.2.1", "react-scripts": "^5.0.1", "readable-stream": "npm:readable-stream@^2.3.8", "recharts": "^2.13.0-alpha.4", "resolve-url-loader": "^5.0.0", "stream": "npm:stream-browserify@^3.0.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "typescript": ">=5.1.0 <5.2.0", "url": "^0.11.4", "vm": "npm:vm-browserify@^1.1.2"}, "overrides": {"@material-ui/core": "npm:@mui/material@^5.16.7"}, "resolutions": {"postcss": "^8.4.41", "@jridgewell/gen-mapping": "0.1.1"}}