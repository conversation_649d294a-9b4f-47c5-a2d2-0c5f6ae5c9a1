CRITICAL:
YOU ARE NOT ALLOWED TO DELETE FILES.
YOU ARE NOT ALLOWED TO RUN DESTRUCTIVE DATABASE OR EDIT DATABASE COMMANDS.
YOU ARE NOT ALLOWED TO DOW<PERSON>RA<PERSON> PACKAGE IN PACKAGE.JSON
DONT EVERY DOWNGRADE REACT 19 EVER
YOU ARE NOT ALLOWED TO RUN NPM COMMANDS LIKE npm run start, npm run dev, npm test, etc. JUST ASSUME that im already doing that in my local environment.

coding:
- i prefer to use async await over .then and .catch
- i prefer to use functional components and hooks over class components
- i prefer to use modern javascript (ES6+) features and syntax