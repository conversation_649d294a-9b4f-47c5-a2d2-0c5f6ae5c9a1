import { ReasonObj } from "../shared/models/Maintenance";
import { internalGet, internalPost, internalDelete} from "../utils/CoreApi";

// Get Return Reason List
export async function getReturnReason(role:string,token:string) {
  const url = `/reason/return`;
  try{
    return await internalGet([] as ReasonObj[],url,token);
  }catch(err: any){
    console.log(err.response.status);
    if (err.response) {
      switch (err.response.status) {
        case 401:
          console.log("unauthorized user");
          break;
        default:
          break;
      }
    }
  }
}

// Get Disapprove Reason List
export async function getDisapproveReason(role:string,token:string) {
  const url = `/reason/disapprove`;
  try{
    return await internalGet([] as ReasonObj[],url,token);
  }catch(err: any){
    console.log(err.response.status);
    if (err.response) {
      switch (err.response.status) {
        case 401:
          console.log("unauthorized user");
          break;
        default:
          break;
      }
    }
  }
}

// Save Reason
export async function saveReason(obj: ReasonObj,token:string, reasonDesc:string) {
  const url = `/reason/${reasonDesc}`;
  return await internalPost(obj,url,token);
}

// Delete Reason
export async function deleteReason(id : string,token:string, reason:string) {
  const url = `/reason/${reason}/${id}`;
  return await internalDelete(url,token);
}

// Update Reason
export async function updateReason(obj: ReasonObj,token:string, reasonDesc:string) {
  const url = `/reason/${reasonDesc}`;
  return await internalPost(obj,url,token);
}