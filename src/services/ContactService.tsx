import { Contact } from "../shared/models/Contact"
import {
	internalGet,
	internalPost,
	internalPatch,
} from "../utils/CoreApi"
import { apiURL } from "../utils/Environment"

// Sales User Location Province
export async function getProvinceList(token: string) {
	const url = `${apiURL.businessLocation}/province-list`
	return await internalGet([], url, token)
}

// Sales User Location Cities
export async function getCitiesList(token: string) {
	const url = `${apiURL.businessLocation}/city-list`
	return await internalGet([], url, token)
}

// Sales User Location Brgys
export async function getBrgyList(token: string) {
	const url = `${apiURL.businessLocation}/barangay-list`
	return await internalGet([], url, token)
}

// Create contact
export async function createContactService(obj: Contact, token: string) {
	const url = `/contact`
	return await internalPost(obj, url, token)
}

// Update contact status
export async function updateContactStatus(
	id: string,
	action: string,
	values: any,
	token: string,
) {
	const url = `/contact/${id}/${action}`
	return await internalPatch({} as Contact, values, url, token)
}

// Update contact details
export async function updateContact(id: string, values: any, token: string) {
	const url = `/contact/${id}`
	return await internalPatch({} as Contact, values, url, token)
}

// Get contact details by id
export async function getContact(id: string, role: string, token: string) {
	const url = `/contact/${id}`
	return await internalGet({} as Contact, url, token)
}

// Get list of contacts
export async function getContacts(role: string, token: string) {
	const url = `/contact/`
	return await internalGet([] as Contact[], url, token)
}

// Get Office Locations
export async function getOfficeLocationList(token: string) {
	const url = `${apiURL.businessLocation}/offices/list`
	return await internalGet([], url, token)
}
