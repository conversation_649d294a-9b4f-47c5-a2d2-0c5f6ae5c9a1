import { internalGet, internalPatch, internalPut} from "../utils/CoreApi";
import { apiURL } from "../utils/Environment";

export async function getUserData(token: string) {
  const url = `${apiURL.userManagement}/profile`;
  return await internalGet({},url,token);
}

export async function updateUserPassword(obj: any, token: string) {
  const url = `${apiURL.userManagement}/user/password`;
  return await internalPut(obj,url,token);
}

export async function updateProfAvatar(obj: any, token: string){
  const url = `${apiURL.userManagement}/users/profile_pic`;
  return await internalPatch({},obj,url,token);
}
