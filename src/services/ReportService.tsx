import { internalGet, internalPost, internalPatch} from "../utils/CoreApi";
import axios from 'axios'

export async function sendGeneratedReport(id:string, role:string, formdata: any, token:string) {
  const url = `/franchise/reports/${role}/generate/${id}/send`;

  //return await internalPatch({}, url, formdata, token);

  return await axios.patch<Response>(url, formdata,{
          headers: {'Authorization': `Bearer ${token}`}
    })
}

export async function viewGeneratedReport(id: string,role:string,token: string) {
  const url = `/franchise/reports/${role}/generate/${id}`;
  return await internalGet({},url,token);
}

export async function viewEncoderReport(id: string,role:string,token: string) {
  const url = `/franchise/reports/${role}/${id}`;
  return await internalGet({},url,token);
}

export async function getGeneratedReport(role:string,token: string) {
  const url = `/franchise/reports/${role}/generate`;
  return await internalGet([],url,token);
}

export async function postGeneratedReport(role:string, obj: {}, token:string) {
  const url = `/franchise/reports/${role}/generate`;
  return await internalPost(obj,url,token);
}

export async function postGenerateFranchisingReport(role: string, obj: {}, token: string) {
  const url = `/franchise/reports/${role}/generate/Franchising-report`

  console.log('OBJ', obj)
  return await internalPost(obj, url, token);
}

export async function postGeneratedReportBySalesHead(role: string, obj: {}, token: string) {
  const url = `/franchise/reports/${role}/all`

  return await internalPost(obj, url, token);
}

export async function getFranchiseRequestLogs(role:string,token:string) {
  const url = `/franchise/reports/${role}`;

  return await internalGet([],url,token).catch((err)=>{
    console.log(err.response.status);
    if (err.response) {
      switch (err.response.status) {
        case 401:
          console.log("unauthorized user");
          break;
        default:
          break;
      }
    }
  })
}




