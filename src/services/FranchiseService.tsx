import { Franchise } from "../shared/models/Franchise"
import {
	internalGet,
	internalPost,
	internalUploadFile,
	internalPut,
	internalPatch,
	internalDelete,
	FILE_DOWNLOAD,
} from "../utils/CoreApi"
import { apiURL } from "../utils/Environment"

export interface TestCreateFranchiseResponse {
	message: string
	data: TestFrachise
}

export interface UploadResponse {
	_id: string
	path: string
	type: string
	created_at: string
	updated_at: string
}

export interface IndustryList {
	sector: string
	name: string
	effectivity_date: string
	date_updated: string
	status: string
}

export interface BDOUserList {
	group_id: string
	username: string
}

export interface MotherCompanyList {
	_id: string
	corporate_name: string
}

export interface TestFrachise {
	entry_id: string
	// company_id:string;
	channel_of_request: string
	is_refranchise: string
	corporate_name: string
	is_mother_company: boolean
	brand_name: string
	company_affiliates_id: string
	company_affiliates: string
	business_address: string
	tax_number: string
	company_number: string
	industry_class_id: string
	contact_persons: string
	applicant_id: string
	applicant_name: string
	applicant_contact_person: string
	applicant_business_address: string
	applicant_contact_number: string
	applicant_email_address: string
	terms: string
	no_of_employee: string
	no_of_dependents: string
	provider_name: string
	is_voluntary_enrollees: string
	company_paid_shared: string
	stakeholders: string
	signatories: string
	source_of_funds: string
	supporting_docs: string
}

export async function getFranchise(id: string, role: string, token: string) {
	const url = `/franchise/${role}/${id}`
	return await internalGet({} as Franchise, url, token)
}

export async function getFranchises(role: string, token: string) {
	const url = `/franchise/${role}`
	return await internalGet([] as Franchise[], url, token)
	// try {
	// 	return await internalGet([] as Franchise[], url, token);
	// } catch (err) {
	// 	console.log(err.response.status);
	// 	if (err.response) {
	// 		switch (err.response.status) {
	// 			case 401:
	// 				console.log("unauthorized user");
	// 				window.location.replace("../index.html#/");
	// 				break;
	// 			default:
	// 				break;
	// 		}
	// 	}
	// }
}

// Fetch Franchise List
// Sales User
export async function getFranchiseListSpecialist(token: string) {
	const url = `/franchise/validator`
	return await internalGet([] as Franchise[], url, token)
	// try {
	// 	return await internalGet([] as Franchise[], url, token);
	// } catch (err) {
	// 	console.log(err.response.status);
	// 	if (err.response) {
	// 		switch (err.response.status) {
	// 			case 401:
	// 				console.log("unauthorized user");
	// 				window.location.replace("../index.html#/");
	// 				break;
	// 			default:
	// 				break;
	// 		}
	// 	}
	// }
}

// Sales User Industry List Lookup
export async function getIndustryList(role: string, token: string) {
	const url = `/franchise/${role}/industry-list`
	return await internalGet([] as IndustryList[], url, token)
	// try {
	// 	return await internalGet([] as IndustryList[], url, token);
	// } catch (err) {
	// 	console.log("Industry List error: ", err.response.status);
	// 	if (err.response) {
	// 		switch (err.response.status) {
	// 			case 401:
	// 				console.log("unauthorized user");
	// 				window.location.replace("../index.html#/");
	// 				break;
	// 			default:
	// 				break;
	// 		}
	// 	}
	// }
}

// Sales User BDO User List Lookup
export async function getBdoUserList(role: string, token: string) {
	console.log("API URL", apiURL)
	const url = `/franchise/${role}/bdo-list`
	return await internalGet([] as BDOUserList[], url, token)
}

// Sales User Location Regions
export async function getRegionList(role: string, token: string) {
	console.log("API URL", apiURL)
	const url = `${apiURL.businessLocation}/region-list`
	return await internalGet([], url, token)
	// try {
	// 	return await internalGet([], url, token);
	// } catch (err) {
	// 	console.log("Industry List error: ", err.response.status);
	// 	if (err.response) {
	// 		switch (err.response.status) {
	// 			case 401:
	// 				console.log("unauthorized user");
	// 				window.location.replace("../index.html#/");
	// 				break;
	// 			default:
	// 				break;
	// 		}
	// 	}
	// }
}

// Sales User Location Province
export async function getProvinceList(role: string, token: string) {
	console.log("API URL", apiURL)
	const url = `${apiURL.businessLocation}/province-list`
	return await internalGet([], url, token)
	// try {
	// 	return await internalGet([], url, token);
	// } catch (err) {
	// 	console.log("Industry List error: ", err.response.status);
	// 	if (err.response) {
	// 		switch (err.response.status) {
	// 			case 401:
	// 				console.log("unauthorized user");
	// 				window.location.replace("../index.html#/");
	// 				break;
	// 			default:
	// 				break;
	// 		}
	// 	}
	// }
}

// Sales User Location Regions
export async function getCitiesList(role: string, token: string) {
	console.log("API URL", apiURL)
	const url = `${apiURL.businessLocation}/city-list`
	return await internalGet([], url, token)
	// try {

	// } catch (err) {
	// 	console.log("Industry List error: ", err.response.status);
	// 	if (err.response) {
	// 		switch (err.response.status) {
	// 			case 401:
	// 				console.log("unauthorized user");
	// 				window.location.replace("../index.html#/");
	// 				break;
	// 			default:
	// 				break;
	// 		}
	// 	}
	// }
}

// Sales User Location Brgys
export async function getBrgyList(role: string, token: string) {
	console.log("API URL", apiURL)
	const url = `${apiURL.businessLocation}/barangay-list`
	return await internalGet([], url, token)
	// try {
	// 	return await internalGet([], url, token);
	// } catch (err) {
	// 	console.log("Industry List error: ", err.response.status);
	// 	if (err.response) {
	// 		switch (err.response.status) {
	// 			case 401:
	// 				console.log("unauthorized user");
	// 				window.location.replace("../index.html#/");
	// 				break;
	// 			default:
	// 				break;
	// 		}
	// 	}
	// }
}
// Get Barangay List
export async function getBarangayList(role:string, token: string, city: string, region: string, province: string) {
	const pathName = `barangay-list?filter={"where":{"city":"${city}","region":"${region}","province":"${province}"}}`;
	const url =  `${apiURL.businessLocation}/${pathName}`;
	console.log(`url: ${url}`)
	return await internalGet([], url, token)
}

// Create Request
// Sales User
export async function createFranchiseService(obj: Franchise, token: string) {
	const url = `/franchise`
	return await internalPost(obj, url, token)
}

// Save Request
// Sales User
export async function saveFranchiseService(obj: Franchise, token: string) {
	const url = `/franchise`
	return await internalPut(obj, url, token)
}

// Upload Files
// Sales User
export async function uploadFranchiseFileService(
	formData: FormData,
	config: any,
	from: String
) {
	const url = `/upload${from ? `?from=${from}` : ''}`
	return await internalUploadFile({} as UploadResponse, formData, url, config)
}

// Delete Files
export async function deleteFranchiseFileService(id: string, token: string) {
	const url = `/upload/${id}`
	return await internalDelete(url, token)
}

// Download Files
export async function downloadFranchiseFileService(id: string, token: string) {
	const url = `/upload/${id}/download`
	return await FILE_DOWNLOAD(url, token)
}

// Download PDF File
export async function downloadReportService(id: string, token: string) {
	const url = `/upload/${id}/download`
	return await FILE_DOWNLOAD(url, token)
}

export async function updateFranchiseStatus(
	id: string,
	role: string,
	action: string,
	values: any,
	token: string,
) {
	const url = `/franchise/${role}/${id}/${action}`
	return await internalPatch({} as Franchise, values, url, token)
}

export async function submitFranchise(values: any, token: string) {
	const url = `/franchise/bulk-submit`
	return await internalPut(values, url, token)
}

export async function updateFranchise(id: string, values: any, token: string) {
	const url = `/franchise/${id}`
	return await internalPatch({} as Franchise, values, url, token)
}

export async function updateRequestResubmitted(
	id: string,
	values: any,
	token: string,
) {
	const url = `/franchise/encoder/${id}/notyetreceived`
	return await internalPatch({} as Franchise, values, url, token)
}

export async function validateFranchiseInput(values: any, token: string) {
	const url = `/franchise/validate-client-id`
	return await internalPost(values, url, token)
}

export async function validateFranchiseInpuTIN(values: any, token: string) {
	const url = `/franchise/validate-tin-id`
	return await internalPost(values, url, token)
}

export async function updateDueDate(id: string, values: any, token: string) {
	const url = `/franchise/validator/${id}/duedate`
	return await internalPatch({} as Franchise, values, url, token)
}

export async function updateExpiryDate(id: string, values: any, token: string) {
	const url = `/franchise/manager/${id}/expirydate`
	return await internalPatch({} as Franchise, values, url, token)
}

// Sales User Mother Company List Lookup
export async function getMotherCompanyList(role: string, token: string) {
	const url = `/franchise/${role}/mother-companies`
	return await internalGet([] as MotherCompanyList[], url, token)
	// try {
	// 	return await internalGet([] as MotherCompanyList[], url, token);
	// } catch (err) {
	// 	console.log("Mother Company List error: ", err.response.status);
	// 	if (err.response) {
	// 		switch (err.response.status) {
	// 			case 401:
	// 				console.log("unauthorized user");
	// 				window.location.replace("../index.html#/");
	// 				break;
	// 			default:
	// 				break;
	// 		}
	// 	}
	// }
}

// submit refranchise request
export async function submitRefranchise(id: string, values: any, token: any) {
	const url = `/franchise/refranchise/submit/${id}`
	return await internalPost(values, url, token)
}
