import * as crypto from 'crypto';

const fieldsToEncrypt = [
    'first_name',
    'last_name',
    'middle_name',
    'username',
    'user_name',
    'full_name',
    'email'
];

// Derive encryption key from hashing password provided in environment variable
const key = crypto
  .createHash('sha256')
  .update(String(process.env.REACT_APP_HASHING_PASSWORD))
  .digest('base64')
  .substring(0, 32);

// Algorithm used for encryption
const cipherAlgorithm = 'aes-256-gcm';

/**
 * Encrypts the provided text.
 * @param text The text to be encrypted.
 * @returns The encrypted text along with initialization vector and authentication tag.
 */
const encryptCredentials = (text: any): string => {
    const initVector = crypto.randomBytes(16);
    const initVectorHex = initVector.toString('hex');
    const cipher = crypto.createCipheriv(cipherAlgorithm, key, initVector);
    const encoded = cipher.update(text, 'utf8', 'hex') + cipher.final('hex');
    const authTag = cipher.getAuthTag().toString('hex');
    const metaAndEncoded = [authTag, initVectorHex, encoded].join('|');
    return metaAndEncoded;
  };
  
  
  /**
   * Decrypts the provided text.
   * @param text The text to be decrypted.
   * @returns The decrypted text.
   */
  const decryptCredentials = (text: any): string => {
    const [authTag, initVectorHex, encrypted] = text.split('|');
    const initVector = Buffer.from(initVectorHex, 'hex');
    const decipher = crypto.createDecipheriv(cipherAlgorithm, key, initVector);
    decipher.setAuthTag(Buffer.from(authTag, 'hex'));
  
  
    let decrypted = decipher
      .update(Buffer.from(encrypted, 'hex'))
      .toString('utf-8');
    decrypted += decipher.final().toString('utf-8');
  
  
    return decrypted;
  };
  
  
  /**
   * Encrypts sensitive fields of a user object.
   * @param user The user object containing sensitive fields.
   * @returns The user object with sensitive fields encrypted.
   */
  const encryptUsers = (user: any): any => {
    const encryptedUsers = { ...user };
    fieldsToEncrypt.forEach(field => {
      if (encryptedUsers[field]) {
        encryptedUsers[field] = encryptCredentials(encryptedUsers[field]);
      }
    });
    return encryptedUsers;
  };
  
  
  /**
   * Decrypts sensitive fields of a user object.
   * @param user The user object with sensitive fields encrypted.
   * @returns The user object with sensitive fields decrypted.
   */
  const decryptUsers = (user: any): any => {
    const decryptedUsers = { ...user };
    fieldsToEncrypt.forEach(field => {
      if (decryptedUsers[field]) {
        decryptedUsers[field] = decryptCredentials(decryptedUsers[field]);
      }
    });
    return decryptedUsers;
  };
  
  export {
    // decryptKeyPairData,
    decryptCredentials,
    encryptCredentials,
    fieldsToEncrypt,
    decryptUsers,
    encryptUsers,
  };