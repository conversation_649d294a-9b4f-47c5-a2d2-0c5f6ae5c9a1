import { Reason, Remark } from "../shared/models/Franchise"
import { IUserRole } from "../shared/models/User";

export const getReasonRemarkForReport = (status: string,reasons: Reason[],remarks:Remark[], userRole: IUserRole, preview:boolean) => {
	const {isSalesUser} = userRole;
	let reason = reasons.filter((content: Reason,i: number) => {
		let valid = false;
		let actualRole = getFranchisingRole(content.roles)
		switch (status) {
			case "RETURNED":
				valid = (content.main_role === "FRF_VALIDATOR" || actualRole === "FRF_VALIDATOR") && content.type === "RETURN";
				break;
			case "RETURNED_TO_VALIDATOR":
				if(preview){
					valid = (content.main_role === "FRF_INITIAL_APPROVER" || actualRole === "FRF_INITIAL_APPROVER") && content.type === "RETURN";
				}else{
					valid = !isSalesUser && (content.main_role === "FRF_INITIAL_APPROVER" || actualRole === "FRF_INITIAL_APPROVER") && content.type === "RETURN";
				}
				break;
			case "DISAPPROVED":
				valid = (content.main_role === "FRF_FINAL_APPROVER" || content.main_role === "FRF_INITIAL_APPROVER" || actualRole === "FRF_FINAL_APPROVER" || actualRole === "FRF_INITIAL_APPROVER") && content.type === "DISAPPROVE";
				break;
			default:
				break;
		}
		return valid;
	}).map((content: Reason) => { 
		let {reason,remark} = content
		let {message} = remark??{message:"--"}
		return  {
		  reason:reason??"--",
		  remark:message??"--",
		}
	})

	let remark = remarks.filter((content: Remark,i: number) => {
		let valid = false;
		let actualRole = getFranchisingRole(content.roles)
		switch (status) {
			case "FOR_APPROVAL":
				if(preview){
					valid = content.main_role === "FRF_VALIDATOR" || actualRole === "FRF_VALIDATOR";
				}else{
					valid = !isSalesUser && (content.main_role === "FRF_VALIDATOR" || actualRole === "FRF_VALIDATOR");
				}
				break;
			case "FINAL_APPROVAL":
				if(preview){
					valid = content.main_role === "FRF_INITIAL_APPROVER" || actualRole === "FRF_INITIAL_APPROVER";
				}else{
					valid = !isSalesUser && (content.main_role === "FRF_INITIAL_APPROVER" || actualRole === "FRF_INITIAL_APPROVER");
				}
				break;
			case "APPROVED":
				valid = content.main_role === "FRF_FINAL_APPROVER" || actualRole === "FRF_FINAL_APPROVER";
				break;
			default:
				break;
		}
		return valid;
	}).map((content: Remark) => { 
		let {message} = content
		return  {
		  reason:"--",
		  remark:message??"--",
		}
	})

	if(reason.length !== 0){
		return reason[reason.length - 1];
	}else if(remark.length !== 0){
		return remark[remark.length - 1];
	}else{
		return {
			reason:"--",
			remark:"--",
		};
	}
}

export const getReasonRemarkForFlist = (status: string,reasons: Reason[],remarks:Remark[], userRole: IUserRole) => {
	let reason = reasons.filter((content: Reason,i: number) => {
		let valid = false;
		let actualRole = getFranchisingRole(content.roles)
		switch (status) {
			case "RESUBMITTED":
				valid = (content.main_role === "FRF_VALIDATOR" || actualRole === "FRF_VALIDATOR") && content.type === "RETURN";
				break;
			case "RETURNED":
				valid = (content.main_role === "FRF_VALIDATOR" || actualRole === "FRF_VALIDATOR") && content.type === "RETURN";
				break;
			case "RETURNED_TO_VALIDATOR":
				valid = !userRole.isSalesUser ? (content.main_role === "FRF_INITIAL_APPROVER" || actualRole === "FRF_INITIAL_APPROVER") && content.type === "RETURN" : false;
				break;
			case "DISAPPROVED":
				valid = (content.main_role === "FRF_FINAL_APPROVER" || content.main_role === "FRF_INITIAL_APPROVER" || actualRole === "FRF_FINAL_APPROVER" || actualRole === "FRF_INITIAL_APPROVER") && content.type === "DISAPPROVE"
				break;
			case "RECEIVED":
				valid = userRole.isSupervisor && ((content.main_role === "FRF_VALIDATOR" || content.main_role === "FRF_INITIAL_APPROVER" || actualRole === "FRF_VALIDATOR" || actualRole === "FRF_INITIAL_APPROVER") && (content.type === "RETURN"))
				break;
			case "VALIDATED":
				valid = userRole.isSupervisor && ((content.main_role === "FRF_VALIDATOR" || content.main_role === "FRF_INITIAL_APPROVER" || actualRole === "FRF_VALIDATOR" || actualRole === "FRF_INITIAL_APPROVER") && (content.type === "RETURN"))
				break;

			default:
				break;
		}
		return valid;
	}).map((content: Reason) => { 
		let {reason,remark} = content
		let {message} = remark??{message:"--"}

		return  {
		  reason:reason??"--",
		  remark:message??"--",
		}
	})

	let remark = remarks.filter((content: Remark,i: number) => {
		let valid = false;
		let actualRole = getFranchisingRole(content.roles)
		switch (status) {
			case "RESUBMITTED_TO_SUPERVISOR":
				valid = !userRole.isSalesUser ? (content.main_role === "FRF_VALIDATOR" || actualRole === "FRF_VALIDATOR") : false;
				break;
			case "FOR_APPROVAL":
				valid = !userRole.isSalesUser ? (content.main_role === "FRF_VALIDATOR" || actualRole === "FRF_VALIDATOR") : false;
				break;
			case "FINAL_APPROVAL":
				valid = !userRole.isSalesUser ? (content.main_role === "FRF_INITIAL_APPROVER" || actualRole === "FRF_INITIAL_APPROVER") : false;
				break;
			case "APPROVED":
				valid = content.main_role === "FRF_FINAL_APPROVER" || actualRole === "FRF_FINAL_APPROVER"
				break;
			default:
				break;
		}
		return valid;
	}).map((content: Remark) => { 
		let {message} = content
		return  {
		  reason:"--",
		  remark:message??"--",
		}
	})

	if(reason.length !== 0){
		if (!userRole.isSalesUser){
			return reason[reason.length - 1];
		}else{
			if (status === "RECEIVED" 
			|| status === "VALIDATED"
			|| status === "RESUBMITTED_TO_SUPERVISOR" 
			|| status === "FOR_APPROVAL" 
			|| status === "APPROVAL_IN_PROCESS" 
			|| status === "FINAL_APPROVAL" 
			){
				return {
					reason:reason[reason.length - 1].reason,
					remark:"--",
				};
			}else if (status === "RESUBMITTED"){
				return {
					reason:"--",
					remark:"--",
				};
			}else{
				return reason[reason.length - 1];
			}
		}
	}else if(remark.length !== 0){
			return remark[remark.length - 1];	
	}else{
		return {
			reason:"--",
			remark:"--",
		};
	}
}

export const remarksForViewFranchiseModal = (reasons: Reason[],remarks:Remark[], userRole: string) => {
	// const {isSalesUser} = userRole;
	let reasonWithRemark: any = []
    if(userRole === 'encoder'){
        if (reasons && reasons.length > 0){
            reasonWithRemark = reasons.filter((data: Reason) => {
				let valid = false;
                if (data.remark){
				let actualRole = getFranchisingRole(data.remark.roles)
                    switch (data.type){
                        case "RETURN":
                            valid = data.remark.main_role === "FRF_VALIDATOR" || actualRole === "FRF_VALIDATOR" 
                            break;
                        case "DISAPPROVE":
                            valid = data.remark.main_role === "FRF_INITIAL_APPROVER" || data.remark.main_role === "FRF_FINAL_APPROVER" || actualRole === "FRF_INITIAL_APPROVER" || actualRole === "FRF_FINAL_APPROVER"
                            break;
                    }
                }
                return valid
            })
            reasonWithRemark = reasonWithRemark.map((content: Reason) => { 
                let {remark} = content
                return remark
            })
        }
    }else{
        if (reasons && reasons.length > 0){
            reasonWithRemark = reasons.map((content: Reason) => { 
                let {remark} = content
                return remark
            })
            reasonWithRemark = reasonWithRemark.filter((data: any) => {
                return data !== undefined
            })
        }
    } 

    let rawRemarks: any = []

    if(userRole === 'encoder'){
        rawRemarks = remarks?.filter((data: any) => {
			let actualRole = getFranchisingRole(data.roles)
            return data && (data.main_role === "FRF_FINAL_APPROVER" || actualRole === "FRF_FINAL_APPROVER")
        })
    }else{
        rawRemarks = remarks
    }
    
    return rawRemarks && rawRemarks.length !== 0 ? rawRemarks.concat(reasonWithRemark) : reasonWithRemark
}

export const getFranchisingRole = (roles: any[]) => {
	const role = roles?.filter(content => content.module === "Franchising") || [];
	return role.length > 0 ? role[0].name : "";
}