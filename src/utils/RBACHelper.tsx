/**
 * Check if user has role
 * @param array userRoles
 * @param string role
 * @return boolean
 * <AUTHOR>
 */
export function hasRole(userRoles: any,role: string): boolean {
	try {
		const result = userRoles.find((r:any) => r.name === role);
		return result !== undefined;
	} catch (error) {
		console.log('hasRole error',error,typeof error)
		return false;
	}
}

/**
 * Check if user has permission
 * @param array rbac
 * @param string action
 * @return boolean
 * <AUTHOR>
 */
export function hasPermission(rbac: any,id: string) : boolean {
	try {
		const result = rbac.find((r:any) => r.policy_id === id && r.module === "Franchising");
		return result !== undefined;
	} catch (error) {
		console.log('hasPermission error',error,typeof error)
		return false;
	}
}