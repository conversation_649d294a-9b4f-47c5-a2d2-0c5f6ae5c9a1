import IndexedDBHelper,{ IndexedDBHelperResponse } from "./IndexedDBHelper";
import axios from 'axios'
import { apiURL } from "./Environment";

const _indexedDb = new IndexedDBHelper();

axios.defaults.baseURL = apiURL.franchising
axios.defaults.headers.post['Content-Type'] = 'application/json';

export async function externalPost(values: any, extUrl: string) {
    console.log("MY URL", axios.defaults.baseURL, values, extUrl);
    return await axios.post(extUrl, values)
}


export function internalPost(values: any, url: string, token: string) {
    return axios.post(url, values, {
        headers: {'Authorization': `Bearer ${token}`}
    })
}

export function internalPut( values: any, url: string, token: string) {
    return axios.put(url, values, {
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-type': 'application/json'
        }
    })
}

export function internalDelete( url: string, token: string) {
    return axios.delete(url, {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
}

export function internalPatch(Response: any, values: any, url: string, token: string) {
    return axios.patch(url, values, {
        headers: {'Authorization': `Bearer ${token}`}
    })
}

export function internalGet( Response: any, url: string, token: string) {
    return axios.get<Response>(url,{
        headers: {'Authorization': `Bearer ${token}` },
    })  
}

export async function internalUploadFile( Response: any,formData: any, url: string, config: any) {
    return await axios.post<Response>(url, formData, config)
}

export async function FILE_DOWNLOAD(url: string, token: string){
    try {
        return await axios.get(url, {
            responseType: 'blob',
            headers: {'Authorization': `Bearer ${token}` }
        });
    } catch (error) {
        console.log("error FILE_DOWNLOAD", error)
    }
}


export function getIndexDb (storeName: string, keyPath?: string){
    return _indexedDb.openDb('himsDb').then((db: IndexedDBHelperResponse) => {

        if (!keyPath) {
            return _indexedDb.getAll(db.result, storeName);
        } else {
            return _indexedDb.getByKeyReturnValue(db.result, storeName, keyPath);
        }
    })
}

export function logoutService() {
    getIndexDb('user_data', 'access_token').then((res: any) => {
        console.log(res, 'token');
        const url = `${apiURL.userManagement}//logout`;
        console.log('url', url);
        externalPost(
            {
                method: 'post',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({"access_token": res.result})
            },
            url,
        )
        .then(response => {
              return response;
        })
        .then(resJSON => {
              if (resJSON !== undefined) {
                  localStorage.removeItem('api_token');
                  localStorage.removeItem('pm_token');
                  localStorage.removeItem('user_id');
                  _indexedDb.openDb('himsDb').then((db: IndexedDBHelperResponse) => {
                      _indexedDb.clearStore(db.result, 'user_data')
                      window.location.replace("../index.html#/"); //redirect url
                  });
              } else {
                  alert(resJSON);
              }
          }).catch(error => {
              console.log(error, "error");
              localStorage.removeItem('api_token');
              localStorage.removeItem('pm_token');
              localStorage.removeItem('user_id');
              _indexedDb.openDb('himsDb').then((db: IndexedDBHelperResponse) => {
                _indexedDb.clearStore(db.result, 'user_data')
                window.location.replace("../index.html#/"); //redirect url
            });
          });
    })
}

export async function authService(token: string) {
    try {
        console.log(token, 'token');
        const url = `${apiURL.userManagement}/login/${token}`;
        return await internalGet({},url,token);
    } catch (error) {
        console.log("error authService", error)
    }
   
}

export function updateProfileImageIndexDb (newValue: any){
    _indexedDb.openDb('himsDb').then((res: any) => {
        return _indexedDb.updateSinglePropertyByKey(res.result, 'user_data', 'profile_pic', newValue)
    })
}



