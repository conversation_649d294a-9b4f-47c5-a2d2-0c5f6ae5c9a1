//eslint-disable-next-line
export const validEmailRegex = RegExp(/^(([^<>()\[\]\.,;:\s@\"]+(\.[^<>()\[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i);

export const convertAddressRegionName = (str: string) => {
	if(str && str.includes("_")){
		// Upper Case First Letter
		return str.charAt(0).toUpperCase() + str.replace("_"," ").slice(1);
	}else if (str){
		return str.toUpperCase();
	}
	return str;
}

// converting date format
export const formatDate = (date: Date) => {
	const getDate = date.getDate();
	const getMonth = date.getMonth() + 1;
	const getYear = date.getFullYear();

	let newDate: string;
	let newMonth: string;

	if (getDate < 10) {
		newDate = "0" + getDate
	} else {
		newDate = getDate.toString();
	}

	if (getMonth < 10) {
		newMonth = "0" + getMonth;
	} else {
		newMonth = getMonth.toString();
	}
	return `${newMonth}/${newDate}/${getYear}`;
}

// converting time format
export const formatTime = (date: Date) => {
	let hours = date.getHours();
	let min = date.getMinutes();
	let seconds = date.getSeconds();
	let ampm = (hours >= 12) ? "PM" : "AM";

	hours = hours % 12;
	hours = hours ? hours : 12; // the hour '0' should be '12'

	let newMin: any;
	newMin = (min < 10) ? '0' + min : min;

	var strTime = `${hours}:${newMin}:${seconds} ${ampm}`;
	return strTime;
}

// converting tax identification number
export const formatTIN = (text: string) => {
	const slicedText: string[] = [];
	slicedText.push(text.slice(0, 3));
	slicedText.push(text.slice(3, 6));
	slicedText.push(text.slice(6, 9));
	slicedText.push(text.slice(9, 12));
	const splitValue = slicedText.filter(text => text !== '');
	return splitValue ? splitValue.join('-') : '';
}

// converting client id number
export const formatClientID = (text: string) => {
	const slicedText: string[] = [];
	slicedText.push(text.slice(0, 2));
	slicedText.push(text.slice(2, 4));
	slicedText.push(text.slice(4, 9));
	return slicedText.filter(text => text !== '');
}