import { makeStyles } from '@mui/styles'
export interface columnType {
	name: string;
	title?: string;
	sortEnabled?: boolean;
}

export const tableColumn = [
	{
		name: 'code',
		title: 'Code'
	},
	{
		name: 'name',
		title: 'Name'
	},
	{
		name: 'employee_code',
		title: 'Employee Code',
		sortEnabled: false
	},
	{
		name: 'type',
		title: 'Type',
	},
	{
		name: 'contact_person',
		title: 'Broker/Agent',
	},
	{
		name: 'contact_number',
		title: 'Contact Number',
		style: makeStyles({
			style: {
				'& div': {   
					minWidth: 120
				}  
			}
		})
	},
	{
		name: 'area',
		title: 'Area',
		sortEnabled: false
	},
	{
		name: 'tax_type',
		title: 'Tax Type',
		sortEnabled: false
	},
	{
		name: 'tin',
		title: 'TIN',
		sortEnabled: false,
		style: makeStyles({
			style: {
				'& div': {  
					minWidth: 120
				}  
			}
		})
	},
	{
		name: 'evat_number',
		title: 'EVAT No',
		sortEnabled: false
	},
	{
		name: 'input_tax',
		title: 'Input Tax',
		sortEnabled: false
	},
	{
		name: 'mdpa_id',
		title: 'MDPA identifier',
		sortEnabled: false
	},
	{
		name: 'address',
		title: 'Address',
		style: makeStyles({
			style: {
				'& div': {  
					minWidth: 260
				}  
			}
		})
	},
	{
		name: 'status',
		title: 'Status',
		sortEnabled: false
	},
	{
		name: 'action',
		title: 'Action',
		sortEnabled: false
	}
] as columnType[];

export interface Contact {
	_id: string;
	code: string;
	name: string;
	employee_code: string;
	type: string;
	contact_person: string;
	contact_number: string;
	email_address: string;
	area: string;
	tin: string;
	tax_type: string;
	evat_number: string;
	mdpa_id: string;
	input_tax: string;
	business_address: Address;
	status: string;
}

export interface IErrorAttributes {
	valid: boolean;
	inputClass: string;
	text: string;
	spanClass: string;
}

export interface IErrorNameTypeObj  {
	name: IErrorAttributes;
	type: IErrorAttributes;
};

const emptyErrorAttributes = {
	valid: false,
	inputClass: "",
	text: "",
	spanClass: ""
} as IErrorAttributes;

const validErrorAttributes = {
	valid: true,
	inputClass: "",
	text: "",
	spanClass: ""
} as IErrorAttributes;

export interface InputValidStatus {
	isCodeValid: IErrorAttributes,
	isTypeValid: IErrorAttributes,
	isEmployeeCodeValid: IErrorAttributes,
	isNameValid: IErrorAttributes,
	isContactPersonValid: IErrorAttributes,
	isContactNumberValid: IErrorAttributes,
	isEmailAddressValid: IErrorAttributes,
	isTinValid: IErrorAttributes,
	isAreaValid: IErrorAttributes,
	isTaxTypeValid: IErrorAttributes,
	isEvatNumberValid: IErrorAttributes,
	isMdpaIdValid: IErrorAttributes,
	isInputTaxValid: IErrorAttributes,
	isFloorValid : IErrorAttributes;
	isUnitValid: IErrorAttributes;
	isBldgNameValid : IErrorAttributes;
	isStreetValid : IErrorAttributes;
	isBrgyValid: IErrorAttributes;
	isCityValid : IErrorAttributes;
	isProvinceValid : IErrorAttributes;
	isZipCodeValid : IErrorAttributes;
}

export const emptyInputValidStatus = {
	isCodeValid: emptyErrorAttributes,
	isTypeValid: emptyErrorAttributes,
	isEmployeeCodeValid: validErrorAttributes,
	isNameValid: emptyErrorAttributes,
	isContactPersonValid: emptyErrorAttributes,
	isContactNumberValid: emptyErrorAttributes,
	isEmailAddressValid: emptyErrorAttributes,
	isTinValid: emptyErrorAttributes,
	isAreaValid: emptyErrorAttributes,
	isTaxTypeValid: emptyErrorAttributes,
	isEvatNumberValid: validErrorAttributes,
	isMdpaIdValid: validErrorAttributes,
	isInputTaxValid: validErrorAttributes,
	isFloorValid : validErrorAttributes,
	isUnitValid: validErrorAttributes,
	isBldgNameValid : emptyErrorAttributes,
	isStreetValid : emptyErrorAttributes,
	isBrgyValid: emptyErrorAttributes,
	isCityValid : emptyErrorAttributes,
	isProvinceValid : emptyErrorAttributes,
	isZipCodeValid : validErrorAttributes,
} as InputValidStatus;

export const contact = {
	_id: "",
	code: "",
	name: "",
	employee_code: "",
	type: "",
	contact_person: "",
	contact_number: "",
	email_address: "",
	area: "",
	tin: "",
	tax_type: "",
	evat_number: "",
	mdpa_id: "",
	input_tax: "",
	business_address: {
		floor: "",
		unit: "",
		bldg_name: "",
		street: "",
		brgy: "",
		city: "",
		province: "",
		country: "",
		zip_code: "",
	} as Address,
	status: "",
} as Contact;

export interface columnExtensions {
	columnName: string;
	wordWrapEnabled?: boolean;
	width?: number;
}

export interface columnType {
	name: string;
	title?: string;
	sortEnabled?: boolean;
}

export interface Address {
    floor: string;
    unit: string;
    bldg_name: string;
    street: string;
    brgy: string;
    city: string;
    province: string;
	country: string;
    zip_code: string;
}