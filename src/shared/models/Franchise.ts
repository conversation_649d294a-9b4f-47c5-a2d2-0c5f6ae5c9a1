export interface Franchise {
    [x: string]: any;
	// Client
	client_id?:string;
	frf_id: number;
	_id: string;
	user_id: string;
	status: string;
	entry_id: string;
	// company_id: string;
	channel_of_request: string;
	is_refranchise: string;
	old_account_name: string;
	corporate_name: string;
	is_mother_company: boolean;
	brand_name: string;
	company_affiliates_id: string;
	company_affiliates: string;
	business_address: Address;
	tax_number: string;
	company_number: string;
	industry_class_id: string;
	authorized_contact_persons:any[];
	// Franchisee
	applicant_id: string;
	applicant_name: string;
	applicant_contact_person: string;
	applicant_business_address: string;
	applicant_contact_number: string;
	applicant_email_address: string;
	sales_first_name: string;
	sales_last_name: string;
	// KYC
	terms: string;
	no_of_employee: string;
	no_of_dependents: string;
	provider_name: string;
	provider_effectivity_date_to?: string;
	provider_effectivity_date_from?: string;
	is_voluntary_enrollees: boolean;
	company_paid_shared: string;
	stakeholders: any[];
	signatories: any[];
	source_of_funds: string;
	supporting_docs: any[];
	remarks?: Remark[];
	account_status: string;
	proposal_status: string;
	reason: string;
	reasons?: Reason[];
	resubmit_remarks: string;
	date_time_received: string;

	// relative params
	isSaved?: boolean;
	isReturned?: boolean;
	isSubmitted?: boolean;
	isReSubmitted?: boolean;
	isReceived?: boolean;
	isValidated?: boolean;
	isReturnedToValidator?: boolean;
	isEndorsed?: boolean;
	isReEndorsed?: boolean;
	isApprovalInProcess?: boolean;
	isForFinalApproval?: boolean;
	isFinalApprovalInProcess?: boolean;
	isApproved?: boolean;
	isDisApproved?: boolean;
	isCancelled?: boolean;
	isExpiring?: boolean;
	supporting_docs_obj?: any[];
	submitted_at: string;
	status_updated_at?: string;
	return_due_date?: string;
	validation_message?: string;
	effectivity_date?: string;
	expiry_date?: string;
	sales_user_id:string;
	company_logo: string;
	supervisor_pm_user?:string;
	duplicate?:Franchise[];
	appointment_letter: any[],
	isExpired?: boolean
}

export interface SupportingDocs {
	id: string;
	filename: string;
}

export interface IFileItem {
	objectStatus?: string;
	id?: string;
	icon?: string;
	name?: string;
	fileStatus?: string;
	loading?: string;
	progress?: string;
	percentage?: number;
	errorClass?: string;
	errorMessage?: string;
	key?: any;
	handleClick?: (e: any) => any;
}

export interface CreateModalButton {
	text: string;
	// href: string;
	// rel: string;
	className: string;
	handleEvent?: (e: any) => any;
}

export interface CreateModal {
	id: string;
	title: string;
	p: string;
	button: CreateModalButton[]
}

export interface CreateMaterialModalButton {
	text: string;
	className: string;
	handleEvent?: (e: any) => any;
}

export interface CreateInitialModal {
	event: string;
	title: string;
	p: string;
	button1: CreateMaterialModalButton[];
	button2: CreateMaterialModalButton[];
}

export interface IErrorAttributes {
	valid: boolean;
	inputClass: string;
	text: string;
	spanClass: string;
}

export interface IErrorNameTypeObj  {
	name: IErrorAttributes;
	type: IErrorAttributes;
};

const emptyErrorAttributes = {
	valid: false,
	inputClass: "",
	text: "",
	spanClass: ""
} as IErrorAttributes;
const validErrorAttributes = {
	valid: true,
	inputClass: "",
	text: "",
	spanClass: ""
} as IErrorAttributes;

const emptyIErrorNameTypeObj = {
	name: emptyErrorAttributes,
	type: emptyErrorAttributes
} as IErrorNameTypeObj;



export interface InputValidStatus {
	isEntryNameValid: IErrorAttributes;
	isClientIdValid: IErrorAttributes;
	isChannelOfRequestValid: IErrorAttributes;
	isReFranchiseValid: IErrorAttributes;
	isOldAccountNameValid:IErrorAttributes;
	isCorporateNameValid: IErrorAttributes;
	isBrandNameValid: IErrorAttributes;
	isCompanyAffiliatesValid: IErrorAttributes;
	isTaxNumberValid: IErrorAttributes;
	isCompanyNumberValid: IErrorAttributes;
	isFloorValid : IErrorAttributes;
	isUnitValid: IErrorAttributes;
	isBldgNoValid : IErrorAttributes;
	isBldgNameValid : IErrorAttributes;
	isStreetValid : IErrorAttributes;
	isBrgyValid: IErrorAttributes;
	isCityValid : IErrorAttributes;
	isRegionValid : IErrorAttributes;
	isProvinceValid : IErrorAttributes;
	isZipCodeValid : IErrorAttributes;
	isIndustryClassIdValid: IErrorAttributes;
	isAuthorizedContactPersons: IErrorNameTypeObj[];
	isApplicantNameValid: IErrorAttributes;
	isApplicantContactPersonValid: IErrorAttributes;
	isApplicantBusinessAddress: IErrorAttributes;
	isApplicantContactNumber: IErrorAttributes;
	isApplicantEmailAddress: IErrorAttributes;
	isTermsValid: IErrorAttributes;
	isNoOfEmployeeValid: IErrorAttributes;
	isNoOfDependentsValid: IErrorAttributes;
	isProviderNameValid: IErrorAttributes;
	isProviderEfectivityDateToValid: IErrorAttributes;
	isProviderEfectivityDateFromValid: IErrorAttributes;
	isVoluntaryValid: IErrorAttributes;
	isCompanyPaidSharedValid: IErrorAttributes;
	isStakeholdersValid: IErrorNameTypeObj[];
	isSignatoriesValid: IErrorNameTypeObj[];
	isSourceOfFundsValid: IErrorAttributes;
	isDateAndTimeReceivedValid: IErrorAttributes;
}

export const emptyInputValidStatus = {
	isEntryNameValid: emptyErrorAttributes,
	isClientIdValid: validErrorAttributes,
	isOldAccountNameValid:validErrorAttributes,
	isChannelOfRequestValid: emptyErrorAttributes,
	isReFranchiseValid: emptyErrorAttributes,
	isCorporateNameValid: emptyErrorAttributes,
	isBrandNameValid: emptyErrorAttributes,
	isCompanyAffiliatesValid: validErrorAttributes,
	isTaxNumberValid: validErrorAttributes,
	isCompanyNumberValid: emptyErrorAttributes,
	isFloorValid : emptyErrorAttributes,
	isUnitValid: emptyErrorAttributes,
	isBldgNoValid : emptyErrorAttributes,
	isBldgNameValid : emptyErrorAttributes,
	isStreetValid : emptyErrorAttributes,
	isBrgyValid: emptyErrorAttributes,
	isCityValid : emptyErrorAttributes,
	isRegionValid : emptyErrorAttributes,
	isProvinceValid : emptyErrorAttributes,
	isZipCodeValid : emptyErrorAttributes,
	isIndustryClassIdValid: emptyErrorAttributes,
	isAuthorizedContactPersons: [emptyIErrorNameTypeObj],
	isApplicantNameValid: emptyErrorAttributes,
	isApplicantContactPersonValid: emptyErrorAttributes,
	isApplicantBusinessAddress: emptyErrorAttributes,
	isApplicantContactNumber: emptyErrorAttributes,
	isApplicantEmailAddress: emptyErrorAttributes,
	isTermsValid: emptyErrorAttributes,
	isNoOfEmployeeValid: emptyErrorAttributes,
	isNoOfDependentsValid: emptyErrorAttributes,
	isProviderNameValid: emptyErrorAttributes,
	isProviderEfectivityDateToValid: validErrorAttributes,
	isProviderEfectivityDateFromValid: validErrorAttributes,
	isVoluntaryValid: emptyErrorAttributes,
	isCompanyPaidSharedValid: emptyErrorAttributes,
	isStakeholdersValid: [emptyIErrorNameTypeObj],
	isSignatoriesValid: [emptyIErrorNameTypeObj],
	isSourceOfFundsValid: emptyErrorAttributes,
	isDateAndTimeReceivedValid: emptyErrorAttributes,
} as InputValidStatus;

export const franchise = {
	frf_id: 0,
	_id: "",
	user_id: "",
	entry_id: "",
	client_id:"",
	// company_id: "",
	channel_of_request: "",
	is_refranchise: "",
	old_account_name:"",
	corporate_name: "",
	is_mother_company: true,
	brand_name: "",
	company_affiliates_id: "",
	company_affiliates: "",
	business_address: {
		floor: "",
		unit: "",
		bldg_no: "",
		bldg_name: "",
		street: "",
		brgy: "",
		city: "",
		province: "",
		region: "",
		zip_code: "",
	} as Address,
	tax_number: "",
	company_number: "",
	industry_class_id: "",
	authorized_contact_persons: [{name:"",type:""}],
	applicant_id: "",
	applicant_name: "",
	applicant_contact_person: "",
	applicant_business_address: "",
	applicant_contact_number: "",
	applicant_email_address: "",
	terms: "",
	no_of_employee: "",
	no_of_dependents: "",
	provider_name: "",
	provider_effectivity_date_to: "",
	provider_effectivity_date_from: "",
	is_voluntary_enrollees: false,
	company_paid_shared: "",
	stakeholders: [{name:"",type:""}],
	signatories: [{name:"",type:""}],
	submitted_at:"",
	source_of_funds: "",
	status: "",
	supporting_docs: [],
	remarks: [],
	account_status: "",
	proposal_status: "",
	reason: "",
	reasons: [],
	resubmit_remarks: "",
	sales_first_name: "",
	sales_last_name: "",
	status_updated_at: "",
	return_due_date: "",
	sales_user_id:"",
	company_logo:"",
	date_time_received:"",
	appointment_letter: []
} as Franchise;

export const salesUserStatuses = [
	"Total Requests",
	"Saved Requests",
	"Submitted",
	"Returned",
	"Expiring Returned Franchise",
	"Expired Returned Franchise",
	"Received",
	"Validated",
	"For Approval",
	"Approved",
	"Disapproved",
	"Expiring Franchise",
	"Expired Franchise"
];

export const specialistUserTileStatuses = [
	"Total Requests",
	"Submitted Requests",
	"Validation In-Process",
	"Validation Complete",
	"Returned",
	"Returned to Sales User",
	"For Approval",
	"Approved",
	"Disapproved",
	"Expiring Franchise",
	"Expired Franchise",
];

export const supervisorUserTileStatuses = [
	"Total Requests",
	"For Approval",
	"Approval In Process",
	"Returned",
	"For Final Approval",
	"Approved",
	"Disapproved",
	"Expiring Franchise",
	"Expired Franchise",
];

export const managerUserTileStatuses = [
	"Total Requests",
	"For Approval",
	"Approval In Process",
	"Approved",
	"Disapproved",
	"Expiring Franchise",
	"Expired Franchise",
];

export interface columnExtensions {
	columnName: string;
	wordWrapEnabled?: boolean;
	width?: number;
}

export interface columnType {
	name: string;
	title?: string;
	sortEnabled?: boolean;
}

export const tableColumnExtensionsDashboardSalesUser = [
	{
		columnName: 'frf_no',
		wordWrapEnabled: false
	}, {
		columnName: 'reg_corp_name',
		wordWrapEnabled: false,
	}, {
		columnName: 'status',
		wordWrapEnabled: false
	}, {
		columnName: 'client_id',
		wordWrapEnabled: false
	}, {
		columnName: 'reason',
		wordWrapEnabled: false
	}, {
		columnName: 'remarks',
		wordWrapEnabled: false,
		// maxWidth: 600
	}, {
		columnName: 'date',
		wordWrapEnabled: false
	}, {
		columnName: 'time',
		wordWrapEnabled: false
	}, {
		columnName: 'action'
	}
] as columnExtensions[];

export const tableColumnExtensionsDashboardHigherRoles = [
	{
		columnName: 'frf_no',
		wordWrapEnabled: false,
	}, {
		columnName: 'reg_corp_name',
		wordWrapEnabled: false,
	}, {
		columnName: 'brand_name',
		wordWrapEnabled: false,
	}, {
		columnName: 'status_update',
		wordWrapEnabled: false,
	}, {
		columnName: 'client_id',
		wordWrapEnabled: false
	}, {
		columnName: 'franchisee_name',
		wordWrapEnabled: false,
	}, {
		columnName: 'tin_number',
		wordWrapEnabled: false,
	}, {
		columnName: 'reason',
		wordWrapEnabled: false
	}, {
		columnName: 'remarks',
		wordWrapEnabled: false,
		// maxWidth: 600
	}, {
		columnName: 'date',
		wordWrapEnabled: false
	}, {
		columnName: 'time',
		wordWrapEnabled: false
	}, {
		columnName: 'action',
	}
] as columnExtensions[];

export const tableColumnDashboardSalesUser = [
	{
		name: 'frf_no',
		title: 'FRF No.'
	}, {
		name: 'reg_corp_name',
		title: 'Registered Corporate Name'
	}, {
		name: 'status',
		title: 'Status Update',
		sortEnabled: false
	}, {
		name: 'client_id',
		title: 'Client ID',
		sortEnabled: false
	}, {
		name: 'reason',
		title: 'Reason',
		sortEnabled: false
	}, {
		name: 'remarks',
		title: 'Remarks',
		sortEnabled: false
	}, {
		name: 'date',
		title: 'Date',
	}, {
		name: 'time',
		title: 'Time',
	}, {
		name: 'action',
		title: 'Action',
		sortEnabled: false
	}
] as columnType[];

export const tableColumnDashboardHigherRoles = [
	{
		name: 'frf_no',
		title: 'FRF No.'
	},
	{
		name: 'reg_corp_name',
		title: 'Registered Corporate Name'
	},
	{
		name: 'brand_name',
		title: 'Trade/Brand Name',
	},
	{
		name: 'status_update',
		title: 'Status Update',
		sortEnabled: false
	},
	{
		name: 'client_id',
		title: 'Client ID',
		sortEnabled: false
	},
	{
		name: 'franchisee_name',
		title: 'Franchisee Name',
		sortEnabled: false
	},
	{
		name: 'tin_number',
		title: 'Tax Identification Number',
		sortEnabled: false
	},
	{
		name: 'reason',
		title: 'Reason',
		sortEnabled: false
	},
	{
		name: 'remarks',
		title: 'Remarks',
		sortEnabled: false
	},
	{
		name: 'date',
		title: 'Date',
	},
	{
		name: 'time',
		title: 'Time',
	},
	{
		name: 'action',
		title: 'Action',
		sortEnabled: false
	}
] as columnType[];

export const tableColumnExtensionsFranchiseViewAllSalesUser = [
	{
		columnName: 'frf_no',
		wordWrapEnabled: false
	}, {
		columnName: 'reg_corp_name',
		wordWrapEnabled: false,
	}, {
		columnName: 'status_update',
		wordWrapEnabled: false
	}, {
		columnName: 'client_id',
		wordWrapEnabled: false
	}, {
		columnName: 'reason',
		wordWrapEnabled: false
	}, {
		columnName: 'remarks',
		wordWrapEnabled: false,
		// maxWidth: 550
	}, {
		columnName: 'action'
	}
] as columnExtensions[];

export const tableColumnExtensionsFranchiseViewAllHigherRoles = [
	{
		columnName: 'frf_no',
		wordWrapEnabled: true,
	}, {
		columnName: 'reg_corp_name',
		wordWrapEnabled: false,
	}, {
		columnName: 'brand_name',
		wordWrapEnabled: true,
	}, {
		columnName: 'status_update',
		wordWrapEnabled: false,
	}, {
		columnName: 'client_id',
		wordWrapEnabled: true
	}, {
		columnName: 'franchisee_name',
		wordWrapEnabled: true,
	}, {
		columnName: 'tin_number',
		wordWrapEnabled: true,
	}, {
		columnName: 'reason',
		wordWrapEnabled: true
	}, {
		columnName: 'remarks',
		wordWrapEnabled: true,
		// maxWidth: 550
	}, {
		columnName: 'action'
	}
] as columnExtensions[];

export const tableColumnFranchiseViewAllSalesUser = [
	{
		name: 'frf_no',
		title: 'FRF No.'
	}, {
		name: 'reg_corp_name',
		title: 'Registered Corporate Name'
	}, {
		name: 'status_update',
		title: 'Status',
		sortEnabled: false
	}, {
		name: 'client_id',
		title: 'Client ID',
		sortEnabled: false
	}, {
		name: 'reason',
		title: 'Reason',
		sortEnabled: false
	}, {
		name: 'remarks',
		title: 'Remarks',
		sortEnabled: false
	}, {
		name: 'action',
		title: 'Action',
		sortEnabled: false
	}
] as columnType[];

export const tableColumnFranchiseViewAllHigherRoles = [
	{
		name: 'frf_no',
		title: 'FRF No.'
	},
	{
		name: 'reg_corp_name',
		title: 'Registered Corporate Name'
	},
	{
		name: 'brand_name',
		title: 'Trade/Brand Name',
	},
	{
		name: 'status_update',
		title: 'Status',
		sortEnabled: false
	},
	{
		name: 'client_id',
		title: 'Client ID',
		sortEnabled: false
	},
	{
		name: 'franchisee_name',
		title: 'Franchisee Name',
		sortEnabled: false
	},
	{
		name: 'tin_number',
		title: 'Tax Identification Number',
		sortEnabled: false
	},
	{
		name: 'reason',
		title: 'Reason',
		sortEnabled: false
	},
	{
		name: 'remarks',
		title: 'Remarks',
		sortEnabled: false
	},
	{
		name: 'action',
		title: 'Action',
		sortEnabled: false
	}
] as columnType[];

export interface Address {
    floor: string;
    unit: string;
    bldg_no: string;
    bldg_name: string;
    street: string;
    brgy: string;
    city: string;
    province?: string;
    region: string;
    zip_code: string;
}

export interface Remark {
    _id: string;
    user_id: string;
	main_role: string;
	roles: any[];
    message: string;
    created_at: string;
    updated_at: string;
}

export interface Reason {
    _id: string;
    user_id: string;
	main_role: string;
	roles: any[];
	type: string;
	reason: string;
	remark: Remark;
    created_at: string;
    updated_at: string;
}

export interface CompanyAffiliates {
	_id: string;
  	corporate_name: string;
}