export interface IUserRole {
    isSalesUser: boolean;
    isSpecialist: boolean;
    isManager: boolean;
    isSupervisor: boolean;
    isMaintainer:boolean;
    isAbtMaintainer:boolean;
    isSalesHead:boolean;
}

export interface iProfile{
    body: content;
    count: Number;
}

export interface content {
    _id: string;
    last_name: string;
    middle_name: string;
    first_name: string;
    full_name: string;
    job_title: string;
    username: string;
    employee_id: string;
    department: {
        name: string;
    },
    location: string;
    profile_pic: string;
    email: string;
    main_module: string;
    main_role: {
        _id: string;
        name: string;
        description: string;
        module: string;
        role_id: string;
        is_processmaker: Boolean;
        effectivity_date: string;
        rbac: iRBAC[];
        associated_groups: iAssociatedGroups[]
        status: string;
        date_last_update: string;
    };
    is_locked: Boolean;
    effectivity_date: string;
    group: {
        _id: string;
        group_id: Number;
        name: string;
        description: string;
        department: string;
        user_count: Number;
        effectivity_date: string;
        date_updated: string;
        is_processmaker: Boolean;
        pmaker_uid: string;
        amount_limit: Number;
        roles: iRoles[];
        status: string;
        user_account: Number;
    };
    group_name: string;
    meta_data: {
        temporary_password: {
            old: string;
        }
    };
    roles: iRoles[];
    status: string;
    retry_count: Number;
    last_login: string;
    is_password_exempt: Boolean;
    is_online: Boolean;
    group_roles: iRoles[];
}

export interface iRBAC {
    _id: string;
    module: string;
    method: string;
    resource: string;
    name: string;
    operation: string;
    policy: string;
    policy_id: string;
    element_id: string;
    expiry_date: string;
    effectivity_date: string;
    status: string;
}

export interface iAssociatedGroups {
    _id: string;
    group_id: Number;
    name: string;
    description: string;
    user_account: Number;
    effectivity_date: string;
    date_updated: string;
    is_processmaker: Boolean;
    amount_limit: Number;
    user_count: Number;
    status: string;
    department: string;
    roles: iRoles[]
}

export interface iRoles {
    _id: string;
    name: string;
    description: string;
    module: string;
    role_id: string;
    is_processmaker: Boolean;
    effectivity_date: string;
    rbac: iRBAC[];
    associated_groups?: iAssociatedGroups
    status: string;
    date_last_update: string;
}
