import { Franchise } from './Franchise';
export interface StatusHistory {
    _id: string;
    user_id: string;
    main_role: string;
    status: string;
    dtr_date: string;
    franchise: Franchise;
    created_at: string;
    updatedAt: string;
}

export interface Roles {
    _id: string;
    name: string; 
}

export interface ViewGeneratedReport {
    status_history: StatusHistory[];
    franchises: Franchise[];
    _id: string;
    generated_by: string;
    generated_date_from:string;
    generated_date_to:string;
    report: string;
    send_to: string;
    full_name: string;
    requested_by_name: string;
    requested_by_id: string;
    created_at: string;
    updatedAt: string;
    roles:Roles[]
}
export interface previewDataFrom {
    isDTR: Boolean;
    isGDTR: Boolean;
    DTRfrom: string;
    DTRto: string;
    GTRfrom: string;
    GTRto: string;
    search: {
      date: string;
      salesUser: string;
    }
}
export interface searchCriteria {
    date: string;
    client_id: string;
    franchisee_name: string;
    frf_no: string;
    reg_corp_name: string;
    status_update: string;
    tin: string;
    trade: string;
    user: string
}