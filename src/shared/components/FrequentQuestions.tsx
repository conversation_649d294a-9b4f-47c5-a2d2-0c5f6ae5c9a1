import React from 'react'
import { Box, Typography } from "@mui/material";
import { PageSubHeader } from './PageSubHeader';

interface IProps {}

const FrequentQuestions: React.FC<IProps> = () => {
    return (
        <>
        <PageSubHeader title="Frequently Asked Questions" ></PageSubHeader>
        <Box className="main vni-py-8 vni-px-12">
            <Typography component="p" className="text-14" paragraph={true}>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse ut ligula vestibulum, maximus dui quis, 
                dapibus libero. In consequat, libero in scelerisque dignissim, justo nunc blandit ante, a elementum eros arcu a dolor. 
                Nullam tellus leo, volutpat vel dictum eu, placerat vitae mauris. Nunc magna lorem, eleifend eget leo vel, bibendum 
                vulputate diam. Etiam iaculis non mi nec pulvinar. Pellentesque nec pellentesque nisl. 
                </Typography>
            <Typography component="p" className="text-14" paragraph={true}>
                Curabitur nisl ligula, semper 
                at luctus vitae, accumsan quis lacus. Etiam sollicitudin ultricies nisl, a ultricies lorem accumsan sit amet. Quisque 
                facilisis feugiat purus, quis elementum dui porta vitae. Proin in diam non nunc gravida efficitur hendrerit ut nisl.
                Pellentesque in augue in justo malesuada volutpat. 
            </Typography>
            <Typography component="p" className="text-14" paragraph={true}>
                Praesent eros felis, fringilla vel erat at, ornare vehicula tellus.
                Ut nec mi id nisl pharetra luctus in vitae ligula. Aliquam ut lorem augue. Sed mattis ligula nunc, in pharetra metus
                tincidunt in. Duis euismod nunc nisi, ut varius purus tincidunt non. Ut tempor a lectus et tempus. Orci varius natoque 
                penatibus et magnis dis parturient montes, nascetur ridiculus mus. Vivamus rhoncus semper arcu vitae eleifend. Nunc sed 
                vestibulum urna, viverra cursus lacus. 
            </Typography>
            <Typography component="p" className="text-14" paragraph={true}>
                In vitae blandit augue, at ultrices turpis. Cras dapibus nisi sed justo tristique 
                rutrum. Duis quis rutrum sapien. Nam ac ipsum id massa porta porta nec vitae neque. 
            </Typography>
            <Typography component="p" className="text-14" paragraph={true}>
                Sed finibus tortor diam, sed 
                fringilla neque faucibus et. Phasellus at lorem enim. Donec maximus in odio vel bibendum. Nam tristique, ex vitae 
                hendrerit facilisis, enim augue ornare elit, eget molestie sapien felis eget orci. Pellentesque pellentesque mauris 
                eu magna venenatis, sit amet aliquam erat pharetra. Aenean vel bibendum turpis. Vivamus maximus, tellus at suscipit 
                euismod, dui lectus fringilla ante, a convallis ex mi ac ipsum. 
            </Typography>
            <Typography component="p" className="text-14" paragraph={true}>
                Curabitur suscipit laoreet vestibulum. Morbi lacinia 
                ornare tellus, a blandit elit vehicula nec. Proin semper mauris tortor, sit amet gravida urna scelerisque ut. Etiam eu 
                venenatis diam, id finibus quam. Pellentesque malesuada facilisis efficitur. Vivamus laoreet vehicula pharetra. 
                Nam risus felis, mattis in nunc nec, rhoncus consectetur urna.
            </Typography>
        </Box>
        </>
    )
}

export default FrequentQuestions;
