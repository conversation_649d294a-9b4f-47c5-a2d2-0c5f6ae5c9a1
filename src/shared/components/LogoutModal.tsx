import React, { Component, useState } from "react";
import { useHistory } from "react-router-dom";
import { Dialog, DialogTitle, DialogContent, DialogActions, Select, MenuItem } from "@mui/material";

const LogoutModal: React.FC = () => {
  // let history = useHistory();
  const [openModal, setOpenModal] = useState(false);

  const handleLogout = () => {
    window.location.replace("../index.html#/");
  };
  return (
    <div id="log-out" className="modal">
      <h3 className="title">Log out</h3>
      <p>Are you sure you want to log out?</p>
      <span className="btn-group">
        <a
          data-cy="logout-yes-btn"
          rel="modal:close"
          className="CustomPrimaryOulineButton"
          onClick={handleLogout}
        >
          Yes
        </a>
        <a rel="modal:close" className="CustomPrimaryButton" data-cy="logout-no-btn">
          No
        </a>
      </span>
    </div>
  );
};

export default LogoutModal;
