import React, { useState, useEffect } from "react"
import { NavLink, useLocation } from "react-router-dom"

// DX Grid imports
import {
	Grid as GridTable,
	Table,
	TableHeaderRow,
	TableFixedColumns,
	PagingPanel,
	TableSelection,
	Toolbar,
	SearchPanel,
	TableFilterRow,
} from "@devexpress/dx-react-grid-material-ui"

import {
	PagingState,
	IntegratedPaging,
	SortingState,
	IntegratedSorting,
	SelectionState,
	IntegratedSelection,
	SearchState,
	FilteringState,
	IntegratedFiltering,
} from "@devexpress/dx-react-grid"

import moment from "moment"

import { Paper, TableCell, Box, Checkbox } from "@mui/material"

// Relative imports
import { tableStyles } from "./styles"
import {
	renderSortable,
	DefaultTableToolbar,
	renderSearchPanel,
	FilterCell,
} from "./SubComponents"

// Redux imports
import { RootState } from "../../reducers/rootReducer"
import { useSelector } from "react-redux"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import { faCheckSquare } from "@fortawesome/free-solid-svg-icons"

interface TableProps {
	rows: any[]
	columns: Array<{
		title: string
		name: string
		sortEnabled?: boolean
		filterEnabled?: boolean
	}>
	columnExtensions?: any[]
	enableSelection?: boolean
	selection?: any
	handleSelection?: any
	enableSearch?: boolean
	enablePaging?: boolean
	handleViewClick?: (id: any) => any
	searchResult?: any
	result?: any
	tableItem?: any
	enableColumnFilter?: boolean
	filteringStateColumnExtensions?: {
		columnName: string
		predicate?: any
		filteringEnabled: boolean
	}[]
	customNoDataMessage?: string
	isGeneratedReportList?: Boolean
	minHeight?: any
	userRole?: string
}

export const TableComponent: React.FC<TableProps> = (
	props: TableProps,
)=> {
	const {
		rows,
		columns,
		columnExtensions,
		enableSelection,
		handleSelection,
		selection,
		enableSearch,
		enablePaging,
		searchResult,
		enableColumnFilter,
		filteringStateColumnExtensions,
		customNoDataMessage,
		isGeneratedReportList,
		tableItem,
		minHeight,
		userRole
	} = props

	const gridTableProps = {
		rows: rows,
		columns: columns,
	};

	let location = useLocation()

	let report = `/franchising/report/`
	const { isSalesUser, isManager, isSpecialist, isSupervisor } = useSelector(
		(state: RootState) => state.login.userRoles,
	)

	const mainRole = useSelector((state: RootState) => state.login.main_role);
  	// const mainModule = useSelector((state: RootState) => state.login.main_module);


	const userRoles = useSelector((state: RootState) => state.login.userRoles);
	const multipleRoles = Object.values(userRoles).reduce(
		(a, userRole) => a + userRole,
		0
	);

	console.log('rolesss', userRoles);
	console.log('current role', mainRole);
	
	const [isMultipleRoles, setIsMultipleRoles] = useState(false);
	
	const isSearching = useSelector(
		(state: RootState) => state.franchise.isSearching,
	)
	console.log('rolesssss', (isMultipleRoles && location.pathname.includes("encoder")));
	const activeFilterIndex = useSelector(
		(state: RootState) => state.franchise.activeFilterIndex,
	)
	const { success } = useSelector(
		(state: RootState) => state.franchise.bulkSubmitData,
	)
	const { canEditFR, canViewFR, canViewReport, canViewBrokerAgent } = useSelector(
		(state: RootState) => state.login.userPermissions,
	)

	const classes = tableStyles()

	const CustomTableHeaderCellBase = ({ ...restProps }: any) => (
		<TableHeaderRow.Cell className={restProps.column.style ? restProps.column.style().style : classes.customHeaderCell} {...restProps} />
	)

	const tableCell = ({ value, row, column }: any) => {
		if (column.name === "action" || column.name === "action_compare") {
			let rowStatus = row.status === undefined ? row.status_update : row.status
			return (
				<TableCell
					style={{
						zIndex: 300,
						position: "sticky",
						right: "0px",
						backgroundClip: "padding-box",
						backgroundColor: "#fff",
						cursor: "pointer",
						color: "#3AB77D",
						textDecoration: "underline",
					}}
				>
					<Box
						component="span"
						data-cy={
							column.name === "action"
								? location.pathname !== report
									? `view-franchise-${row.index_no}`
									: `view-report-${row.index_no}`
								: `compare-franchise-${row.index_no}`
						}
						onClick={() =>
							(canViewFR && location.pathname !== report) ||
							(canViewBrokerAgent && location.pathname !== report) ||
							(canViewReport && location.pathname === report)
								? props["handleViewClick"] && props.handleViewClick(row.id)
								: null
						}
						style={
							(canViewFR && location.pathname !== report) ||
							(canViewBrokerAgent && location.pathname !== report) ||
							(canViewReport && location.pathname === report)
								? { marginRight: "10px" }
								: {
										marginRight: "10px",
										fontWeight: 400,
										color: "#c5c4c9",
										textDecorationLine: "inherit",
								  }
						}
					>
						{column.name === "action" ? "View" : "Compare"}
					</Box>
					{(
						(location.pathname !== report && (isSalesUser && userRole === 'encoder')) || 
						(isMultipleRoles && location.pathname.includes("encoder"))
					) && column.name === "action" && location.pathname !== "/franchising/contact/" ? (
						<Box component="span" style={{ marginRight: "5px" }}>
							<NavLink
								to={`/franchising/franchise/edit/${row.id}`}
								data-cy={`edit-franchise-${row.index_no}`}
								className={
									(rowStatus === "Saved" ||
										rowStatus === "Submitted" ||
										rowStatus === "Resubmitted" ||
										rowStatus === "Returned") &&
									canEditFR
										? ""
										: "disabled"
								}
							>
								Edit
							</NavLink>
						</Box>
					) : ( location.pathname !== report &&
						canViewBrokerAgent &&
						column.name === "action" && (isMultipleRoles && location.pathname.includes("encoder")) ? (
							<Box component="span" style={{ marginRight: "5px" }}>
								<NavLink
									to={`/franchising/contact/edit/${row.id}`}
									data-cy={`edit-contact-${row.index_no}`}
									className={""}
								>
									Edit
								</NavLink>
							</Box>
						) : null ) }
				</TableCell>
			)
		} else if (column.name === "time") {
			return <TableCell>{moment(value).format("hh:mm:ss a")}</TableCell>
		} else if (column.name === "date") {
			return <TableCell>{moment(value).format("MM/D/YYYY")}</TableCell>
		} else if (column.name === "status" || column.name === "status_update") {
			let actualExpiryDate = moment(row.expiry_date)
			let expiryCounter = actualExpiryDate.diff(moment().startOf("day"), "days")

			let actualReturnDuedate = moment(row.return_due_date)
			let dueDateCounter = actualReturnDuedate.diff(
				moment().startOf("day"),
				"days",
			)

			let ApproveExpireColor = ""
			if (expiryCounter > 15 && expiryCounter <= 30) {
				ApproveExpireColor = "vni-text-gold"
			} else if (expiryCounter <= 15) {
				ApproveExpireColor = "red"
			}

			let ReturnDueColor = ""
			if (dueDateCounter <= 5) {
				ReturnDueColor = "red"
			}

			if (
				location.pathname !== report &&
				row.backend_status === "APPROVED" &&
				row.account_status !== "ACTIVE" &&
				expiryCounter <= 30 &&
				(isSalesUser || isManager || isSupervisor || isSpecialist)
			) {
				return (
					<TableCell>
						{value} (
						<Box component="span" className={ApproveExpireColor}>
							{expiryCounter === 0 ? "-" : expiryCounter}
						</Box>
						)
					</TableCell>
				)
			} else if (
				location.pathname !== report &&
				row.backend_status === "RETURNED" &&
				dueDateCounter <= 5 &&
				isSalesUser
			) {
				return (
					<TableCell>
						{value} (
						<Box component="span" className={ReturnDueColor}>
							{dueDateCounter === 0 ? "-" : dueDateCounter}
						</Box>
						)
					</TableCell>
				)
			} else {
				return <TableCell>{value}</TableCell>
			}
		} else {
			return <TableCell>{value}</TableCell>
		}
	}

	const customSortAlgorithm = (a: any, b: any) => {
		let a1 = a.toLowerCase()
		let b1 = b.toLowerCase()

		return a1 < b1 ? -1 : 1
	}

	const [integratedSortingColumnExtensions] = React.useState([
		{
			columnName: "reg_corp_name",
			compare: customSortAlgorithm,
			sortingEnabled: true,
		},
		{
			columnName: "brand_name",
			compare: customSortAlgorithm,
			sortingEnabled: true,
		},
		{
			columnName: "user",
			compare: customSortAlgorithm,
			sortingEnabled: true,
		},
		{
			columnName: "franchisee_name",
			compare: customSortAlgorithm,
			sortingEnabled: true,
		},
		{
			columnName: "trade",
			compare: customSortAlgorithm,
			sortingEnabled: true,
		},
	])

	const [pageSizes] = useState(location.pathname === "/franchising/contact/" ? [10, 20, 50] : [10, 15, 20])
	const [pageSize, setPageSize] = useState(10)
	let path = useLocation()

	const [disableSelectAll, setDisableSelectAll] = useState<boolean>(true)

	useEffect(() => {
		if (enableSelection) {
			setDisableSelectAll(true)
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [activeFilterIndex, isSearching, success])

	const customCellComponent = ({ onToggle, selected, row }: any) => {
		// let disabled = row.status_update !== 'Saved';
		let disabled =
			row.status_update === "Saved" || row.status_update === "Returned"
		if (disabled) {
			setDisableSelectAll(false)
		}
		return (
			<Checkbox
				data-cy={`checkbox-item-${row.index_no}`}
				disabled={!disabled}
				checked={selected && disabled}
				onClick={onToggle}
			/>
		)
	}

	const customHeaderSelectComponent = ({
		onToggle,
		disabled,
		allSelected,
		someSelected,
	}: any) => {
		return (
			<Checkbox
				data-cy="checkbox-select-all"
				disabled={disableSelectAll}
				checked={allSelected}
				// indeterminate={someSelected}
				indeterminateIcon={
					<FontAwesomeIcon icon={faCheckSquare} color={"#3ab77d"} className="" />
				}
				onClick={() => {
					onToggle(!allSelected)
				}}
			/>
		)
	}

	const handlePagingChange = (currentPage: number) => {
		setDisableSelectAll(true)
	}

	useEffect(() => {
		if (multipleRoles > 1) {
		  setIsMultipleRoles(true);
		}
	  }, [multipleRoles]);

	return (
		<>
			<Paper className={classes.main} style={{ minHeight: minHeight }}>
				<GridTable {...gridTableProps}>
					<PagingState
						defaultCurrentPage={0}
						pageSize={
							enablePaging
								? pageSize
								: path.pathname.includes("/franchising/dashboard")
								? 10
								: 0
						}
						onPageSizeChange={setPageSize}
						onCurrentPageChange={handlePagingChange}
					/>
					<SortingState />

					{enableColumnFilter && (
						<FilteringState
							defaultFilters={[]}
							columnExtensions={filteringStateColumnExtensions}
						/>
					)}

					<IntegratedFiltering
						columnExtensions={filteringStateColumnExtensions}
					/>
					<IntegratedSorting
						columnExtensions={integratedSortingColumnExtensions}
					/>
					<IntegratedPaging />

					{enableSelection && (
						<SelectionState
							selection={selection}
							onSelectionChange={handleSelection}
						/>
					)}
					{enableSelection && <IntegratedSelection />}

					{enableSearch && <SearchState />}

					<Table
						data-cy="franchise_list_table"
						cellComponent={tableCell}
						columnExtensions={columnExtensions}
						noDataCellComponent={() => {
							return (
								<TableCell colSpan={42}>
									<div
										className="vni-text-center vni-italic vni-underline vni-text-gray-700 vni-py-40"
										style={{
											width: isGeneratedReportList ? "" : "calc(100vw - 198px)",
											position: "sticky",
											left: "0",
											marginLeft: "-0.75rem",
										}}
									>
										{/* isSearching && searchResult.length === 0 && tableItem.length == 0 */}
										{(isSearching && tableItem && tableItem.length === 0) ||
										(isSearching &&
											searchResult &&
											searchResult.length !== 0 &&
											tableItem &&
											tableItem.length === 0) ||
										(!isSearching && tableItem && tableItem.length !== 0)
											? "Record does not exist in our system"
											: customNoDataMessage
											? customNoDataMessage
											: "No franchise request yet"}
									</div>
								</TableCell>
							)
						}}
					/>

					<TableHeaderRow
						showSortingControls
						sortLabelComponent={renderSortable}
						cellComponent={CustomTableHeaderCellBase}
					/>

					{enableColumnFilter && <TableFilterRow cellComponent={FilterCell} />}

					{enableSearch && <Toolbar rootComponent={DefaultTableToolbar} />}
					{enableSearch && (
						<SearchPanel
							inputComponent={(e) => renderSearchPanel(e, classes)}
						/>
					)}

					{enableSelection && (
						<TableSelection
							showSelectAll
							cellComponent={customCellComponent}
							headerCellComponent={customHeaderSelectComponent}
							selectionColumnWidth={20}
						/>
					)}

					<TableFixedColumns rightColumns={["action"]} />

					{enablePaging ? <PagingPanel pageSizes={pageSizes} /> : null}
				</GridTable>
			</Paper>
		</>
	)
}
