import { makeStyles } from '@mui/styles'

const tableStyles = makeStyles({
    main: {
        '@global': {
            '.MuiTableCell-head': {
                backgroundColor: '#DDDFE3',
                border: 'none',
                fontWeight: 'bold',
                color: '#272E4C'
            },
            '.MuiTableRow-head': {
                backgroundColor: '#DDDFE3',
                border: 'none',
                // fontWeight: 'bold',
                
            },
            '.MuiTableCell-root': {
                borderBottom: 'none',
                borderLeft: 'none',
                fontFamily: 'usual, sans-serif !important'
            },
            '.tableHeaderCell-container': {
                whiteSpace: 'normal'
            },
            '.MuiCheckbox-colorSecondary.Mui-checked': {
                color: '#3AB77D'
            },
            '.MuiTableSortLabel-root:':{
                '&hover': {
                    color: '#272E4C'
                },
                '&focus': {
                    color: '#272E4C'
                }
            },
            '.MuiTableCell-paddingCheckbox': {
                maxWidth: '48px !important',
                minWidth: '48px !important'
            },
            '.TableInvisibleRow-row-287': {
                display: 'none !important'
            },
            '.MuiInputBase-input.Mui-disabled': {
                backgroundColor: '#cccccc',
                opacity: '.60',
                padding: '7.5px 11px !important'
                // pointer-events: none;
                // opacity: .60;
            },
        }
    },
    customHeaderCell: {
        '& div': {
            // whiteSpace: "normal",
            // wordWrap: "break-word"
        }
    },
    headerCell: {
        backgroundColor: 'red'
    },
    outlineButton: {
        backgroundColor: '#3AB77D',
        color: '#FFFFFF',
        textTransform: 'capitalize',
        width: 140,
        '&:hover': {
            backgroundColor: '#3AB77D'
        }
    }
})

export {
    tableStyles
}