import React, {useEffect} from 'react'
import {
    TableSortLabel,
    Toolbar,
    Grid,
    OutlinedInput,
    Button,
    Select as MuiSelect,
    MenuItem,
    TableCell,
} from '@mui/material'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
    faSort,
    faSortUp,
    faSortDown
} from '@fortawesome/free-solid-svg-icons'

import {
  LocalizationProvider,
  DatePicker,
} from "@mui/x-date-pickers";
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useDispatch, useSelector } from "react-redux"
import { setSearchItems } from "../../../reducers/ReportSlice"
import { RootState } from '../../../reducers/rootReducer'
import moment from 'moment'
 
const SortingIcon = ({ direction }: any) =>
    direction === 'asc' ? (
        <FontAwesomeIcon style={{ marginLeft: '10px', color: '#A5A7AD', fontSize: '12px' }} icon={faSortUp} className='' />
    ) : (
            <FontAwesomeIcon style={{ marginLeft: '10px', color: '#A5A7AD', fontSize: '12px' }} icon={faSortDown} className='' />
        );

const renderSortable = ({ column, onSort, children, direction }: any) => {

    if (
        !column.hasOwnProperty('sortEnabled') &&
        !column.sortEnabled 
    ) {
        return (
            <TableSortLabel hideSortIcon={true} onClick={onSort} data-cy={column.sort_text?`${column.name}_sort_icon`:`${column.name}_sort`}>
                {children}
                {direction ? (
                    <SortingIcon direction={direction} />
                ) : (
                        <FontAwesomeIcon style={{ marginLeft: '10px', color: '#A5A7AD', fontSize: '12px' }} icon={faSort} className='' />
                    )}
            </TableSortLabel>
        );
    } else {
        return <span>{column.title}</span>;
    }
}

const Select = ({ items, ...restProps }: any) => {
    return (
        <MuiSelect
            style={{ width: '100%' }}
            renderValue={(selected: any) => {
                if (selected != null) {
                    const value: any = items.find((item: any) => {
                        return item.value === selected;
                    });
                    if (value) {
                        return value.label;
                    } else {
                        return '——';
                    }
                }
                return '——';
            }}
            {...restProps}
        >
            {items.map((item: any, i: number) => (
                <MenuItem key={i} value={item.value}>
                    {item.label}
                </MenuItem>
            ))}
        </MuiSelect>
    );
};

const renderSearchPanel = ({ value, onValueChange }: any, classes: any) => {
    let inputValue=''; 
    return (
        <Grid
            container
            justifyContent="center"
            alignItems="center"
            spacing={3}>
            <Grid item xs={6}>
                <OutlinedInput
                    style={{ width: '100%', height: 36 }}
                    placeholder='Input Franchise Request'
                    onChange={e => inputValue = e.target.value}
                />
            </Grid>
            <Grid item xs={2}>
                <Button
                    className={classes.outlineButton}
                    onClick={() => onValueChange(inputValue ? inputValue : '')}>Search</Button>
            </Grid>
            <Grid item xs={4}>
                <Grid container>
                    <Grid item xs={2}>
                        <span>Filter by:</span>
                    </Grid>
                    <Grid item xs={10}>
                        <Select
                            onChange={() => onValueChange(inputValue ? inputValue : '')}
                            name={'status'}
                            items={[]}
                            id={'status'}
                            value={inputValue ? inputValue : ''}
                        />
                    </Grid>
                </Grid>
            </Grid>
        </Grid>
    )
}

const FilterCell = ({ filter, onFilter, filteringEnabled, classes, column }: any) => {
    const dispatch = useDispatch()
    const search = useSelector((state: RootState) => state.report.search)

    useEffect(()=>{
        let searchContent: any = search
        if(filter && filter.value !== ""){
            if(column["name"] === "date"){
                dispatch(setSearchItems({...searchContent,...{[column["name"]]: filter.value === ""? "" : moment(filter.value).format('MM/DD/YYYY')}}))
            }else if(column["name"] === "status_update"){
                let status = filter.value.toLowerCase()
                  switch (status) {
                    case "validation in-process":
                        dispatch(setSearchItems({...searchContent,...{[column["name"]]: filter.value === ""? "" : "RECEIVED"}}))
                        break;
                    case "validation complete":
                        dispatch(setSearchItems({...searchContent,...{[column["name"]]: filter.value === ""? "" : "VALIDATED"}}))
                        break;
                    case "resubmitted to supervisor":
                        dispatch(setSearchItems({...searchContent,...{[column["name"]]: filter.value === ""? "" : "RESUBMITTED_TO_SUPERVISOR"}}))
                        break;
                    case "returned":
                        dispatch(setSearchItems({...searchContent,...{[column["name"]]: filter.value === ""? "" : "RETURNED_TO_VALIDATOR"}}))
                        break;
                    case "returned to sales user":
                        dispatch(setSearchItems({...searchContent,...{[column["name"]]: filter.value === ""? "" : "RETURNED"}}))
                        break;
                    default:
                        dispatch(setSearchItems({...searchContent,...{[column["name"]]: filter.value === ""? "" : filter.value}}))
                        break;
                }
            }else{
                dispatch(setSearchItems({...searchContent,...{[column["name"]]: filter.value}}))
            }
        }else{
            const isEmpty = (obj: any) => {
                if (!obj || typeof obj !== 'object') return true;
                for(var key in obj) {
                    if(obj.hasOwnProperty(key))
                        return false;
                }
                return true;
            }

            if(!isEmpty(searchContent)){
                dispatch(setSearchItems({...searchContent,...{[column["name"]]: ""}}))
            }
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    },[filter])

    // Note: Use switch case to handle different fields or state of input per column
    // if no column specified default material ui outlined input will be shown
    if (column.filterEnabled) {
        switch (column['name']){
            case 'date':
                return(
                    <TableCell style={{ padding: '0px 10px 8px 10px', width: '100%'}} classes={classes}>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                            <DatePicker
                                className="vni-search-date-picker"
                                format="MM/dd/yyyy"
                                maxDate={new Date()}
                                value={filter ? filter.value : null}
                                onChange={data => {
                                    if(data){
                                        data.setHours(0o0, 0o0, 0o0)
                                    }
                                    onFilter(data ? { value: data} : null)
                                }}
                                slotProps={{
                                  textField: {
                                    id: "franchise_reports_date_column_filter",
                                    style: { width: '110%', height: 36 },
                                    variant: 'outlined',
                                    inputProps: {
                                      'data-cy': "franchise_reports_date_column_filter"
                                    }
                                  }
                                }}
                                />
                        </LocalizationProvider>
                    </TableCell>
                )
                // break; 
            default:
                return (
                    <TableCell style={{ padding: '0px 10px 8px 10px', width: '100%'}} classes={classes}>
                        <OutlinedInput
                            data-cy={filteringEnabled?`${column['name']}_filter`:null}
                            disabled={!filteringEnabled}
                            classes={classes}
                            style={{ width: '100%', height: 36 }}
                            value={filter ? filter.value : ''}
                            onChange={e => onFilter(e.target.value ? { value: e.target.value } : null)}
                            placeholder=""
                        />
                    </TableCell>
                )
                // break;
        }
    }

    return (
        <TableCell style={{ padding: '0px 10px 8px 10px' }} classes={classes}></TableCell>
    )
}

const DefaultTableToolbar = ({ children }: any) => {
    return <Toolbar disableGutters={true} children={children} />
}



export {
    renderSortable,
    DefaultTableToolbar,
    renderSearchPanel,
    FilterCell,
}