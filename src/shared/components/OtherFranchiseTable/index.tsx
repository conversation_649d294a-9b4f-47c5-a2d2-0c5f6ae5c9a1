import React, { useEffect } from 'react'
import { Grid } from '@mui/material'
import { TableComponent } from '../TableComponent';
import { columns, columnExtensions } from './OtherFranchiseTable.const';
import { useSelector } from 'react-redux';
import { RootState } from '../../reducers/rootReducer';
import { Franchise } from '../../models/Franchise';
import { getReasonRemarkForFlist } from '../../../utils/DataHelper';

interface IProps {
    handleClickButton: (id: string) => any
}

export const OtherFranchiseTable: React.FC<IProps> = (props: any)=> {
    const userRoles = useSelector((state: RootState) => state.login.userRoles);
    const {duplicate} = useSelector((state: RootState) => state.franchise.details);
    const [tableRows, setTableRows] = React.useState<any>([]);
    const {handleClickButton} = props
    useEffect(() => {
      if(duplicate && duplicate.length > 0){
        let tableRowContents: any = [];
        tableRowContents = duplicate.map((content: Franchise, index:number) => {
            let {frf_id,_id } = content
            let requested_by = content.applicant_name;
            let {remark} = getReasonRemarkForFlist(content.status,content.reasons??[],content.remarks??[], userRoles);
            
            let actualStatus: string;
              switch (content.status) {
                case "FINAL_APPROVAL":
                  actualStatus = "For Approval";
                  break;
                case "FINAL_APPROVAL_IN_PROCESS":
                  actualStatus = "Approval In Process";
                  break;
                case null:
                  actualStatus = "";
                  break;
                default:
                    actualStatus =
                      content.status[0].toUpperCase() +
                      content.status.slice(1).toLowerCase();
                  break;
              }
            
            return {
                index_no:index+1,
                frf_id: String(frf_id).padStart(6, "0"),
                requested_by,
                status:actualStatus,
                remarks:remark,
                id:_id
            };
        });
        setTableRows(tableRowContents)
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [duplicate]);
   
    return (
        <Grid className="vni-table" container>
            <p className="vni-text-lg vni-font-bold vni-mb-5">Other Sales User Requested</p>
            <TableComponent
                rows={tableRows}
                columns={columns}
                columnExtensions={columnExtensions}
                enablePaging={false}
                customNoDataMessage="No Records Found"
                handleViewClick={handleClickButton}
                minHeight= {300}
            />
        </Grid>
    )
}