import React from 'react'
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../reducers/rootReducer';
import { CircularProgress} from '@mui/material';
import { logout } from "../reducers/LoginSlice";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";

const Spinner: React.FC = () => {
  const dispatch = useDispatch();
    let requestingFlag = useSelector(
        (state: RootState) => state.franchise.isRequesting
      );
      console.log("requestingFlag",requestingFlag)
    let requestingFlagContact = useSelector(
        (state: RootState) => state.contact.isRequesting
      );
  const session = useSelector((state:RootState) => state.login.auth.sessionExpired)

    return (
        <React.Fragment>
            { ((requestingFlag || requestingFlagContact) && !session ) &&
                <div style={{zIndex:1001,position:"fixed"}} className="vni-absolute vni-z-50 vni-w-full vni-min-h-full bg-black-50 vni-flex vni-items-center vni-justify-center">
                    <CircularProgress color="primary" size={150}/>
                </div>
            }
            { session && (
                <Dialog 
                    id="session-modal"
                    maxWidth='xs'
                    open={session}
                    onClose={() => {
                    dispatch(logout())
                    window.location.replace('../index.html#/')
                    }}
                    className="vni-m-auto"
                >
                    <DialogTitle className="vni-px-5 vni-pt-3 vni-mb-8">
                    <h2 className="title">Session Expired</h2>
                    </DialogTitle>
                    <DialogContent>
                    <p className="vni-mb-8">
                    Your account has logged in on another device. Please contact your administrator if it was not you.
                    </p>
                    </DialogContent>
                    <DialogActions className="vni-flex d-flex-center">
                    <button className="CustomPrimaryButton vni-mr-5" data-cy="session-timeout-logout-btn" onClick={() => {
                        dispatch(logout())
                        window.location.replace('../index.html#/')
              
                        }}>Okay</button>
                    </DialogActions>
                </Dialog>
            )}
        </React.Fragment>
    )
}

export default Spinner
