import React, { useEffect, useState } from "react";
import { useLocation, useHistory, NavLink } from "react-router-dom";
import { RootState } from "../reducers/rootReducer";
import { startOfToday } from "date-fns";
import {
  fetchFranchise,
  fetchFranchises,
  goBackAndCreateNew,
  resetState,
  addFranchise,
  clearValidationModalContent,
  updateFranchiseRequest,
  setModalContent,
  setValidatorUploaded,
} from "../reducers/FranchiseSlice";
import { resetPreviewData } from "../reducers/ReportSlice";
import { useDispatch, useSelector } from "react-redux";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Select,
  MenuItem,
  Grid,
  Typography,
  TextField,
  List,
  ListItem,
} from "@mui/material";
import {
  LocalizationProvider,
  DatePicker,
} from "@mui/x-date-pickers";
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import loodingLogo from "../../assets/images/loading-spinner.gif";

import { TableComponent } from "../../shared/components/TableComponent";
import {
  columnExtensions,
  columnType,
  tableColumnExtensionsDashboardSalesUser,
  tableColumnExtensionsDashboardHigherRoles,
  tableColumnDashboardSalesUser,
  tableColumnDashboardHigherRoles,
  tableColumnExtensionsFranchiseViewAllSalesUser,
  tableColumnExtensionsFranchiseViewAllHigherRoles,
  tableColumnFranchiseViewAllSalesUser,
  tableColumnFranchiseViewAllHigherRoles,
  Franchise,
} from "../../shared/models/Franchise";
import { Modal } from "./Modal";
import { ViewFranchise } from "./ViewFranchise/Layout/ViewFranchise";
import {
  fetchReturnReasonList,
  fetchDisapproveReasonList,
} from "../reducers/MaintenanceSlice";

import { getReasonRemarkForFlist } from "../../utils/DataHelper";
import { OtherFranchiseTable } from "./OtherFranchiseTable";
import { CompareFranchise } from "./CompareFranchise";
import moment from "moment";
import { logout } from "../reducers/LoginSlice";

interface iProps {
  searchResult?: any;
  result?: any;
  setBulkSelect?: any;
  userRole?: any;
  isMultipleRole?: any;
  dateTo?: any;
  dateFrom?: any;
  applyFilter?: any;
}

const FranchiseList: React.FC<iProps> = (props) => {
  const {
    searchResult,
    result,
    setBulkSelect,
    userRole,
    isMultipleRole,
    dateTo,
    dateFrom,
    applyFilter,
  } = props;
  let path = useLocation();
  const dispatch = useDispatch();
  const details = useSelector((state: RootState) => state.franchise.details);
  const token = useSelector((state: RootState) => state.login.token);
  const role = useSelector((state: RootState) => state.login.roleAPI);
  const history = useHistory();
  const [selection, setSelection] = React.useState<any[]>([]);
  const onFranchisePage = path.pathname.includes("/franchising/franchise/");
  const returnReasonList = useSelector(
    (state: RootState) => state.maintenance.returnReasonList
  );
  const disapproveReasonList = useSelector(
    (state: RootState) => state.maintenance.disapproveReasonList
  );
  const [disapproveList, setDisapproveList] = React.useState<any[]>([]);
  const pattern = new RegExp(/\D/);
  const { canAddFR, canSubmitFR } = useSelector(
    (state: RootState) => state.login.userPermissions
  );
  const sessionExpired = useSelector(
    (state: RootState) => state.login.auth.sessionExpired
  );
  console.log("SE FL", sessionExpired);

  useEffect(() => {
    if (token && role) {
      dispatch(fetchFranchises(userRole));
      dispatch(resetPreviewData());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [path, token, role]);

  // fetch franchise list from state
  let FranchiseListData = useSelector(
    (state: RootState) => state.franchise.franchiseList
  );

  // fetch dashboard active tile
  const dashboardTileIndex = useSelector(
    (state: RootState) => state.dashboard.activeTile
  );
  const dashboardSalesTileIndex = useSelector(
    (state: RootState) => state.dashboard.activeSalesTile
  );
  const dashboardSpecialistTileIndex = useSelector(
    (state: RootState) => state.dashboard.activeSpecialistTile
  );
  const dashboardSupervisorTileIndex = useSelector(
    (state: RootState) => state.dashboard.activeSupervisorTile
  );
  const dashboardManagerTileIndex = useSelector(
    (state: RootState) => state.dashboard.activeManagerTile
  );

  const dashboardTileName = useSelector(
    (state: RootState) => state.dashboard.tileName
  );

  // fetch franchise list view active filter
  const franchiseListFilterIndex = useSelector(
    (state: RootState) => state.franchise.activeFilterIndex
  );

  const franchiseFilter = useSelector(
    (state: RootState) => state.franchise.activeFilterName
  );

  // error in fetch
  const error = useSelector((state: RootState) => state.franchise.error);

  // get user roles.
  const userRoles = useSelector((state: RootState) => state.login.userRoles);
  const { isSalesUser, isSpecialist, isSupervisor, isManager } = userRoles;

  // for searching
  const isSearching = useSelector(
    (state: RootState) => state.franchise.isSearching
  );

  const isViewLoading = useSelector(
    (state: RootState) => state.franchise.isViewLoading
  );
  const isRequesting = useSelector(
    (state: RootState) => state.franchise.isRequesting
  );
  const isUpdating = useSelector(
    (state: RootState) => state.franchise.isUpdating
  );
  const validationContent = useSelector(
    (state: RootState) => state.franchise.validationModalContent
  );

  const [openModalView, setOpenModalView] = useState(false);
  const [openOtherSalesUser, setOpenOtherSalesUser] = useState(false);
  const [compareFranchiseId, setCompareFranchiseId] = useState("");
  const [openCompareFranchise, setOpenCompareFranchise] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [defaultExpiry, setDefaultExpiry] = React.useState<any>(null);
  const [confirmValues, setConfirmValues] = useState({
    action: "",
    h1: "",
    p: "",
    text: [] as string[],
  });
  const [modalButton, setModalButton] = useState("");
  const [param, setParam] = useState({
    reason: "",
    remarks: "",
    due_date: "",
    effectivity_date: "",
    expiry_date: "",
  });
  const [paramError, setParamError] = useState({
    reason: "",
    remarks: "",
    due_date: "",
    effectivity_date: "",
    expiry_date: "",
  });

  useEffect(() => {
    if (validationContent.title && validationContent.p) {
      setConfirmValues({
        ...confirmValues,
        h1: validationContent.title,
        p: validationContent.p,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [validationContent]);

  useEffect(() => {
    if (onFranchisePage) {
      setSelection([]);
      setBulkSelect([]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [FranchiseListData, searchResult, franchiseListFilterIndex]);

  useEffect(() => {
    if (details.duplicate && details.duplicate.length > 0) {
      let duplicateList = details.duplicate.map((content: Franchise) => {
        const { _id, applicant_name } = content;
        return {
          id: _id,
          name: applicant_name,
          reason_for_disapprove: "",
          error: "",
        };
      });
      setDisapproveList(duplicateList);
    }
    setDefaultExpiry(handleDefaultExpiry(new Date()));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [details]);

  let tableContents: any = [];
  let tableRow: any = [];
  let tableColumnExtensions: columnExtensions[] = [];
  let column: columnType | any[] = [];
  let tableDashboard: number = 0;

  if (isMultipleRole) {
    if (userRole === "encoder") {
      tableColumnExtensions =
        path.pathname === `/franchising/dashboard/${userRole}`
          ? tableColumnExtensionsDashboardSalesUser
          : tableColumnExtensionsFranchiseViewAllSalesUser;
      column =
        path.pathname === `/franchising/dashboard/${userRole}`
          ? tableColumnDashboardSalesUser
          : tableColumnFranchiseViewAllSalesUser;
      tableDashboard =
        path.pathname === `/franchising/dashboard/${userRole}` ? 1 : 2;
    } else if (
      userRole === "validator" ||
      userRole === "supervisor" ||
      userRole === "manager"
    ) {
      tableColumnDashboardHigherRoles[7].title = isManager
        ? "Reason for Disapproval"
        : "Reason";
      tableColumnFranchiseViewAllHigherRoles[7].title = isManager
        ? "Reason for Disapproval"
        : "Reason";

      tableColumnExtensions =
        path.pathname === `/franchising/dashboard/${userRole}`
          ? tableColumnExtensionsDashboardHigherRoles
          : tableColumnExtensionsFranchiseViewAllHigherRoles;
      column =
        path.pathname === `/franchising/dashboard/${userRole}`
          ? tableColumnDashboardHigherRoles
          : tableColumnFranchiseViewAllHigherRoles;
      tableDashboard =
        path.pathname === `/franchising/dashboard/${userRole}` ? 3 : 4;
    }
  } else {
    if (isSalesUser) {
      tableColumnExtensions =
        path.pathname === `/franchising/dashboard/${userRole}`
          ? tableColumnExtensionsDashboardSalesUser
          : tableColumnExtensionsFranchiseViewAllSalesUser;
      column =
        path.pathname === `/franchising/dashboard/${userRole}`
          ? tableColumnDashboardSalesUser
          : tableColumnFranchiseViewAllSalesUser;
      tableDashboard =
        path.pathname === `/franchising/dashboard/${userRole}` ? 1 : 2;
    } else if (isSpecialist || isSupervisor || isManager) {
      tableColumnDashboardHigherRoles[7].title = isManager
        ? "Reason for Disapproval"
        : "Reason";
      tableColumnFranchiseViewAllHigherRoles[7].title = isManager
        ? "Reason for Disapproval"
        : "Reason";

      tableColumnExtensions =
        path.pathname === `/franchising/dashboard/${userRole}`
          ? tableColumnExtensionsDashboardHigherRoles
          : tableColumnExtensionsFranchiseViewAllHigherRoles;
      column =
        path.pathname === `/franchising/dashboard/${userRole}`
          ? tableColumnDashboardHigherRoles
          : tableColumnFranchiseViewAllHigherRoles;
      tableDashboard =
        path.pathname === `/franchising/dashboard/${userRole}` ? 3 : 4;
    }
  }

  // let sampleActive = FranchiseListData.filter((item:any) => item.account_status == "ACTIVE")
  // let d = sampleActive.map((x:any) => { return {name:x.corporate_name,user:x.sales_username}} )

  // let x = d.filter((x:any) => x.user == "frf_encoder_4" || x.user == "frf_encoder_5")
  // console.log("ACTIVE FR: ", d)
  // console.log("ACTIVE FR user 4&5: ", x)
  // // console.log("ACTIVE FR: ", sampleActive)

  //  filters for content
  if (!error) {
    switch (path.pathname) {
      case `/franchising/dashboard/${userRole}`:
        // for sales user
        if (isMultipleRole) {
          if (userRole === "encoder") {
            // filter data
            if (FranchiseListData.length !== 0) {
              if (
                dashboardTileName === "Total Requests" ||
                dashboardSalesTileIndex === 0
              ) {
                tableContents = FranchiseListData;
              } else if (dashboardTileName === "Saved Requests") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "SAVED"
                );
              } else if (dashboardTileName === "For Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "FOR_APPROVAL" ||
                    status === "APPROVAL_IN_PROCESS" ||
                    status === "RETURNED_TO_VALIDATOR" ||
                    status === "RESUBMITTED_TO_SUPERVISOR" ||
                    status === "FINAL_APPROVAL" ||
                    status === "FINAL_APPROVAL_IN_PROCESS"
                );
              } else if (dashboardTileName === "Submitted") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "SUBMITTED" || status === "RESUBMITTED"
                );
              } else if (dashboardTileName === "Expiring Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "APPROVED" &&
                      account_status !== "ACTIVE" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (dashboardTileName === "Expired Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      (status === "APPROVED" && expiryCounter < 0) ||
                      status === "EXPIRED"
                    );
                  }
                );
              } else if (dashboardTileName === "Expiring Returned Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "RETURNED" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (dashboardTileName === "Expired Returned Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return status === "RETURNED" && expiryCounter < 0;
                  }
                );
              } else {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === dashboardTileName.toUpperCase()
                );
              }

              // row
              tableRow = tableContents.map((content: any, index: any) => {
                let newDate =
                  content.status === "SAVED"
                    ? new Date(content.updated_at)
                    : new Date(content.status_updated_at);
                let actualClientID =
                  content.client_id === "" || content.client_id === undefined
                    ? "--"
                    : content.client_id;
                let actualStatus: string;

                switch (content.status) {
                  case "FOR_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "APPROVAL_IN_PROCESS":
                    actualStatus = "Approval In Process";
                    break;
                  case "FINAL_APPROVAL_IN_PROCESS":
                    actualStatus = "For Final Approval";
                    break;
                  case "FINAL_APPROVAL":
                    actualStatus = "For Final Approval";
                    break;
                  case "RESUBMITTED_TO_SUPERVISOR":
                    actualStatus = "Resubmitted To Supervisor";
                    break;
                  case "RETURNED_TO_VALIDATOR":
                    actualStatus = "Returned";
                    break;
                  case null:
                    actualStatus = "";
                    break;
                  default:
                    actualStatus =
                      content.status[0].toUpperCase() +
                      content.status.slice(1).toLowerCase();
                    break;
                }
                let { reason, remark } = getReasonRemarkForFlist(
                  content.status,
                  content.reasons ?? [],
                  content.remarks ?? [],
                  userRoles
                );
                let account_status = content.account_status ?? "";
                let expiry_date = content.expiry_date ?? "";
                let return_due_date = content.return_due_date ?? "";
                return {
                  index_no: index + 1,
                  id: content._id,
                  frf_no: String(content.frf_id).padStart(6, "0"),
                  reg_corp_name: content.corporate_name,
                  status: actualStatus,
                  client_id: actualClientID,
                  reason: reason,
                  remarks: remark,
                  date: newDate,
                  time: newDate,
                  action: content._id,
                  backend_status: content.status,
                  return_due_date,
                  account_status,
                  expiry_date,
                };
              });
            } else {
              tableContents = [];
              // console.log("No request found")
            }
            // for specialist
          } else if (userRole === "validator") {
            // filter data
            if (FranchiseListData.length !== 0) {
              if (
                dashboardTileName === "Total Requests" ||
                dashboardSpecialistTileIndex === 0
              ) {
                tableContents = FranchiseListData;
              } else if (dashboardTileName === "Submitted Requests") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "SUBMITTED" || status === "RESUBMITTED"
                );
              } else if (dashboardTileName === "Validation In-Process") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "RECEIVED"
                );
              } else if (dashboardTileName === "Validation Complete") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "VALIDATED"
                );
              } else if (dashboardTileName === "For Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "FOR_APPROVAL" ||
                    status === "APPROVAL_IN_PROCESS" ||
                    status === "RESUBMITTED_TO_SUPERVISOR" ||
                    status === "FINAL_APPROVAL_IN_PROCESS" ||
                    status === "FINAL_APPROVAL"
                );
              } else if (dashboardTileName === "Returned to Sales User") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "RETURNED"
                );
              } else if (dashboardTileName === "Returned") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "RETURNED_TO_VALIDATOR"
                );
              } else if (dashboardTileName === "Expiring Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "APPROVED" &&
                      account_status !== "ACTIVE" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (dashboardTileName === "Expired Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      (status === "APPROVED" && expiryCounter < 0) ||
                      status === "EXPIRED"
                    );
                  }
                );
              } else {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === dashboardTileName.toUpperCase()
                );
              }

              // row
              tableRow = tableContents.map((content: any, index: number) => {
                let newDate = new Date(content.status_updated_at);
                let actualClientID =
                  content.client_id === "" || content.client_id === undefined
                    ? "--"
                    : content.client_id;
                let actualStatus: string;
                switch (content.status) {
                  case "RECEIVED":
                    actualStatus = "Validation In-Process";
                    break;
                  case "VALIDATED":
                    actualStatus = "Validation Complete";
                    break;
                  case "FOR_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "APPROVAL_IN_PROCESS":
                    actualStatus = "For Approval";
                    break;
                  case "FINAL_APPROVAL_IN_PROCESS":
                    actualStatus = "For Approval";
                    break;
                  case "FINAL_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "RESUBMITTED_TO_SUPERVISOR":
                    actualStatus = "Resubmitted To Supervisor";
                    break;
                  case "RETURNED_TO_VALIDATOR":
                    actualStatus = "Returned";
                    break;
                  case "RETURNED":
                    actualStatus = "Returned to Sales User";
                    break;
                  case "EXPIRING_FRANCHISE":
                    actualStatus = "Expiring Franchise";
                    break;
                  case null:
                    actualStatus = "";
                    break;
                  default:
                    actualStatus =
                      content.status[0].toUpperCase() +
                      content.status.slice(1).toLowerCase();
                    break;
                }
                let { reason, remark } = getReasonRemarkForFlist(
                  content.status,
                  content.reasons ?? [],
                  content.remarks ?? [],
                  userRoles
                );
                let account_status = content.account_status ?? "";
                let expiry_date = content.expiry_date ?? "";
                let return_due_date = content.return_due_date ?? "";

                return {
                  index_no: index + 1,
                  id: content._id,
                  frf_no: String(content.frf_id).padStart(6, "0"),
                  reg_corp_name: content.corporate_name,
                  brand_name: content.brand_name,
                  status_update: actualStatus,
                  client_id: actualClientID,
                  franchisee_name: content.applicant_name,
                  tin_number: content.tax_number,
                  reason: reason,
                  remarks: remark,
                  date: newDate,
                  time: newDate,
                  action: content._id,
                  backend_status: content.status,
                  return_due_date,
                  account_status,
                  expiry_date,
                };
              });
            } else {
              tableContents = [];
              // console.log("No request found")
            }
            // for supervisor
          } else if (userRole === "supervisor") {
            // filter data
            if (FranchiseListData.length !== 0) {
              if (
                dashboardTileName === "Total Requests" ||
                dashboardSupervisorTileIndex === 0
              ) {
                tableContents = FranchiseListData;
              } else if (dashboardTileName === "For Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "FOR_APPROVAL" ||
                    status === "RESUBMITTED_TO_SUPERVISOR"
                );
              } else if (dashboardTileName === "Approval In Process") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "APPROVAL_IN_PROCESS"
                );
              } else if (dashboardTileName === "For Final Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "FINAL_APPROVAL" ||
                    status === "FINAL_APPROVAL_IN_PROCESS"
                );
              } else if (dashboardTileName === "Returned") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "RETURNED_TO_VALIDATOR" ||
                    status === "RESUBMITTED" ||
                    status === "RECEIVED" ||
                    status === "RETURNED"
                );
              } else if (dashboardTileName === "Expiring Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "APPROVED" &&
                      account_status !== "ACTIVE" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (dashboardTileName === "Expired Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      (status === "APPROVED" && expiryCounter < 0) ||
                      status === "EXPIRED"
                    );
                  }
                );
              } else {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === dashboardTileName.toUpperCase()
                );
              }

              // row
              tableRow = tableContents.map((content: any, index: number) => {
                let newDate = new Date(content.status_updated_at);
                let actualClientID =
                  content.client_id === "" || content.client_id === undefined
                    ? "--"
                    : content.client_id;
                let actualStatus: string;
                switch (content.status) {
                  case "FOR_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "APPROVAL_IN_PROCESS":
                    actualStatus = "Approval In Process";
                    break;
                  case "FINAL_APPROVAL_IN_PROCESS":
                    actualStatus = "For Final Approval";
                    break;
                  case "FINAL_APPROVAL":
                    actualStatus = "For Final Approval";
                    break;
                  case "RESUBMITTED_TO_SUPERVISOR":
                    actualStatus = "Resubmitted To Supervisor";
                    break;
                  case null:
                    actualStatus = "";
                    break;
                  default:
                    if (
                      content.status === "RETURNED_TO_VALIDATOR" ||
                      content.status === "RESUBMITTED"
                    ) {
                      actualStatus = "Returned";
                    } else {
                      actualStatus =
                        content.status[0].toUpperCase() +
                        content.status.slice(1).toLowerCase();
                    }
                    break;
                }

                let { reason, remark } = getReasonRemarkForFlist(
                  content.status,
                  content.reasons ?? [],
                  content.remarks ?? [],
                  userRoles
                );
                let account_status = content.account_status ?? "";
                let expiry_date = content.expiry_date ?? "";
                let return_due_date = content.return_due_date ?? "";

                return {
                  index_no: index + 1,
                  id: content._id,
                  frf_no: String(content.frf_id).padStart(6, "0"),
                  reg_corp_name: content.corporate_name,
                  brand_name: content.brand_name,
                  status_update: actualStatus,
                  client_id: actualClientID,
                  franchisee_name: content.applicant_name,
                  tin_number: content.tax_number,
                  reason: reason,
                  remarks: remark,
                  date: newDate,
                  time: newDate,
                  action: content._id,
                  backend_status: content.status,
                  return_due_date,
                  account_status,
                  expiry_date,
                };
              });
            } else {
              tableContents = [];
              // console.log("No request found")
            }

            // for manager
          } else if (userRole === "manager") {
            // filter data
            if (FranchiseListData.length !== 0) {
              if (
                dashboardTileName === "Total Requests" ||
                dashboardManagerTileIndex === 0
              ) {
                tableContents = FranchiseListData;
              } else if (dashboardTileName === "For Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "FINAL_APPROVAL"
                );
              } else if (dashboardTileName === "Approval In Process") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "FINAL_APPROVAL_IN_PROCESS"
                );
              } else if (dashboardTileName === "Expiring Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "APPROVED" &&
                      account_status !== "ACTIVE" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (dashboardTileName === "Expired Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      (status === "APPROVED" && expiryCounter < 0) ||
                      status === "EXPIRED"
                    );
                  }
                );
              } else {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === dashboardTileName.toUpperCase()
                );
              }

              // row
              tableRow = tableContents.map((content: any, index: number) => {
                let newDate = new Date(content.status_updated_at);
                let actualClientID =
                  content.client_id === "" || content.client_id === undefined
                    ? "--"
                    : content.client_id;
                let actualStatus: string;
                switch (content.status) {
                  case "FINAL_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "FINAL_APPROVAL_IN_PROCESS":
                    actualStatus = "Approval in Process";
                    break;
                  case "EXPIRING_FRANCHISE":
                    actualStatus = "Expiring Franchise";
                    break;
                  case null:
                    actualStatus = "";
                    break;
                  default:
                    actualStatus =
                      content.status[0].toUpperCase() +
                      content.status.slice(1).toLowerCase();
                    break;
                }

                let { reason, remark } = getReasonRemarkForFlist(
                  content.status,
                  content.reasons ?? [],
                  content.remarks ?? [],
                  userRoles
                );
                let account_status = content.account_status ?? "";
                let expiry_date = content.expiry_date ?? "";
                let return_due_date = content.return_due_date ?? "";

                return {
                  index_no: index + 1,
                  id: content._id,
                  frf_no: String(content.frf_id).padStart(6, "0"),
                  reg_corp_name: content.corporate_name,
                  brand_name: content.brand_name,
                  status_update: actualStatus,
                  client_id: actualClientID,
                  franchisee_name: content.applicant_name,
                  tin_number: content.tax_number,
                  reason: reason,
                  remarks: remark,
                  date: newDate,
                  time: newDate,
                  action: content._id,
                  backend_status: content.status,
                  return_due_date,
                  account_status,
                  expiry_date,
                };
              });
            } else {
              tableContents = [];
              // console.log("No request found")
            }
          }
        } else {
          if (isSalesUser) {
            // filter data
            if (FranchiseListData.length !== 0) {
              if (
                dashboardTileName === "Total Requests" ||
                dashboardTileIndex === 0
              ) {
                tableContents = FranchiseListData;
              } else if (dashboardTileName === "Saved Requests") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "SAVED"
                );
              } else if (dashboardTileName === "For Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "FOR_APPROVAL" ||
                    status === "APPROVAL_IN_PROCESS" ||
                    status === "RETURNED_TO_VALIDATOR" ||
                    status === "RESUBMITTED_TO_SUPERVISOR" ||
                    status === "FINAL_APPROVAL" ||
                    status === "FINAL_APPROVAL_IN_PROCESS"
                );
              } else if (dashboardTileName === "Submitted") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "SUBMITTED" || status === "RESUBMITTED"
                );
              } else if (dashboardTileName === "Expiring Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "APPROVED" &&
                      account_status !== "ACTIVE" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (dashboardTileName === "Expired Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      (status === "APPROVED" && expiryCounter < 0) ||
                      status === "EXPIRED"
                    );
                  }
                );
              } else if (dashboardTileName === "Expiring Returned Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "RETURNED" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (dashboardTileName === "Expired Returned Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return status === "RETURNED" && expiryCounter < 0;
                  }
                );
              } else {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === dashboardTileName.toUpperCase()
                );
              }

              // row
              tableRow = tableContents.map((content: any, index: any) => {
                let newDate =
                  content.status === "SAVED"
                    ? new Date(content.updated_at)
                    : new Date(content.status_updated_at);
                let actualClientID =
                  content.client_id === "" || content.client_id === undefined
                    ? "--"
                    : content.client_id;
                let actualStatus: string;

                switch (content.status) {
                  case "FOR_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "APPROVAL_IN_PROCESS":
                    actualStatus = "For Approval";
                    break;
                  case "FINAL_APPROVAL_IN_PROCESS":
                    actualStatus = "For Approval";
                    break;
                  case "FINAL_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "RESUBMITTED_TO_SUPERVISOR":
                    actualStatus = "For Approval";
                    break;
                  case "RETURNED_TO_VALIDATOR":
                    actualStatus = "For Approval";
                    break;
                  case null:
                    actualStatus = "";
                    break;
                  default:
                    actualStatus =
                      content.status[0].toUpperCase() +
                      content.status.slice(1).toLowerCase();
                    break;
                }
                let { reason, remark } = getReasonRemarkForFlist(
                  content.status,
                  content.reasons ?? [],
                  content.remarks ?? [],
                  userRoles
                );
                let account_status = content.account_status ?? "";
                let expiry_date = content.expiry_date ?? "";
                let return_due_date = content.return_due_date ?? "";
                return {
                  index_no: index + 1,
                  id: content._id,
                  frf_no: String(content.frf_id).padStart(6, "0"),
                  reg_corp_name: content.corporate_name,
                  status: actualStatus,
                  client_id: actualClientID,
                  reason: reason,
                  remarks: remark,
                  date: newDate,
                  time: newDate,
                  action: content._id,
                  backend_status: content.status,
                  return_due_date,
                  account_status,
                  expiry_date,
                };
              });
            } else {
              tableContents = [];
              // console.log("No request found")
            }
            // for specialist
          } else if (isSpecialist) {
            // filter data
            if (FranchiseListData.length !== 0) {
              if (
                dashboardTileName === "Total Requests" ||
                dashboardTileIndex === 0
              ) {
                tableContents = FranchiseListData;
              } else if (dashboardTileName === "Submitted Requests") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "SUBMITTED" || status === "RESUBMITTED"
                );
              } else if (dashboardTileName === "Validation In-Process") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "RECEIVED"
                );
              } else if (dashboardTileName === "Validation Complete") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "VALIDATED"
                );
              } else if (dashboardTileName === "For Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "FOR_APPROVAL" ||
                    status === "APPROVAL_IN_PROCESS" ||
                    status === "RESUBMITTED_TO_SUPERVISOR" ||
                    status === "FINAL_APPROVAL_IN_PROCESS" ||
                    status === "FINAL_APPROVAL"
                );
              } else if (dashboardTileName === "Returned to Sales User") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "RETURNED"
                );
              } else if (dashboardTileName === "Returned") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "RETURNED_TO_VALIDATOR"
                );
              } else if (dashboardTileName === "Expiring Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "APPROVED" &&
                      account_status !== "ACTIVE" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (dashboardTileName === "Expired Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      (status === "APPROVED" && expiryCounter < 0) ||
                      status === "EXPIRED"
                    );
                  }
                );
              } else {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === dashboardTileName.toUpperCase()
                );
              }

              // row
              tableRow = tableContents.map((content: any, index: number) => {
                let newDate = new Date(content.status_updated_at);
                let actualClientID =
                  content.client_id === "" || content.client_id === undefined
                    ? "--"
                    : content.client_id;
                let actualStatus: string;
                switch (content.status) {
                  case "RECEIVED":
                    actualStatus = "Validation In-Process";
                    break;
                  case "VALIDATED":
                    actualStatus = "Validation Complete";
                    break;
                  case "FOR_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "APPROVAL_IN_PROCESS":
                    actualStatus = "For Approval";
                    break;
                  case "FINAL_APPROVAL_IN_PROCESS":
                    actualStatus = "For Approval";
                    break;
                  case "FINAL_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "RESUBMITTED_TO_SUPERVISOR":
                    actualStatus = "Resubmitted To Supervisor";
                    break;
                  case "RETURNED_TO_VALIDATOR":
                    actualStatus = "Returned";
                    break;
                  // case "EXPIRING_FRANCHISE":
                  //   actualStatus = "Expiring Franchise";
                  //   break;
                  case "RETURNED":
                    actualStatus = "Returned to Sales User";
                    break;
                  case null:
                    actualStatus = "";
                    break;
                  default:
                    actualStatus =
                      content.status[0].toUpperCase() +
                      content.status.slice(1).toLowerCase();
                    console.log(actualStatus, 12121);
                    break;
                }
                let { reason, remark } = getReasonRemarkForFlist(
                  content.status,
                  content.reasons ?? [],
                  content.remarks ?? [],
                  userRoles
                );
                let account_status = content.account_status ?? "";
                let expiry_date = content.expiry_date ?? "";
                let return_due_date = content.return_due_date ?? "";

                console.log(actualStatus, 32323);
                return {
                  index_no: index + 1,
                  id: content._id,
                  frf_no: String(content.frf_id).padStart(6, "0"),
                  reg_corp_name: content.corporate_name,
                  brand_name: content.brand_name,
                  status_update: actualStatus,
                  client_id: actualClientID,
                  franchisee_name: content.applicant_name,
                  tin_number: content.tax_number,
                  reason: reason,
                  remarks: remark,
                  date: newDate,
                  time: newDate,
                  action: content._id,
                  backend_status: content.status,
                  return_due_date,
                  account_status,
                  expiry_date,
                };
              });
            } else {
              tableContents = [];
              // console.log("No request found")
            }
            // for supervisor
          } else if (isSupervisor) {
            // filter data
            if (FranchiseListData.length !== 0) {
              if (
                dashboardTileName === "Total Requests" ||
                dashboardTileIndex === 0
              ) {
                tableContents = FranchiseListData;
              } else if (dashboardTileName === "For Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "FOR_APPROVAL" ||
                    status === "RESUBMITTED_TO_SUPERVISOR"
                );
              } else if (dashboardTileName === "Approval In Process") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "APPROVAL_IN_PROCESS"
                );
              } else if (dashboardTileName === "For Final Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "FINAL_APPROVAL" ||
                    status === "FINAL_APPROVAL_IN_PROCESS"
                );
              } else if (dashboardTileName === "Returned") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "RETURNED_TO_VALIDATOR" ||
                    status === "RESUBMITTED" ||
                    status === "RECEIVED" ||
                    status === "RETURNED"
                );
              } else if (dashboardTileName === "Expiring Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "APPROVED" &&
                      account_status !== "ACTIVE" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (dashboardTileName === "Expired Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      (status === "APPROVED" && expiryCounter < 0) ||
                      status === "EXPIRED"
                    );
                  }
                );
              } else {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === dashboardTileName.toUpperCase()
                );
              }

              // row
              tableRow = tableContents.map((content: any, index: number) => {
                let newDate = new Date(content.status_updated_at);
                let actualClientID =
                  content.client_id === "" || content.client_id === undefined
                    ? "--"
                    : content.client_id;
                let actualStatus: string;
                switch (content.status) {
                  case "FOR_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "APPROVAL_IN_PROCESS":
                    actualStatus = "Approval In Process";
                    break;
                  case "FINAL_APPROVAL_IN_PROCESS":
                    actualStatus = "For Final Approval";
                    break;
                  case "FINAL_APPROVAL":
                    actualStatus = "For Final Approval";
                    break;
                  case "RESUBMITTED_TO_SUPERVISOR":
                    actualStatus = "Resubmitted To Supervisor";
                    break;
                  case null:
                    actualStatus = "";
                    break;
                  default:
                    if (
                      content.status === "RETURNED_TO_VALIDATOR" ||
                      content.status === "RESUBMITTED"
                    ) {
                      actualStatus = "Returned";
                    } else {
                      actualStatus =
                        content.status[0].toUpperCase() +
                        content.status.slice(1).toLowerCase();
                    }
                    break;
                }

                let { reason, remark } = getReasonRemarkForFlist(
                  content.status,
                  content.reasons ?? [],
                  content.remarks ?? [],
                  userRoles
                );
                let account_status = content.account_status ?? "";
                let expiry_date = content.expiry_date ?? "";
                let return_due_date = content.return_due_date ?? "";

                return {
                  index_no: index + 1,
                  id: content._id,
                  frf_no: String(content.frf_id).padStart(6, "0"),
                  reg_corp_name: content.corporate_name,
                  brand_name: content.brand_name,
                  status_update: actualStatus,
                  client_id: actualClientID,
                  franchisee_name: content.applicant_name,
                  tin_number: content.tax_number,
                  reason: reason,
                  remarks: remark,
                  date: newDate,
                  time: newDate,
                  action: content._id,
                  backend_status: content.status,
                  return_due_date,
                  account_status,
                  expiry_date,
                };
              });
            } else {
              tableContents = [];
              // console.log("No request found")
            }

            // for manager
          } else if (isManager) {
            // filter data
            if (FranchiseListData.length !== 0) {
              if (
                dashboardTileName === "Total Requests" ||
                dashboardTileIndex === 0
              ) {
                tableContents = FranchiseListData;
              } else if (dashboardTileName === "For Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "FINAL_APPROVAL"
                );
              } else if (dashboardTileName === "Approval In Process") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "FINAL_APPROVAL_IN_PROCESS"
                );
              } else if (dashboardTileName === "Expiring Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "APPROVED" &&
                      account_status !== "ACTIVE" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (dashboardTileName === "Expired Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      (status === "APPROVED" && expiryCounter < 0) ||
                      status === "EXPIRED"
                    );
                  }
                );
              } else {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === dashboardTileName.toUpperCase()
                );
              }

              // row
              tableRow = tableContents.map((content: any, index: number) => {
                let newDate = new Date(content.status_updated_at);
                let actualClientID =
                  content.client_id === "" || content.client_id === undefined
                    ? "--"
                    : content.client_id;
                let actualStatus: string;
                switch (content.status) {
                  case "FINAL_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "FINAL_APPROVAL_IN_PROCESS":
                    actualStatus = "Approval in Process";
                    break;
                  case "EXPIRING_FRANCHISE":
                    actualStatus = "Expiring Franchise";
                    break;
                  case null:
                    actualStatus = "";
                    break;
                  default:
                    actualStatus =
                      content.status[0].toUpperCase() +
                      content.status.slice(1).toLowerCase();
                    break;
                }

                let { reason, remark } = getReasonRemarkForFlist(
                  content.status,
                  content.reasons ?? [],
                  content.remarks ?? [],
                  userRoles
                );
                let account_status = content.account_status ?? "";
                let expiry_date = content.expiry_date ?? "";
                let return_due_date = content.return_due_date ?? "";

                return {
                  index_no: index + 1,
                  id: content._id,
                  frf_no: String(content.frf_id).padStart(6, "0"),
                  reg_corp_name: content.corporate_name,
                  brand_name: content.brand_name,
                  status_update: actualStatus,
                  client_id: actualClientID,
                  franchisee_name: content.applicant_name,
                  tin_number: content.tax_number,
                  reason: reason,
                  remarks: remark,
                  date: newDate,
                  time: newDate,
                  action: content._id,
                  backend_status: content.status,
                  return_due_date,
                  account_status,
                  expiry_date,
                };
              });
            } else {
              tableContents = [];
              // console.log("No request found")
            }
          }
        }

        break;

      case `/franchising/franchise/${userRole}`:
        // for sales user
        if (isMultipleRole) {
          if (userRole === "encoder") {
            // filter data
            let searchItem = result;

            if (FranchiseListData.length !== 0) {
              FranchiseListData =
                searchResult && isSearching && searchItem !== ""
                  ? searchResult
                  : FranchiseListData;

              if (franchiseFilter === "All" || franchiseListFilterIndex === 0) {
                tableContents = FranchiseListData;
              } else if (franchiseFilter === "For Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "FOR_APPROVAL" ||
                    status === "APPROVAL_IN_PROCESS" ||
                    status === "RETURNED_TO_VALIDATOR" ||
                    status === "RESUBMITTED_TO_SUPERVISOR" ||
                    status === "FINAL_APPROVAL" ||
                    status === "FINAL_APPROVAL_IN_PROCESS"
                );
              } else if (franchiseFilter === "Submitted") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "SUBMITTED" || status === "RESUBMITTED"
                );
              } else if (franchiseFilter === "Expiring Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "APPROVED" &&
                      account_status !== "ACTIVE" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (franchiseFilter === "Expired Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      (status === "APPROVED" && expiryCounter < 0) ||
                      status === "EXPIRED"
                    );
                  }
                );
              } else if (franchiseFilter === "Expiring Returned Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "RETURNED" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (franchiseFilter === "Expired Returned Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return status === "RETURNED" && expiryCounter < 0;
                  }
                );
              } else {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === franchiseFilter.toUpperCase()
                );
              }

              // row
              tableRow = tableContents.map((content: any, index: number) => {
                let actualClientID =
                  content.client_id === "" || content.client_id === undefined
                    ? "--"
                    : content.client_id;
                let actualStatus: string;
                switch (content.status) {
                  case "FOR_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "APPROVAL_IN_PROCESS":
                    actualStatus = "Approval In Process";
                    break;
                  case "FINAL_APPROVAL":
                    actualStatus = "For Final Approval";
                    break;
                  case "FINAL_APPROVAL_IN_PROCESS":
                    actualStatus = "For Final Approval";
                    break;
                  case "RESUBMITTED_TO_SUPERVISOR":
                    actualStatus = "Resubmitted To Supervisor";
                    break;
                  case "RETURNED_TO_VALIDATOR":
                    actualStatus = "Returned";
                    break;
                  case null:
                    actualStatus = "";
                    break;
                  default:
                    actualStatus =
                      content.status[0].toUpperCase() +
                      content.status.slice(1).toLowerCase();
                    break;
                }

                let { reason, remark } = getReasonRemarkForFlist(
                  content.status,
                  content.reasons ?? [],
                  content.remarks ?? [],
                  userRoles
                );
                let account_status = content.account_status ?? "";
                let expiry_date = content.expiry_date ?? "";
                let return_due_date = content.return_due_date ?? "";

                return {
                  index_no: index + 1,
                  id: content._id,
                  frf_no: String(content.frf_id).padStart(6, "0"),
                  reg_corp_name: content.corporate_name,
                  status_update: actualStatus,
                  client_id: actualClientID,
                  reason: reason,
                  remarks: remark,
                  action: content._id,
                  app_uid: content.app_uid,
                  backend_status: content.status,
                  return_due_date,
                  account_status,
                  expiry_date,
                  date: content.created_at,
                  specialist_pm_user: content.specialist_pm_user ?? "",
                };
              });
            } else {
              tableContents = [];
              // console.log("No request found")
            }
          } else if (userRole === "validator") {

            // filter data
            let searchItem = result;
            if (FranchiseListData.length !== 0) {
              FranchiseListData =
                searchResult && isSearching && searchItem !== ""
                  ? searchResult
                  : FranchiseListData;
              if (franchiseFilter === "All" || franchiseListFilterIndex === 0) {
                tableContents = FranchiseListData;
              } else if (franchiseFilter === "Validation In-Process") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "RECEIVED"
                );
              } else if (franchiseFilter === "For Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "FOR_APPROVAL" ||
                    status === "APPROVAL_IN_PROCESS" ||
                    status === "RESUBMITTED_TO_SUPERVISOR" ||
                    status === "FINAL_APPROVAL" ||
                    status === "FINAL_APPROVAL_IN_PROCESS"
                );
              } else if (franchiseFilter === "Validation Complete") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "VALIDATED"
                );
              } else if (franchiseFilter === "Returned to Sales User") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "RETURNED"
                );
              } else if (franchiseFilter === "Returned") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "RETURNED_TO_VALIDATOR"
                );
              } else if (franchiseFilter === "Submitted") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "SUBMITTED" || status === "RESUBMITTED"
                );
              } else if (franchiseFilter === "Expiring Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "APPROVED" &&
                      account_status !== "ACTIVE" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (franchiseFilter === "Expired Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      (status === "APPROVED" && expiryCounter < 0) ||
                      status === "EXPIRED"
                    );
                  }
                );
              } else {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === franchiseFilter.toUpperCase()
                );
              }

              // row
              tableRow = tableContents.map((content: any, index: number) => {
                let actualClientID =
                  content.client_id === "" || content.client_id === undefined
                    ? "--"
                    : content.client_id;
                let actualStatus: string;
                switch (content.status) {
                  case "RECEIVED":
                    actualStatus = "Validation In-Process";
                    break;
                  case "VALIDATED":
                    actualStatus = "Validation Complete";
                    break;
                  case "FOR_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "FINAL_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "FINAL_APPROVAL_IN_PROCESS":
                    actualStatus = "For Approval";
                    break;
                  case "APPROVAL_IN_PROCESS":
                    actualStatus = "For Approval";
                    break;
                  case "RESUBMITTED_TO_SUPERVISOR":
                    actualStatus = "Resubmitted To Supervisor";
                    break;
                  case "RETURNED_TO_VALIDATOR":
                    actualStatus = "Returned";
                    break;
                  case "EXPIRING_FRANCHISE":
                    actualStatus = "Expiring Franchise";
                    break;
                  case "RETURNED":
                    actualStatus = "Returned to Sales User";
                    break;
                  case null:
                    actualStatus = "";
                    break;
                  default:
                    actualStatus =
                      content.status[0].toUpperCase() +
                      content.status.slice(1).toLowerCase();
                    break;
                }

                let { reason, remark } = getReasonRemarkForFlist(
                  content.status,
                  content.reasons ?? [],
                  content.remarks ?? [],
                  userRoles
                );
                let account_status = content.account_status ?? "";
                let expiry_date = content.expiry_date ?? "";
                let return_due_date = content.return_due_date ?? "";

                return {
                  index_no: index + 1,
                  id: content._id,
                  frf_no: String(content.frf_id).padStart(6, "0"),
                  reg_corp_name: content.corporate_name,
                  brand_name: content.brand_name,
                  status_update: actualStatus,
                  client_id: actualClientID,
                  franchisee_name: content.applicant_name,
                  tin_number: content.tax_number,
                  reason: reason,
                  remarks: remark,
                  action: content._id,
                  app_uid: content.app_uid,
                  backend_status: content.status,
                  return_due_date,
                  account_status,
                  expiry_date,
                  date: content.created_at,
                };
              });
            } else {
              tableContents = [];
              // console.log("No request found")
            }
            // for supervisor
          
          } else if (userRole === "supervisor") {

            // filter data
            let searchItem = result;
            if (FranchiseListData.length !== 0) {
              FranchiseListData =
                searchResult && isSearching && searchItem !== ""
                  ? searchResult
                  : FranchiseListData;
              if (franchiseFilter === "All" || franchiseListFilterIndex === 0) {
                tableContents = FranchiseListData;
              } else if (franchiseFilter === "For Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "FOR_APPROVAL" ||
                    status === "RESUBMITTED_TO_SUPERVISOR"
                );
              } else if (franchiseFilter === "For Final Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "FINAL_APPROVAL" ||
                    status === "FINAL_APPROVAL_IN_PROCESS"
                );
              } else if (franchiseFilter === "Approval In Process") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "APPROVAL_IN_PROCESS"
                );
              } else if (franchiseFilter === "Returned") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "RETURNED_TO_VALIDATOR" ||
                    status === "RESUBMITTED" ||
                    status === "RECEIVED" ||
                    status === "RETURNED"
                );
              } else if (franchiseFilter === "Expiring Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "APPROVED" &&
                      account_status !== "ACTIVE" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (franchiseFilter === "Expired Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      (status === "APPROVED" && expiryCounter < 0) ||
                      status === "EXPIRED"
                    );
                  }
                );
              } else {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === franchiseFilter.toUpperCase()
                );
              }

              // row
              tableRow = tableContents.map((content: any, index: number) => {
                // let newDate =
                //   content.status === "SAVED"
                //     ? new Date(content.updated_at)
                //     : new Date(content.status_updated_at);
                let actualClientID =
                  content.client_id === "" || content.client_id === undefined
                    ? "--"
                    : content.client_id;
                let actualStatus: string;
                switch (content.status) {
                  case "FOR_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "APPROVAL_IN_PROCESS":
                    actualStatus = "Approval In Process";
                    break;
                  case "FINAL_APPROVAL":
                    actualStatus = "For Final Approval";
                    break;
                  case "FINAL_APPROVAL_IN_PROCESS":
                    actualStatus = "For Final Approval";
                    break;
                  case "RESUBMITTED_TO_SUPERVISOR":
                    actualStatus = "Resubmitted To Supervisor";
                    break;
                  case null:
                    actualStatus = "";
                    break;
                  default:
                    if (
                      content.status === "RETURNED_TO_VALIDATOR" ||
                      content.status === "RESUBMITTED"
                    ) {
                      actualStatus = "Returned";
                    } else {
                      actualStatus =
                        content.status[0].toUpperCase() +
                        content.status.slice(1).toLowerCase();
                    }
                    break;
                }

                let { reason, remark } = getReasonRemarkForFlist(
                  content.status,
                  content.reasons ?? [],
                  content.remarks ?? [],
                  userRoles
                );
                let account_status = content.account_status ?? "";
                let expiry_date = content.expiry_date ?? "";
                let return_due_date = content.return_due_date ?? "";

                return {
                  index_no: index + 1,
                  id: content._id,
                  frf_no: String(content.frf_id).padStart(6, "0"),
                  reg_corp_name: content.corporate_name,
                  brand_name: content.brand_name,
                  status_update: actualStatus,
                  client_id: actualClientID,
                  franchisee_name: content.applicant_name,
                  tin_number: content.tax_number,
                  reason: reason,
                  remarks: remark,
                  action: content._id,
                  app_uid: content.app_uid,
                  backend_status: content.status,
                  return_due_date,
                  account_status,
                  expiry_date,
                  date: content.created_at,
                };
              });
            } else {
              tableContents = [];
              // console.log("No request found")
            }
            // for Manager
          
          } else if (userRole === "manager") {

            // filter data
            let searchItem = result;
            if (FranchiseListData.length !== 0) {
              FranchiseListData =
                searchResult && isSearching && searchItem !== ""
                  ? searchResult
                  : FranchiseListData;
              if (franchiseFilter === "All" || franchiseListFilterIndex === 0) {
                tableContents = FranchiseListData;
              } else if (franchiseFilter === "For Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "FINAL_APPROVAL"
                );
              } else if (franchiseFilter === "Approval In Process") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "FINAL_APPROVAL_IN_PROCESS"
                );
              } else if (franchiseFilter === "Expiring Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "APPROVED" &&
                      account_status !== "ACTIVE" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (franchiseFilter === "Expired Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      (status === "APPROVED" && expiryCounter < 0) ||
                      status === "EXPIRED"
                    );
                  }
                );
              } else {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === franchiseFilter.toUpperCase()
                );
              }

              // row
              tableRow = tableContents.map((content: any, index: number) => {
                let newDate =
                  content.status === "SAVED"
                    ? new Date(content.updated_at)
                    : new Date(content.status_updated_at);
                let actualClientID =
                  content.client_id === "" || content.client_id === undefined
                    ? "--"
                    : content.client_id;
                let actualStatus: string;
                switch (content.status) {
                  case "FINAL_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "FINAL_APPROVAL_IN_PROCESS":
                    actualStatus = "Approval in Process";
                    break;
                  case "EXPIRING_FRANCHISE":
                    actualStatus = "Expiring Franchise";
                    break;
                  case null:
                    actualStatus = "";
                    break;
                  default:
                    actualStatus =
                      content.status[0].toUpperCase() +
                      content.status.slice(1).toLowerCase();
                    break;
                }

                let { reason, remark } = getReasonRemarkForFlist(
                  content.status,
                  content.reasons ?? [],
                  content.remarks ?? [],
                  userRoles
                );
                let account_status = content.account_status ?? "";
                let expiry_date = content.expiry_date ?? "";
                let return_due_date = content.return_due_date ?? "";

                return {
                  index_no: index + 1,
                  id: content._id,
                  frf_no: String(content.frf_id).padStart(6, "0"),
                  reg_corp_name: content.corporate_name,
                  brand_name: content.brand_name,
                  status_update: actualStatus,
                  client_id: actualClientID,
                  franchisee_name: content.applicant_name,
                  tin_number: content.tax_number,
                  reason: reason,
                  remarks: remark,
                  action: content._id,
                  app_uid: content.app_uid,
                  backend_status: content.status,
                  return_due_date,
                  account_status,
                  expiry_date,
                  date: content.created_at,
                  time: newDate,
                };
              });
            } else {
              tableContents = [];
            }
          
          }
        } else {
          if (isSalesUser) {
            // filter data
            let searchItem = result;

            if (FranchiseListData.length !== 0) {
              FranchiseListData =
                searchResult && isSearching && searchItem !== ""
                  ? searchResult
                  : FranchiseListData;

              if (franchiseFilter === "All" || franchiseListFilterIndex === 0) {
                tableContents = FranchiseListData;
              } else if (franchiseFilter === "For Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "FOR_APPROVAL" ||
                    status === "APPROVAL_IN_PROCESS" ||
                    status === "RETURNED_TO_VALIDATOR" ||
                    status === "RESUBMITTED_TO_SUPERVISOR" ||
                    status === "FINAL_APPROVAL" ||
                    status === "FINAL_APPROVAL_IN_PROCESS"
                );
              } else if (franchiseFilter === "Submitted") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "SUBMITTED" || status === "RESUBMITTED"
                );
              } else if (franchiseFilter === "Expiring Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "APPROVED" &&
                      account_status !== "ACTIVE" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (franchiseFilter === "Expired Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      (status === "APPROVED" && expiryCounter < 0) ||
                      status === "EXPIRED"
                    );
                  }
                );
              } else if (franchiseFilter === "Expiring Returned Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "RETURNED" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (franchiseFilter === "Expired Returned Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return status === "RETURNED" && expiryCounter < 0;
                  }
                );
              } else {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === franchiseFilter.toUpperCase()
                );
              }

              // row
              tableRow = tableContents.map((content: any, index: number) => {
                let actualClientID =
                  content.client_id === "" || content.client_id === undefined
                    ? "--"
                    : content.client_id;
                let actualStatus: string;
                switch (content.status) {
                  case "FOR_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "APPROVAL_IN_PROCESS":
                    actualStatus = "Approval In Process";
                    break;
                  case "FINAL_APPROVAL":
                    actualStatus = "For Final Approval";
                    break;
                  case "FINAL_APPROVAL_IN_PROCESS":
                    actualStatus = "For Final Approval";
                    break;
                  case "RESUBMITTED_TO_SUPERVISOR":
                    actualStatus = "Resubmitted To Supervisor";
                    break;
                  case "RETURNED_TO_VALIDATOR":
                    actualStatus = "Returned";
                    break;
                  case null:
                    actualStatus = "";
                    break;
                  default:
                    actualStatus =
                      content.status[0].toUpperCase() +
                      content.status.slice(1).toLowerCase();
                    break;
                }

                let { reason, remark } = getReasonRemarkForFlist(
                  content.status,
                  content.reasons ?? [],
                  content.remarks ?? [],
                  userRoles
                );
                let account_status = content.account_status ?? "";
                let expiry_date = content.expiry_date ?? "";
                let return_due_date = content.return_due_date ?? "";

                return {
                  index_no: index + 1,
                  id: content._id,
                  frf_no: String(content.frf_id).padStart(6, "0"),
                  reg_corp_name: content.corporate_name,
                  status_update: actualStatus,
                  client_id: actualClientID,
                  reason: reason,
                  remarks: remark,
                  action: content._id,
                  app_uid: content.app_uid,
                  backend_status: content.status,
                  return_due_date,
                  account_status,
                  expiry_date,
                  date: content.created_at,
                  specialist_pm_user: content.specialist_pm_user ?? "",
                };
              });
            } else {
              tableContents = [];
              // console.log("No request found")
            }

            // for specialist
          } else if (isSpecialist) {
            // filter data
            let searchItem = result;
            if (FranchiseListData.length !== 0) {
              FranchiseListData =
                searchResult && isSearching && searchItem !== ""
                  ? searchResult
                  : FranchiseListData;
              if (franchiseFilter === "All" || franchiseListFilterIndex === 0) {
                tableContents = FranchiseListData;
              } else if (franchiseFilter === "Validation In-Process") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "RECEIVED"
                );
              } else if (franchiseFilter === "For Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "FOR_APPROVAL" ||
                    status === "APPROVAL_IN_PROCESS" ||
                    status === "RESUBMITTED_TO_SUPERVISOR" ||
                    status === "FINAL_APPROVAL" ||
                    status === "FINAL_APPROVAL_IN_PROCESS"
                );
              } else if (franchiseFilter === "Validation Complete") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "VALIDATED"
                );
              } else if (franchiseFilter === "Returned to Sales User") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "RETURNED"
                );
              } else if (franchiseFilter === "Returned") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "RETURNED_TO_VALIDATOR"
                );
              } else if (franchiseFilter === "Submitted") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "SUBMITTED" || status === "RESUBMITTED"
                );
              } else if (franchiseFilter === "Expiring Franchise") {
                console.log(franchiseFilter, 11111);
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "APPROVED" &&
                      account_status !== "ACTIVE" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (franchiseFilter === "Expired Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      (status === "APPROVED" && expiryCounter < 0) ||
                      status === "EXPIRED"
                    );
                  }
                );
              } else {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === franchiseFilter.toUpperCase()
                );
              }

              // row
              tableRow = tableContents.map((content: any, index: number) => {
                let actualClientID =
                  content.client_id === "" || content.client_id === undefined
                    ? "--"
                    : content.client_id;
                let actualStatus: string;
                switch (content.status) {
                  case "RECEIVED":
                    actualStatus = "Validation In-Process";
                    break;
                  case "VALIDATED":
                    actualStatus = "Validation Complete";
                    break;
                  case "FOR_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "FINAL_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "FINAL_APPROVAL_IN_PROCESS":
                    actualStatus = "For Approval";
                    break;
                  case "APPROVAL_IN_PROCESS":
                    actualStatus = "For Approval";
                    break;
                  case "RESUBMITTED_TO_SUPERVISOR":
                    actualStatus = "Resubmitted To Supervisor";
                    break;
                  case "RETURNED_TO_VALIDATOR":
                    actualStatus = "Returned";
                    break;
                  case "EXPIRING_FRANCHISE":
                    actualStatus = "Expiring Franchise";
                    break;
                  case "RETURNED":
                    actualStatus = "Returned to Sales User";
                    break;
                  case null:
                    actualStatus = "";
                    break;
                  default:
                    actualStatus =
                      content.status[0].toUpperCase() +
                      content.status.slice(1).toLowerCase();
                    break;
                }

                let { reason, remark } = getReasonRemarkForFlist(
                  content.status,
                  content.reasons ?? [],
                  content.remarks ?? [],
                  userRoles
                );
                let account_status = content.account_status ?? "";
                let expiry_date = content.expiry_date ?? "";
                let return_due_date = content.return_due_date ?? "";

                return {
                  index_no: index + 1,
                  id: content._id,
                  frf_no: String(content.frf_id).padStart(6, "0"),
                  reg_corp_name: content.corporate_name,
                  brand_name: content.brand_name,
                  status_update: actualStatus,
                  client_id: actualClientID,
                  franchisee_name: content.applicant_name,
                  tin_number: content.tax_number,
                  reason: reason,
                  remarks: remark,
                  action: content._id,
                  app_uid: content.app_uid,
                  backend_status: content.status,
                  return_due_date,
                  account_status,
                  expiry_date,
                  date: content.created_at,
                };
              });
            } else {
              tableContents = [];
              // console.log("No request found")
            }
            // for supervisor
          } else if (isSupervisor) {
            // filter data
            let searchItem = result;
            if (FranchiseListData.length !== 0) {
              FranchiseListData =
                searchResult && isSearching && searchItem !== ""
                  ? searchResult
                  : FranchiseListData;
              if (franchiseFilter === "All" || franchiseListFilterIndex === 0) {
                tableContents = FranchiseListData;
              } else if (franchiseFilter === "For Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "FOR_APPROVAL" ||
                    status === "RESUBMITTED_TO_SUPERVISOR"
                );
              } else if (franchiseFilter === "For Final Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "FINAL_APPROVAL" ||
                    status === "FINAL_APPROVAL_IN_PROCESS"
                );
              } else if (franchiseFilter === "Approval In Process") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "APPROVAL_IN_PROCESS"
                );
              } else if (franchiseFilter === "Returned") {
                tableContents = FranchiseListData.filter(
                  ({ status }) =>
                    status === "RETURNED_TO_VALIDATOR" ||
                    status === "RESUBMITTED" ||
                    status === "RECEIVED" ||
                    status === "RETURNED"
                );
              } else if (franchiseFilter === "Expiring Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "APPROVED" &&
                      account_status !== "ACTIVE" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (franchiseFilter === "Expired Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      (status === "APPROVED" && expiryCounter < 0) ||
                      status === "EXPIRED"
                    );
                  }
                );
              } else {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === franchiseFilter.toUpperCase()
                );
              }

              // row
              tableRow = tableContents.map((content: any, index: number) => {
                // let newDate =
                //   content.status === "SAVED"
                //     ? new Date(content.updated_at)
                //     : new Date(content.status_updated_at);
                let actualClientID =
                  content.client_id === "" || content.client_id === undefined
                    ? "--"
                    : content.client_id;
                let actualStatus: string;
                switch (content.status) {
                  case "FOR_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "APPROVAL_IN_PROCESS":
                    actualStatus = "Approval In Process";
                    break;
                  case "FINAL_APPROVAL":
                    actualStatus = "For Final Approval";
                    break;
                  case "FINAL_APPROVAL_IN_PROCESS":
                    actualStatus = "For Final Approval";
                    break;
                  case "RESUBMITTED_TO_SUPERVISOR":
                    actualStatus = "Resubmitted To Supervisor";
                    break;
                  case null:
                    actualStatus = "";
                    break;
                  default:
                    if (
                      content.status === "RETURNED_TO_VALIDATOR" ||
                      content.status === "RESUBMITTED"
                    ) {
                      actualStatus = "Returned";
                    } else {
                      actualStatus =
                        content.status[0].toUpperCase() +
                        content.status.slice(1).toLowerCase();
                    }
                    break;
                }

                let { reason, remark } = getReasonRemarkForFlist(
                  content.status,
                  content.reasons ?? [],
                  content.remarks ?? [],
                  userRoles
                );
                let account_status = content.account_status ?? "";
                let expiry_date = content.expiry_date ?? "";
                let return_due_date = content.return_due_date ?? "";

                return {
                  index_no: index + 1,
                  id: content._id,
                  frf_no: String(content.frf_id).padStart(6, "0"),
                  reg_corp_name: content.corporate_name,
                  brand_name: content.brand_name,
                  status_update: actualStatus,
                  client_id: actualClientID,
                  franchisee_name: content.applicant_name,
                  tin_number: content.tax_number,
                  reason: reason,
                  remarks: remark,
                  action: content._id,
                  app_uid: content.app_uid,
                  backend_status: content.status,
                  return_due_date,
                  account_status,
                  expiry_date,
                  date: content.created_at,
                };
              });
            } else {
              tableContents = [];
              // console.log("No request found")
            }
            // for Manager
          } else if (isManager) {
            // filter data
            let searchItem = result;
            if (FranchiseListData.length !== 0) {
              FranchiseListData =
                searchResult && isSearching && searchItem !== ""
                  ? searchResult
                  : FranchiseListData;
              if (franchiseFilter === "All" || franchiseListFilterIndex === 0) {
                tableContents = FranchiseListData;
              } else if (franchiseFilter === "For Approval") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "FINAL_APPROVAL"
                );
              } else if (franchiseFilter === "Approval In Process") {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === "FINAL_APPROVAL_IN_PROCESS"
                );
              } else if (franchiseFilter === "Expiring Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      status === "APPROVED" &&
                      account_status !== "ACTIVE" &&
                      expiryCounter <= 30 &&
                      expiryCounter >= 0
                    );
                  }
                );
              } else if (franchiseFilter === "Expired Franchise") {
                tableContents = FranchiseListData.filter(
                  ({ status, expiry_date, account_status }) => {
                    let actualExpiryDate = expiry_date
                      ? moment(expiry_date)
                      : moment().startOf("day");
                    let expiryCounter = actualExpiryDate.diff(
                      moment().startOf("day"),
                      "days"
                    );
                    return (
                      (status === "APPROVED" && expiryCounter < 0) ||
                      status === "EXPIRED"
                    );
                  }
                );
              } else {
                tableContents = FranchiseListData.filter(
                  ({ status }) => status === franchiseFilter.toUpperCase()
                );
              }

              // row
              tableRow = tableContents.map((content: any, index: number) => {
                let newDate =
                  content.status === "SAVED"
                    ? new Date(content.updated_at)
                    : new Date(content.status_updated_at);
                let actualClientID =
                  content.client_id === "" || content.client_id === undefined
                    ? "--"
                    : content.client_id;
                let actualStatus: string;
                switch (content.status) {
                  case "FINAL_APPROVAL":
                    actualStatus = "For Approval";
                    break;
                  case "FINAL_APPROVAL_IN_PROCESS":
                    actualStatus = "Approval in Process";
                    break;
                  case "EXPIRING_FRANCHISE":
                    actualStatus = "Expiring Franchise";
                    break;
                  case null:
                    actualStatus = "";
                    break;
                  default:
                    actualStatus =
                      content.status[0].toUpperCase() +
                      content.status.slice(1).toLowerCase();
                    break;
                }

                let { reason, remark } = getReasonRemarkForFlist(
                  content.status,
                  content.reasons ?? [],
                  content.remarks ?? [],
                  userRoles
                );
                let account_status = content.account_status ?? "";
                let expiry_date = content.expiry_date ?? "";
                let return_due_date = content.return_due_date ?? "";

                return {
                  index_no: index + 1,
                  id: content._id,
                  frf_no: String(content.frf_id).padStart(6, "0"),
                  reg_corp_name: content.corporate_name,
                  brand_name: content.brand_name,
                  status_update: actualStatus,
                  client_id: actualClientID,
                  franchisee_name: content.applicant_name,
                  tin_number: content.tax_number,
                  reason: reason,
                  remarks: remark,
                  action: content._id,
                  app_uid: content.app_uid,
                  backend_status: content.status,
                  return_due_date,
                  account_status,
                  expiry_date,
                  date: content.created_at,
                  time: newDate,
                };
              });
            } else {
              tableContents = [];
            }
          }
        }

        break;

      default:
        break;
    }
  } else {
    console.log("ERROR");
  }

  const handleDefaultExpiry = (expiry_date: any) => {
    switch (details.channel_of_request) {
      case "New Business Associate":
      case "New Business Agency":
        expiry_date.setMonth(expiry_date.getMonth() + 6);
        break;
      case "Business Development Officer":
        expiry_date.setMonth(expiry_date.getMonth() + 6);
        break;
      default:
        expiry_date.setDate(expiry_date.getDate() + 60);
        break;
    }
    return moment(expiry_date).format("MM/DD/YYYY");
  };

  const handleViewClick = (id: string) => {
    if (!sessionExpired) {
      setOpenModalView(true);
      dispatch(setModalContent({ title: "", p: "", error: false }));
      dispatch(fetchFranchise(id, userRole));
    }
  };

  const handleNewClick = (e: any) => {
    e.preventDefault();
    setOpenModal(false);
    dispatch(goBackAndCreateNew());
    dispatch(resetState());
    history.push("/franchising/franchise/create");
  };
  const handleBackToDashboard = (e: any) => {
    e.preventDefault();
    setOpenModal(false);
    dispatch(goBackAndCreateNew());
    dispatch(resetState());
    history.push(`/franchising/dashboard/${userRole}`);
  };

  const handleOpenConfirm = (e: any, action: string) => {
    e.preventDefault();
    let h1 = "";
    let p = "";
    let text = [] as string[];
    const effectivity_date: any = moment(new Date()).format("MM/DD/YYYY");
    dispatch(clearValidationModalContent());
    setParam({
      reason: "",
      remarks: "",
      due_date: "",
      effectivity_date,
      expiry_date: defaultExpiry,
    });
    setParamError({
      reason: "",
      remarks: "",
      due_date: "",
      effectivity_date: "",
      expiry_date: "",
    });
    switch (action) {
      case "return":
        h1 = "Return Request";
        p = "";
        text = ["Cancel", "Return"];
        dispatch(fetchReturnReasonList());
        break;
      case "return_validator":
        h1 = "Return Request";
        p = "";
        text = ["Cancel", "Return"];
        dispatch(fetchReturnReasonList());
        break;
      case "validate":
        h1 = "Validating Franchise Request";
        p = "";
        dispatch(updateFranchiseRequest(details._id, "validate", userRole));
        break;
      case "endorse":
        h1 = "Endorse Request";
        p = "";
        text = ["No", "Yes"];
        break;
      case "reendorse":
        h1 = "Endorse Request";
        p = "";
        text = ["No", "Yes"];
        break;
      case "approve":
        h1 = "Approve Request";
        p = "";
        text = ["No", "Yes"];
        if (
          disapproveReasonList.length < 1 &&
          details.duplicate &&
          details.duplicate.length > 0
        ) {
          dispatch(fetchDisapproveReasonList());
        }
        break;
      case "disapprove":
        h1 = "Disapprove Request";
        p = "";
        text = ["Cancel", "Disapprove"];
        if (!disapproveReasonList.length) {
          dispatch(fetchDisapproveReasonList());
        }
        break;
      case "cancel":
        h1 = "Cancel Request?";
        p = "Are you sure you want to cancel your Franchise Request?";
        text = ["No", "Yes"];
        break;
      case "submit":
        h1 = "Submit Request?";
        p =
          "Are you sure you want to submit your Franchise Request? This will be subject for review by the Franchising Specialist.";
        text = ["No", "Yes"];
        break;
      case "resubmit":
        h1 = "Submit Request?";
        p =
          "Are you sure you want to submit your Franchise Request? This will be subject for review by the Franchising Specialist.";
        text = ["No", "Yes"];
        break;
    }
    setConfirmValues({ action, h1, p, text });
    setOpenModal(true);
    setOpenModalView(false);
    setModalButton("Confirm");
  };

  // console.log("Sample Return Data: ", param)

  const handleOpenConfirmed = (e: any, action: string) => {
    e.preventDefault();
    let h1 = "";
    let p = "";
    let text = [] as string[];
    const { reason, due_date, effectivity_date, expiry_date, remarks } = param;
    switch (action) {
      case "return":
        const validDueDate =
          due_date &&
          moment(due_date).isValid() &&
          moment(due_date) >= moment(startOfToday());
        if (reason && validDueDate) {
          dispatch(
            updateFranchiseRequest(details._id, "return", userRole, param)
          );
          text = ["Back to Dashboard"];
        } else {
          setParamError({
            ...paramError,
            reason: reason ? "" : "This field is required.",
            due_date: validDueDate ? "" : "This field is required.",
          });
          return;
        }
        break;
      case "return_validator":
        if (reason) {
          dispatch(
            updateFranchiseRequest(details._id, "return", userRole, param)
          );
          text = ["Back to Dashboard"];
        } else {
          setParamError({
            ...paramError,
            reason: reason ? "" : "This field is required.",
          });
          return;
        }
        break;
      case "endorse":
        dispatch(
          updateFranchiseRequest(details._id, "endorse", userRole, param)
        );
        text = ["Back to Dashboard"];
        break;
      case "reendorse":
        dispatch(
          updateFranchiseRequest(details._id, "reendorse", userRole, param)
        );
        text = ["Back to Dashboard"];
        break;
      case "approve":
        if (isManager) {
          console.log("YES btn: ", disapproveList);

          const validReason = !(
            disapproveList.filter(
              ({ reason_for_disapprove }) => !reason_for_disapprove
            ).length > 0
          );

          console.log(
            "YES btn FILTER reason: ",
            disapproveList.filter(
              ({ reason_for_disapprove }) => reason_for_disapprove
            )
          );
          console.log(
            "YES btn FILTER !reason: ",
            disapproveList.filter(
              ({ reason_for_disapprove }) => !reason_for_disapprove
            ).length
          );
          console.log(
            "YES btn DUPLICATE?: ",
            details.duplicate && details.duplicate.length > 0
          );

          const validEffectivityDate =
            effectivity_date &&
            moment(effectivity_date).isValid() &&
            moment(effectivity_date) >= moment(startOfToday()) &&
            moment(expiry_date) >= moment(effectivity_date);
          const validExpiryDate =
            expiry_date &&
            moment(expiry_date).isValid() &&
            moment(expiry_date) >= moment(startOfToday()) &&
            moment(expiry_date) >= moment(effectivity_date);

          if (details.duplicate && details.duplicate.length > 0) {
            if (validEffectivityDate && validExpiryDate && validReason) {
              let disapprove = disapproveList.map((content: any) => {
                const { id, reason_for_disapprove } = content;
                return {
                  id,
                  reason_for_disapprove,
                };
              });
              let requestor = details.applicant_name;
              dispatch(
                updateFranchiseRequest(
                  details._id,
                  action,
                  userRole,
                  { disapprove, effectivity_date, expiry_date, remarks },
                  requestor
                )
              );
            } else {
              setParamError({
                ...paramError,
                effectivity_date: validEffectivityDate
                  ? ""
                  : "This field is required.",
                expiry_date: validExpiryDate ? "" : "This field is required.",
              });
              const disapprove = disapproveList.map((content: any) => {
                const { id, name, reason_for_disapprove } = content;
                return {
                  id,
                  name,
                  reason_for_disapprove,
                  error: reason_for_disapprove ? "" : "This field is required.",
                };
              });
              setDisapproveList(disapprove);
              return;
            }
          } else {
            if (validEffectivityDate && validExpiryDate) {
              dispatch(
                updateFranchiseRequest(details._id, action, userRole, {
                  effectivity_date,
                  expiry_date,
                  remarks,
                })
              );
            } else {
              setParamError({
                ...paramError,
                effectivity_date: validEffectivityDate
                  ? ""
                  : "This field is required.",
                expiry_date: validExpiryDate ? "" : "This field is required.",
              });
              return;
            }
            // return;
          }
          // if(validEffectivityDate && validExpiryDate && validReason){
          //   if(details.duplicate && details.duplicate.length > 0){
          //     let disapprove = disapproveList.map((content:any) => {
          //       const {id,reason_for_disapprove} = content
          //       return {
          //         id,
          //         reason_for_disapprove,
          //       }
          //     });
          //     let requestor = details.applicant_name;
          //     dispatch(updateFranchiseRequest(details._id, action, { disapprove,effectivity_date, expiry_date, remarks },requestor));
          //   }else{
          //     dispatch(updateFranchiseRequest(details._id, action, { effectivity_date, expiry_date, remarks}));
          //   }
          // }else{
          //   setParamError({
          //     ...paramError,
          //     effectivity_date: validEffectivityDate ? "" : "This field is required.",
          //     expiry_date: validExpiryDate ? "" : "This field is required.",
          //   });
          //   const disapprove = disapproveList.map((content:any) => {
          //     const {id,name,reason_for_disapprove} = content
          //     return {
          //       id,
          //       name,
          //       reason_for_disapprove,
          //       error:reason_for_disapprove? "":"This field is required."
          //     }
          //   });
          //   setDisapproveList(disapprove)
          //   return;
          // }
        } else {
          dispatch(
            updateFranchiseRequest(details._id, action, userRole, { remarks })
          );
        }
        text = ["Back to Dashboard"];
        break;
      case "disapprove":
        if (reason) {
          dispatch(
            updateFranchiseRequest(details._id, "disapprove", userRole, {
              reason,
              remarks,
            })
          );
          text = ["Back to Dashboard"];
        } else {
          setParamError({
            ...paramError,
            reason: "This field is required.",
          });
          return;
        }
        break;
      case "cancel":
        dispatch(updateFranchiseRequest(details._id, "cancel", userRole));
        text = ["Okay"];
        break;
      case "submit":
        dispatch(addFranchise(details));
        text = ["Back to Dashboard", "Create New"];
        break;
      case "resubmit":
        dispatch(
          updateFranchiseRequest(details._id, "resubmit", userRole, details)
        );
        text = ["Back to Dashboard", "Create New"];
        break;
    }
    setConfirmValues({ action, h1, p, text });
    setModalButton("Yes");
  };

  const handleClose = (e: any) => {
    e.preventDefault();
    setOpenModal(false);
    setOpenModalView(validationContent.error);
  };

  const handleInputChange = (e: any) => {
    e.preventDefault();
    let { name, value } = e.target;
    setParamError({ ...paramError, [name]: "" });
    setParam({ ...param, [name]: value });
  };

  const handleDueDate = (e: any, due_date: any) => {
    let replacedDate =
      typeof due_date === "string"
        ? due_date.replace(/\//g, "").replace(/_/g, "")
        : "";
    if (!pattern.test(replacedDate) && replacedDate.length <= 8) {
      let validDate =
        moment(due_date).isValid() &&
        moment(due_date) >= moment(startOfToday());
      setParam({ ...param, due_date });
      setParamError({
        ...paramError,
        due_date: validDate || !due_date ? "" : "Invalid Date Format",
      });
    } else {
      setParamError({ ...paramError, due_date: "Invalid Date Format" });
    }
  };

  const handleEffectivityDate = (e: any, effectivity_date: any) => {
    let replacedDate =
      typeof effectivity_date === "string"
        ? effectivity_date.replace(/\//g, "").replace(/_/g, "")
        : "";
    if (!pattern.test(replacedDate) && replacedDate.length <= 8) {
      let validExpiryDate =
        !param.expiry_date ||
        (moment(param.expiry_date).isValid() &&
          moment(param.expiry_date) >= moment(startOfToday()));
      let validDate =
        moment(effectivity_date).isValid() &&
        moment(effectivity_date) >= moment(startOfToday());

      if (validDate) {
        let expiry_date = handleDefaultExpiry(new Date(effectivity_date));
        setParam({ ...param, effectivity_date, expiry_date });
      } else {
        setParam({ ...param, effectivity_date });
      }

      setParamError({
        ...paramError,
        effectivity_date:
          validDate || !effectivity_date ? "" : "Invalid Date Format",
        expiry_date: validExpiryDate ? "" : "Invalid Date Format",
      });
    } else {
      setParamError({ ...paramError, effectivity_date: "Invalid Date Format" });
    }
  };

  const handleExpiryDate = (e: any, expiry_date: any) => {
    let replacedDate =
      typeof expiry_date === "string"
        ? expiry_date.replace(/\//g, "").replace(/_/g, "")
        : "";
    if (!pattern.test(replacedDate) && replacedDate.length <= 8) {
      let validEffectivityDate =
        !param.effectivity_date ||
        (moment(param.effectivity_date).isValid() &&
          moment(param.effectivity_date) >= moment(startOfToday()) &&
          moment(expiry_date) >= moment(param.effectivity_date));
      let validDate =
        moment(expiry_date).isValid() &&
        moment(expiry_date) >= moment(startOfToday()) &&
        (!param.effectivity_date ||
          !moment(param.effectivity_date).isValid() ||
          moment(expiry_date) >= moment(param.effectivity_date));
      setParam({ ...param, expiry_date });
      setParamError({
        ...paramError,
        expiry_date: validDate || !expiry_date ? "" : "Invalid Date Format",
        effectivity_date: validEffectivityDate ? "" : "Invalid Date Format",
      });
    } else {
      setParamError({ ...paramError, expiry_date: "Invalid Date Format" });
    }
  };

  let remarksGrid = (
    <Grid className="vni-flex vni-flex-col vni-mt-3">
      <Typography>Remarks (Optional)</Typography>
       <TextField
         data-cy="remarks-textarea"
         className="CustomInput"
         variant="outlined"
         name="remarks"
         id="remarks"
         label=""
         multiline
         maxRows={3}
         rows={3}
         onChange={handleInputChange}
       />
    </Grid>
  );

  let reasonGrid = (
    <Grid className="vni-flex vni-mt-3">
      <Grid className="vni-w-full">
        <label htmlFor="">
          Reason <span className="vni-text-red-500">*</span>
        </label>
        <Select
          data-cy="select-reason"
          value={param.reason}
          className={
            "CustomSelect vni-w-full " + (paramError.reason ? "error" : "")
          }
          name="reason"
          onChange={handleInputChange}
          required={true}
          error={paramError.reason !== ""}
        >
          {(confirmValues.action &&
            confirmValues.action.includes("return") &&
            returnReasonList.map((value: any, i) => (
              <MenuItem
                key={i}
                value={value.reason_for_return}
                data-cy={`select-reason-return-${i}`}
              >
                {value.reason_for_return}
              </MenuItem>
            ))) ||
            (confirmValues.action &&
              confirmValues.action.includes("disapprove") &&
              disapproveReasonList.map((value: any, i) => (
                <MenuItem
                  key={i}
                  value={value.reason_for_disapprove}
                  data-cy={`select-reason-disapprove-${i}`}
                >
                  {value.reason_for_disapprove}
                </MenuItem>
              )))}
        </Select>
      </Grid>
    </Grid>
  );

  //RESUBMIT

  // let resubmitToSpecialist = (
  //   <>
  //     <Typography className="vni-mb-5">
  //      Are you sure you want to submit your Franchise Request? <br/>
  //      This will be subject for review by the Franchising Specialist.
  //     </Typography>
  //     <Grid className="vni-flex vni-flex-col vni-mt-3">
  //         <Typography>Remarks (Optional)</Typography>
  //         <TextField
  //           data-cy="remarks-textarea"
  //           className="CustomInput"
  //           variant="outlined"
  //           name="resubmit_remarks"
  //           id="remarks"
  //           label=""
  //           multiline
  //           rowsMax={3}
  //           rows={3}
  //           onChange={handleInputRemark}
  //         />
  //     </Grid>
  // </>
  // )

  let returnToSalesContent = (
    <>
      <Typography className="vni-mb-5">
        Do you want to return Franchise Request to sales user?
      </Typography>
      {reasonGrid}
      {remarksGrid}
      <Grid container className="vni-flex vni-flex-col vni-mt-3">
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DatePicker
            label={
              <label htmlFor="">
                Due Date <span className="vni-text-red-500">*</span>
              </label>
            }
            format="MM/dd/yyyy"
            minDate={new Date()}
            value={param.due_date ? new Date(param.due_date) : null}
            onChange={handleDueDate}
            slotProps={{
              textField: {
                id: "return-date",
                name: "date",
                placeholder: "mm/dd/yyyy",
                error: paramError.due_date !== "",
                className: "CustomInput",
                inputProps: {
                  'data-cy': "return-date"
                }
              }
            }}
          />
        </LocalizationProvider>
      </Grid>
    </>
  );

  let returnToValidatorContent = (
    <>
      <Typography className="vni-mb-5">
        Do you want to return this Franchise Request?
      </Typography>
      {reasonGrid}
      {remarksGrid}
    </>
  );

  let endorseContent = (
    <>
      <Typography className="vni-mb-5">
        The Franchise Request has been validated. Do you want to endorse it to
        the approvers?
      </Typography>
      {remarksGrid}
    </>
  );

  // let resubmitContent = (
  //   <>
  //     <Typography className="vni-mb-5">
  //       Are you sure you want to submit your Franchise Request? This will be
  //       subject for review by the Franchising Specialist.
  //     </Typography>
  //     {remarksGrid}
  //   </>
  // );

  const handleDuplicateReason = (e: any, full_name: string) => {
    e.preventDefault();
    let { value } = e.target;
    let _id = e.target.name;
    let duplicateList = disapproveList.map((content: any) => {
      const { id, name, reason_for_disapprove, error } = content;
      return {
        id,
        name: _id === id ? full_name : name,
        reason_for_disapprove: _id === id ? value : reason_for_disapprove,
        error: _id === id ? "" : error,
      };
    });
    setDisapproveList(duplicateList);
  };

  const handleCloseView = () => {
    if (openCompareFranchise) {
      setCompareFranchiseId("");
      setOpenCompareFranchise(false);
      setOpenOtherSalesUser(true);
    } else if (openOtherSalesUser) {
      setOpenOtherSalesUser(false);
      if (!openModalView) {
        setOpenModalView(true);
        setOpenModal(false);
      }
    } else {
      setOpenModalView(false);
    }
    //@ts-ignore
    dispatch(setValidatorUploaded(false));
    dispatch(fetchFranchises(userRole));
  };

  const handleCompareButtonAction = () => {
    dispatch(setModalContent({ title: "", p: "", error: false }));
    setCompareFranchiseId("");
    setOpenCompareFranchise(false);
    setOpenOtherSalesUser(false);
    setOpenModalView(false);
  };

  const handleViewCompareFranchise = (id: string) => {
    dispatch(setModalContent({ title: "", p: "", error: false }));
    setOpenCompareFranchise(true);
    setOpenOtherSalesUser(false);
    setCompareFranchiseId(id);
  };

  const handleViewOtherFR = (e: any) => {
    e.preventDefault();
    if (!disapproveReasonList.length) {
      dispatch(fetchDisapproveReasonList());
    }
    setOpenOtherSalesUser(true);
  };

  const handlCompareTable = (e: any) => {
    e.preventDefault();
    if (!disapproveReasonList.length) {
      dispatch(fetchDisapproveReasonList());
    }
    setOpenModal(false);
    setOpenModalView(true);
    setOpenOtherSalesUser(true);
  };

  let approveSupervisor = (
    <>
      <Typography className="vni-mb-5" style={{ maxWidth: 480 }}>
        Are you sure you want to approve the request?
      </Typography>
      {remarksGrid}
    </>
  );

  let isRequestPlural = details.duplicate && details.duplicate.length > 1;
  let isManagerDuplicate = details.duplicate && details.duplicate.length > 0;

  let approveDuplicateDesc = (
    <>
      <Typography className="vni-mb-5" style={{ maxWidth: 480 }}>
        {(details &&
          details.is_refranchise &&
          details.is_refranchise.toLowerCase() === "no" &&
          "Are you sure you want to approve the request by ") ||
          (details &&
            details.is_refranchise &&
            details.is_refranchise.toLowerCase() === "yes" &&
            details.client_id &&
            "Are you sure you want to approve the returning franchise request by ")}
        <strong>{details && " " + details.applicant_name}</strong> ?
        {(details &&
          details.is_refranchise &&
          details.is_refranchise.toLowerCase() === "no" &&
          " This will generate a client ID.") ||
          (details &&
            details.is_refranchise &&
            details.is_refranchise.toLowerCase() === "yes" &&
            details.client_id &&
            ` This will re-use its previous Client ID (${details.client_id}).`)}
        <br />
        The following {isRequestPlural ? "requests" : "request"} will be
        automatically disapproved:
      </Typography>
      {isManagerDuplicate && (
        <Typography className="vni-mb-5">
          Click{" "}
          <NavLink
            to={"#"}
            onClick={handlCompareTable}
            style={{ display: "inline" }}
          >
            here
          </NavLink>{" "}
          to compare the duplicate franchise requests.
        </Typography>
      )}
      <Grid className="custom-scroll">
        {disapproveList.map((option: any, index: any) => (
          <Grid className="vni-flex vni-my-5" key={index}>
            <Typography className="vni-pl-4 vni-font-bold static-name-width">
              {option.name}
            </Typography>
            <Grid className="vni-w-3/5 vni-pl-4">
              <label htmlFor="">
                Reason for Disapproval{" "}
                <span className="vni-text-red-500">*</span>
              </label>
              <Select
                value={option.reason_for_disapprove ?? ""}
                className={"CustomSelect " + (option.error ? "error" : "")}
                name={option.id}
                onChange={(e) => handleDuplicateReason(e, option.name)}
                required={true}
                error={option.error !== ""}
                data-cy="select-reason-disapproval-normal-multiple"
              >
                {disapproveReasonList.map((value: any, i) => (
                  <MenuItem
                    key={i}
                    value={value.reason_for_disapprove}
                    data-cy={`select-reason-disapproval-normal-multiple-${i}`}
                  >
                    {value.reason_for_disapprove}
                  </MenuItem>
                ))}
              </Select>
            </Grid>
          </Grid>
        ))}
      </Grid>
    </>
  );

  let approveSingleDesc = (
    <>
      <Typography className="vni-mb-5" style={{ maxWidth: 480 }}>
        {(details &&
          details.is_refranchise &&
          details.is_refranchise.toLowerCase() === "no" &&
          "Are you sure you want to approve the request? This will generate a Client ID.") ||
          (details &&
            details.is_refranchise &&
            details.is_refranchise.toLowerCase() === "yes" &&
            details.client_id &&
            `Are you sure you want to approve the returning franchise request? This will re-use its previous Client ID (${details.client_id}).`)}
      </Typography>
    </>
  );

  let approveContent = (
    <>
      {(isManagerDuplicate && approveDuplicateDesc) || approveSingleDesc}
      {
        <Grid container className="vni-flex vni-mt-3 vni-justify-between">
          <Grid item xs={6}>
            <Grid className="vni-datepicker">
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label={
                    <label htmlFor="">
                      Franchise Effectivity Date{" "}
                      <span className="vni-text-red-500">*</span>
                    </label>
                  }
                  format="MM/dd/yyyy"
                  minDate={new Date()}
                  value={
                    param.effectivity_date
                      ? new Date(param.effectivity_date)
                      : null
                  }
                  onChange={handleEffectivityDate}
                  slotProps={{
                    textField: {
                      id: "effectivity-date",
                      name: "effectivity_date",
                      placeholder: "mm/dd/yyyy",
                      error: paramError.effectivity_date !== "",
                      className: "CustomInput",
                      inputProps: {
                        'data-cy': "effectivity-date"
                      }
                    }
                  }}
                />
              </LocalizationProvider>
            </Grid>
          </Grid>
          <Grid item xs={6}>
            <Grid className="vni-datepicker">
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label={
                    <label htmlFor="">
                      Franchise Expiry Date{" "}
                      <span className="vni-text-red-500">*</span>
                    </label>
                  }
                  format="MM/dd/yyyy"
                  minDate={
                    param.effectivity_date
                      ? new Date(param.effectivity_date)
                      : new Date()
                  }
                  value={param.expiry_date ? new Date(param.expiry_date) : null}
                  onChange={handleExpiryDate}
                  slotProps={{
                    textField: {
                      id: "expiry-date",
                      name: "expiry_date",
                      placeholder: "mm/dd/yyyy",
                      error: paramError.expiry_date !== "",
                      className: "CustomInput",
                      inputProps: {
                        'data-cy': "expiry-date"
                      }
                    }
                  }}
                />
              </LocalizationProvider>
            </Grid>
          </Grid>
        </Grid>
      }
      {remarksGrid}
    </>
  );

  let rejectedContent = (
    <>
      <Typography className="vni-mb-5">
        Are you sure you want to disapprove this franchise request? <br />
        Sales user will be notified of its status and reason for disapproval.
      </Typography>
      {reasonGrid}
      {remarksGrid}
    </>
  );

  // For breakline
  let withBreakline: any;
  let validateCondition =
    !validationContent.error &&
    confirmValues.p !==
      "The franchise request does not have any duplicates or errors.";
  if (confirmValues.action === "validate" && confirmValues.p !== "") {
    let replace = confirmValues.p.replace(/\./g, "/");
    let newContent = replace.split("/").map((test) => {
      return (
        <>
          {`${test}.`}
          <br />
        </>
      );
    });
    newContent.pop();

    if (validateCondition) {
      withBreakline = (
        <>
          {newContent}
          <br />
          Do you still want to endorse it?
        </>
      );
    } else {
      withBreakline = newContent;
    }
  }

  const successResponse =
    validationContent.title !== "" &&
    validationContent.p !== "" &&
    !validationContent.error;
  const approveBody =
    userRole === "manager" ? approveContent : approveSupervisor;
  // tableRow = tableRow.filter(row => {
  // 	console.log(new Date(dateFrom))
  // })
  let dateFromDate = moment(dateFrom).set({
    hour: 0,
    minute: 0,
    second: 0,
    millisecond: 0,
  });
  let dateToDate = moment(dateTo).set({
    hour: 23,
    minute: 59,
    second: 59,
    millisecond: 59,
  });
  if (applyFilter) {
    tableRow = tableRow.filter((rowDatum: any) => {
      const momentDate = moment(rowDatum.date);

      if (momentDate.isBetween(dateFromDate, dateToDate)) {
        return true;
      }

      return false;
    });
  }

  return (
    <div className="vni-table">
      <div className="vni-flex-grow" style={{ maxWidth: "100%" }}>
        <div className="scrollable">
          <div
            className={
              tableDashboard === 1
                ? "dashboard vni-relative"
                : tableDashboard === 2
                ? "franchise vni-relative"
                : tableDashboard === 3
                ? "otherdash vni-relative"
                : "otherlist vni-relative"
            }
            style={{ width: "calc(100vw - 152px)", maxWidth: "100%" }}
          >
            <TableComponent
              searchResult={searchResult}
              tableItem={tableContents}
              enableSelection={onFranchisePage && isSalesUser}
              selection={selection}
              handleSelection={(e: any) => {
                setSelection(e);
                setBulkSelect(
                  tableRow
                    .filter((content: any, i: number) => {
                      // return e.indexOf(i) > -1 && content.status_update === 'Saved'
                      return (
                        e.indexOf(i) > -1 &&
                        (content.status_update === "Saved" ||
                          content.status_update === "Returned")
                      );
                    })
                    .map((content: any) => {
                      return {
                        _id: content.id,
                        app_uid: content.app_uid,
                        status: content.status_update.toUpperCase(),
                        specialist_pm_user: content.specialist_pm_user ?? "",
                      };
                    })
                );
              }}
              columnExtensions={tableColumnExtensions}
              rows={tableRow}
              columns={column}
              enablePaging={onFranchisePage}
              handleViewClick={handleViewClick}
              userRole={userRole}
            />
          </div>
          {!isRequesting && (
            <Modal
              fullWidth={true}
              maxWidth="xl"
              open={openModalView}
              onClose={handleCloseView}
              whereThisIsUsed={`vfr-${details.status}`}
            >
              {(openOtherSalesUser && (
                <OtherFranchiseTable
                  handleClickButton={handleViewCompareFranchise}
                />
              )) ||
                (openCompareFranchise && (
                  <CompareFranchise
                    franchise={details}
                    selected_id={compareFranchiseId}
                    handleClickButton={handleCompareButtonAction}
                    userRole={userRole}
                  />
                )) ||
                (!isViewLoading && (
                  <ViewFranchise
                    handleClickButton={handleOpenConfirm}
                    handleClickApproveUsers={handleViewOtherFR}
                    userRole={userRole}
                    setViewFranchiseModal={setOpenModalView}
                    isMultipleRole={isMultipleRole}
                  />
                ))}
            </Modal>
          )}
        </div>
      </div>
      {(!isRequesting || confirmValues.action === "validate") && (
        <Dialog
          className="vni-relative mark-modal"
          open={openModal}
          onClose={handleClose}
          aria-labelledby="form-dialog-title"
          maxWidth="md"
        >
          <DialogTitle className="vni-px-5 vni-pt-3">
            <h3
              className={
                confirmValues.action === "validate" && confirmValues.p === ""
                  ? "vni-text-center title"
                  : "title"
              }
            >
              {confirmValues.h1}
            </h3>
          </DialogTitle>
          <DialogContent className=" vni-px-5">
            {(modalButton === "Confirm" &&
              confirmValues.action === "validate" &&
              confirmValues.p === "" && (
                <img
                  className="vni-mx-auto"
                  src={loodingLogo}
                  alt=""
                  width="36"
                />
              )) ||
              (modalButton === "Confirm" &&
                confirmValues.action === "return" &&
                returnToSalesContent) ||
              // RESUBMIT
              // ||
              // modalButton === "Confirm" && confirmValues.action === "resubmit" &&  resubmitToSpecialist
              (modalButton === "Confirm" &&
                confirmValues.action === "return_validator" &&
                returnToValidatorContent) ||
              (modalButton === "Confirm" &&
                (confirmValues.action === "endorse" ||
                  confirmValues.action === "reendorse") &&
                endorseContent) ||
              (modalButton === "Confirm" &&
                confirmValues.action === "approve" &&
                approveBody) ||
              (modalButton === "Confirm" &&
                confirmValues.action === "disapprove" &&
                rejectedContent) ||
              (modalButton === "Confirm" &&
                confirmValues.action === "validate" &&
                confirmValues.p !== "" && (
                  <p className="vni-mb-5" style={{ maxWidth: 450 }}>
                    {withBreakline}
                  </p>
                )) ||
              (confirmValues.action === "approve" &&
                details.duplicate &&
                details.duplicate.length > 0 &&
                successResponse && (
                  <>
                    <Typography className="vni-mb-5" style={{ maxWidth: 500 }}>
                      Request by{" "}
                      <strong style={{ color: "#3ab77d" }}>
                        {confirmValues.p}
                      </strong>{" "}
                      has been approved. It has been successfully sent to
                      Underwriting.
                      <br />
                      The following are automatically disapproved:
                    </Typography>
                    <List>
                      {details.duplicate.map(
                        (option: Franchise, index: any) => (
                          <ListItem key={index}>
                            <strong style={{ color: "#ef5350" }}>
                              {option.applicant_name}
                            </strong>
                          </ListItem>
                        )
                      )}
                    </List>
                  </>
                )) || (
                <>
                  <p className="vni-mb-5" style={{ maxWidth: 450 }}>
                    {confirmValues.p}
                  </p>
                </>
              )}
          </DialogContent>
          <DialogActions className="btn-group d-flex-center vni-px-5 vni-pb-3">
            {modalButton === "Confirm" &&
            confirmValues.action === "validate" &&
            !validationContent.error &&
            !isUpdating ? (
              <a
                href="#!"
                data-cy={`${confirmValues.action}d-back-to-dashboard-btn`}
                className="CustomPrimaryOulineButton"
                onClick={handleBackToDashboard}
              >
                Back to Dashboard
              </a>
            ) : modalButton === "Confirm" && confirmValues.text[0] ? (
              <a
                href="#!"
                data-cy={`${modalButton}-${confirmValues.text[0]}-btn`}
                className="CustomPrimaryOulineButton"
                onClick={handleClose}
              >
                {confirmValues.text[0]}
              </a>
            ) : validationContent.error &&
              confirmValues.h1 &&
              confirmValues.p ? (
              <a
                href="#!"
                data-cy={`${modalButton}-${confirmValues.action}-okay-btn`}
                className="CustomPrimaryButton scarlet"
                onClick={handleClose}
              >
                Okay
              </a>
            ) : confirmValues.action === "cancel" &&
              confirmValues.h1 &&
              confirmValues.p ? (
              <a
                href="#!"
                data-cy={`${modalButton}-${confirmValues.action}-yes-btn`}
                className="CustomPrimaryOulineButton"
                onClick={() => {
                  setOpenModal(false);
                }}
              >
                {confirmValues.text[0]}
              </a>
            ) : (confirmValues.action === "submit" ||
                confirmValues.action === "resubmit" ||
                confirmValues.action === "return" ||
                confirmValues.action === "return_validator" ||
                confirmValues.action === "endorse" ||
                confirmValues.action === "reendorse" ||
                confirmValues.action === "approve" ||
                confirmValues.action === "disapprove") &&
              confirmValues.h1 &&
              confirmValues.p ? (
              <a
                href="#!"
                data-cy={`${confirmValues.action}-back-to-dashboard-btn`}
                className="CustomPrimaryOulineButton"
                onClick={handleBackToDashboard}
              >
                {confirmValues.text[0]}
              </a>
            ) : (
              <></>
            )}
            {modalButton === "Confirm" &&
            confirmValues.action === "validate" &&
            !validationContent.error &&
            !isUpdating ? (
              <a
                href="#!"
                data-cy={
                  details && details.supervisor_pm_user
                    ? `${confirmValues.action}d-reendorse-btn`
                    : `${confirmValues.action}d-endorse-btn`
                }
                className="CustomPrimaryButton"
                style={{ display: canSubmitFR ? "block" : "none" }}
                onClick={(e) =>
                  handleOpenConfirm(
                    e,
                    details && details.supervisor_pm_user
                      ? "reendorse"
                      : "endorse"
                  )
                }
              >
                Endorse
              </a>
            ) : modalButton === "Confirm" && confirmValues.text[1] ? (
              <a
                href="#!"
                data-cy={`${confirmValues.action}-${confirmValues.text[1]}-btn`}
                className={
                  confirmValues.action === "approve" ||
                  confirmValues.action === "endorse" ||
                  confirmValues.action === "reendorse" ||
                  confirmValues.action === "resubmit" ||
                  confirmValues.action === "submit"
                    ? "CustomPrimaryButton"
                    : "CustomPrimaryButton scarlet"
                }
                onClick={(e) => handleOpenConfirmed(e, confirmValues.action)}
              >
                {confirmValues.text[1]}
              </a>
            ) : (confirmValues.action === "submit" ||
                confirmValues.action === "resubmit") &&
              canAddFR &&
              confirmValues.h1 &&
              confirmValues.p ? (
              <a
                href="#!"
                data-cy={`${confirmValues.action}-create-new-btn`}
                className="CustomPrimaryButton"
                onClick={handleNewClick}
              >
                {confirmValues.text[1]}
              </a>
            ) : (
              <></>
            )}
            <a
              href="#!"
              data-cy="data-processing-close-modal"
              className="close-modal"
              onClick={handleClose}
            >
              Close{" "}
            </a>
          </DialogActions>
        </Dialog>
      )}

      <Dialog
        id="session-modal"
        maxWidth="xs"
        open={sessionExpired}
        onClose={() => {
          dispatch(logout());
          window.location.replace("../index.html#/");
        }}
        className="vni-m-auto"
      >
        <DialogTitle className="vni-px-5 vni-pt-3 vni-mb-8">
          <h2 className="title">Session Expired</h2>
        </DialogTitle>
        <DialogContent>
          <p className="vni-mb-8">
            Your account has logged in on another device. Please contact your
            administrator if it was not you.
          </p>
        </DialogContent>
        <DialogActions className="vni-flex d-flex-center">
          <button
            className="CustomPrimaryButton vni-mr-5"
            data-cy="session-timeout-logout-btn"
            onClick={() => {
              dispatch(logout());
              window.location.replace("../index.html#/");
            }}
          >
            Okay
          </button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default FranchiseList;
