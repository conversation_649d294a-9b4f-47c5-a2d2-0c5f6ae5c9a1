import { makeStyles } from '@mui/styles';
import { Theme } from '@mui/material/styles';

const viewReportStyles = makeStyles((_theme: Theme) => ({
    flex : {
        display: 'flex',
        alignItems: 'center',
        // justifyContent: 'flex-end'
    },
    
    divider : {
        display: 'inline-block',
        padding: '0 20px'
    },
    fakeLink: {
        display: 'flex',
        justifyContent: 'flex-start',
        cursor: 'pointer',
        fontWeight: 600,
        textDecoration: 'underline',
        opacity: .5
    },
    uploadLink: {
        fontWeight: 800,
        color: '#3AB77D',
        textDecoration: 'underline'
    },
    uploadIcon: {
        color: '#3AB77D',
        position: 'relative',
        marginTop: 5,
        marginRight:5 
    },
    modalTitle: {
        color: '#272E4C',
        fontWeight: 800,
        fontSize: 18,
        fontFamily: 'usual',
    },
    modalButtonLeft: {
        minWidth: 100,
        backgroundColor: 'transparent',
        color: '#000',
        borderColor: '#000',
        border: '1px solid',
        '&:hover': {
            backgroundColor: 'transparent'
        }
    },
    modalButtonRight: {
        minWidth: 100,
        color: '#FFFFFF',
        border: '1px solid #3AB77D',
        backgroundColor: '#3AB77D',
        '&:hover': {
            backgroundColor: '#3AB77D'
        }
    },
    modalText: {
        color: '#272E4C',
        fontWeight: 400,
        fontSize: 14,
        fontFamily: 'usual',
        textAlign:'center',
        padding: '2rem 0rem 2rem 0rem'

    },
}))

export {
    viewReportStyles
}