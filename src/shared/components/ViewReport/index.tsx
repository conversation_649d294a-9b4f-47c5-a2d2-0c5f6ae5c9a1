import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useHistory } from "react-router-dom";
import { viewReportStyles } from "./styles";
import { Modal } from "../Modal";
import { Grid, Typography, Button } from "@mui/material";
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { TableComponent } from "../TableComponent";
import { columns, columnExtensions } from "./ViewReport.const";
import { RootState } from "../../reducers/rootReducer";
import moment from "moment";
import {
  // setpreviewData,
  generateFranchiseReport,
  generateDailyReport,
} from "../../reducers/ReportSlice";
import { faUpload } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

interface IProps {
  onClickView: () => void;
  role: string;
  // isEnabled: boolean
}

export const ViewReport: React.FC<IProps> = (props: IProps) => {
  const [generateDtrModal, setGenerateDtrModal] = React.useState(false);
  const [generateFRModal, setGenerateFRModal] = React.useState(false);
  const { onClickView, role } = props;
  const classes = viewReportStyles();
  const isSalesUser = useSelector(
    (state: RootState) => state.login.userRoles.isSalesUser
  );
  const isSpecialist = useSelector(
    (state: RootState) => state.login.userRoles.isSpecialist
  );
  const isSupervisor = useSelector(
    (state: RootState) => state.login.userRoles.isSupervisor
  );
  const isManager = useSelector(
    (state: RootState) => state.login.userRoles.isManager
  );
  const isMultipleRole = isSalesUser && isSpecialist && isSupervisor && isManager;
  // const mainRole = useSelector(
  //   (state:RootState) => state.login.main_role
  // )
  // const DTRfrom = useSelector(
  //   (state: RootState) => state.report.previewData.DTRfrom
  // );
  // const DTRto = useSelector(
  //   (state: RootState) => state.report.previewData.DTRto
  // );
  const [dateFrom, setDateFrom] = React.useState<any>("");
  const [dateTo, setDateTo] = React.useState<any>("");
  const reportList = useSelector((state: RootState) => state.report.reportList);
  const [tableRows, setTableRows] = React.useState<any>([]);
  const history = useHistory();
  const dispatch = useDispatch();
  const [dtrDate, setDtrDate] = React.useState<any>();

  const [frDateFrom, setFrDateFrom] = React.useState<any>(new Date());
  const [frDateTo, setFrDateTo] = React.useState<any>(new Date());

  const handleDateFromChange = (e: any) => {
    setDateFrom(e);
  };

  const handleDateToChange = (e: any) => {
    setDateTo(e);
  };

  const handleGenerateDTRDate = (e: any) => {
    console.log(dtrDate);
    setDtrDate(e);
  };

  const handleGenerateFrDateFrom = (e: any) => {
    setFrDateFrom(e);
  };

  const handleGenerateFrDateTo = (e: any) => {
    setFrDateTo(e);
  };

  const handleViewClick = (id: string) => {
    onClickView();
    history.push(`/franchising/report/preview/${id}`);
  };

  // console.log(reportList);

  useEffect(() => {
    if (reportList) {
      // console.log("report listt ===>", reportList);
      let tableContents: any = [];
      let dateF = moment(dateFrom);
      let dateT = moment(dateTo);
      if (dateFrom !== "" && dateTo !== "") {
        if (dateF.isSame(dateT)) {
          tableContents = reportList.filter(
            ({ created_at }) =>
              moment(created_at).format("MM/DD/YYYY") ===
              dateF.format("MM/DD/YYYY")
          );
        } else {
          tableContents = reportList.filter(
            ({ created_at }) =>
              moment(created_at).isBetween(dateF, dateT) ||
              moment(created_at).format("MM/DD/YYYY") ===
                dateF.format("MM/DD/YYYY") ||
              moment(created_at).format("MM/DD/YYYY") ===
                dateT.format("MM/DD/YYYY")
          );
        }
      } else {
        tableContents = reportList;
      }

      tableContents = tableContents.map((content: any, index: number) => {
        return {
          index_no: index + 1,
          id: content._id,
          date: moment(content.created_at).format("MM/DD/YYYY"),
          report_name: `${content.report}.pdf`,
        };
      });
      // const dtrDate = moment(new Date()).format("MMDDYYYY");
      // const dtrDate2 = moment(new Date()).format("MM/DD/YYYY");
      // const tempDTRID =
      //   tableContents && tableContents.length > 0 && tableContents[0].id;
      // const DTR = {
      //   date: dtrDate2,
      //   id: `DTR${tempDTRID}`,
      //   index_no: tableContents.length + 1,
      //   report_name: `DTR${dtrDate}.pdf`,
      // };

      // if (tableContents.length > 0) {
      //   tableContents.unshift(DTR);
      // }
      setTableRows(tableContents);
    }
  }, [reportList, dateFrom, dateTo]);

  return (
    <div className="main vni-py-8 vni-px-12">
      <Grid
        container
        justifyContent={"flex-start"}
        alignItems="center"
        style={{ padding: "20px 0" }}
      >
        <Grid item xs={12} style={{ textAlign: "right" }}>
          <div
            style={{
              justifyContent: "space-between",
              display: "flex",
              alignContent: "center",
            }}
          >
            <div>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  className="vni-date-picker"
                  format="MM/dd/yyyy"
                  label=""
                  maxDate={dateTo ? dateTo : new Date()}
                  value={dateFrom ? dateFrom : null}
                  onChange={handleDateFromChange}
                  slotProps={{
                    textField: {
                      variant: 'outlined',
                      placeholder: "mm/dd/yyyy",
                    },
                  }}
                />
              </LocalizationProvider>

              <span className={classes.divider}>to</span>

              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  className="vni-date-picker"
                  format="MM/dd/yyyy"
                  label=""
                  minDate={dateFrom ?? undefined}
                  maxDate={new Date()}
                  value={dateTo ? dateTo : null}
                  onChange={handleDateToChange}
                  slotProps={{
                    textField: {
                      variant: 'outlined',
                      placeholder: "mm/dd/yyyy",
                    },
                  }}
                />
              </LocalizationProvider>
            </div>
            {role === "encoder" && (
              <div style={{ display: "flex", flexDirection: "row" }}>
                <div
                  className={classes.fakeLink}
                  style={{ opacity: 1, marginRight: "1rem" }}
                >
                  <FontAwesomeIcon
                    className={classes.uploadIcon}
                    icon={faUpload}
                  />
                  <label
                    htmlFor="upload_document"
                    className={classes.fakeLink}
                    style={{ opacity: 1 }}
                  >
                    <Typography
                      className={classes.uploadLink}
                      onClick={() => setGenerateFRModal((prev) => !prev)}
                    >
                      Generate Franchise Report
                    </Typography>
                  </label>
                </div>
                <div className={classes.fakeLink} style={{ opacity: 1 }}>
                  <FontAwesomeIcon
                    className={classes.uploadIcon}
                    icon={faUpload}
                  />
                  <label
                    htmlFor="upload_document"
                    className={classes.fakeLink}
                    style={{ opacity: 1 }}
                  >
                    <Typography
                      onClick={() => setGenerateDtrModal((prev) => !prev)}
                      className={classes.uploadLink}
                    >
                      Generate Daily Transaction Report
                    </Typography>
                  </label>
                </div>
              </div>
            )}
          </div>
        </Grid>
      </Grid>
      <div style={isSalesUser ? { width: "65%" } : {}}>
        <TableComponent
          rows={tableRows}
          columns={columns}
          columnExtensions={columnExtensions}
          customNoDataMessage="No Generated Report"
          enablePaging
          isGeneratedReportList
          handleViewClick={handleViewClick}
        />
      </div>


    {/* {isSpecialist || (isMultipleRole && mainRole === 'PROSPECTIVE_CLIENT_PROPOSAL_MAKE') &&
        <Modal
            fullWidth={true}
            maxWidth="sm"
            open={generateDtrModal}
            onClose={() => setGenerateDtrModal((prev) => !prev)}
            whereThisIsUsed={`vfr-view-contact`}
        >
            <Grid xs={12}>
            <Typography className={classes.modalTitle}>
                Generate Daily Transaction Report
            </Typography>
            <Grid direction="column" container xs={12} style={{ justifyContent: "center"}}>
                <Typography className={classes.modalText}>
                Please specify the date for the Daily Transaction Report
                </Typography>
                <div
                style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                }}
                >
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DatePicker
                    className="vni-date-picker"
                    format="MM/dd/yyyy"
                    maxDate={new Date()}
                    value={dtrDate}
                    onChange={handleGenerateDTRDate}
                    slotProps={{
                      textField: {
                        style: {
                            maxWidth: "50%",
                        },
                        id: "view_franchise_reports_date_to",
                        placeholder: "mm/dd/yyyy",
                        variant: "outlined",
                        inputProps: {
                          'data-cy': "view_franchise_reports_date_to"
                        }
                      }
                    }}
                    />
                </LocalizationProvider>
                </div>
            </Grid>
            <Grid
                    container
                xs={12}
                spacing={1}
                style={{ alignItems: "center", justifyContent: "center" }}
            >
                <Grid item>
                <Button
                    data-cy="okay-btn"
                    className={classes.modalButtonLeft}
                    style={{ marginTop: 20 }}
                    onClick={() => setGenerateDtrModal((prev) => !prev)}
                >
                    Cancel
                </Button>
                </Grid>
                <Grid>
                <Button
                    data-cy="okay-btn"
                    className={classes.modalButtonRight}
                    style={{ marginTop: 20 }}
                    onClick={() => {
                    console.log("Pressed");
                    dispatch(generateDailyReport(dtrDate, dtrDate));
                    setGenerateDtrModal((prev) => !prev);
                    }}
                >
                    Generate
                </Button>
                </Grid>
            </Grid>
            </Grid>
        </Modal>
    } */}

      <Modal
            fullWidth={true}
            maxWidth="sm"
            open={generateDtrModal}
            onClose={() => setGenerateDtrModal((prev) => !prev)}
            whereThisIsUsed={`vfr-view-contact`}
        >
            <Grid xs={12} item>
              <Typography className={classes.modalTitle}>
                  Generate Daily Transaction Report
              </Typography>
              <Grid direction="column" container xs={12} style={{ justifyContent: "center"}} item>
                  <Typography className={classes.modalText}>
                  Please specify the date for the Daily Transaction Report
                  </Typography>
                  <div
                  style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                  }}
                  >
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                        <DatePicker
                        sx={{
                            maxWidth: "50%",
                        }}
                        className="vni-date-picker"
                        format="MM/dd/yyyy"
                        label=""
                        maxDate={new Date()}
                        value={dtrDate}
                        onChange={handleGenerateDTRDate}
                        slotProps={{
                            textField: {
                              variant: 'outlined',
                              placeholder: "mm/dd/yyyy",
                            },
                          }}
                        />
                    </LocalizationProvider>
                  </div>
              </Grid>
            <Grid
                    container
                xs={12}
                spacing={1}
                style={{ alignItems: "center", justifyContent: "center" }}
                item
            >
                <Grid item>
                <Button
                    data-cy="okay-btn"
                    className={classes.modalButtonLeft}
                    style={{ marginTop: 20 }}
                    onClick={() => setGenerateDtrModal((prev) => !prev)}
                >
                    Cancel
                </Button>
                </Grid>
                <Grid>
                <Button
                    data-cy="okay-btn"
                    className={classes.modalButtonRight}
                    style={{ marginTop: 20 }}
                    onClick={() => {
                    dispatch(generateDailyReport(dtrDate, dtrDate, role));
                    setGenerateDtrModal((prev) => !prev);
                    }}
                >
                    Generate
                </Button>
                </Grid>
            </Grid>
            </Grid>
        </Modal>

    {/* {isSalesUser || (isMultipleRole && mainRole !== 'PROSPECTIVE_CLIENT_PROPOSAL_MAKE')} */}

      <Modal
        fullWidth={true}
        maxWidth="md"
        open={generateFRModal}
        onClose={() => setGenerateFRModal((prev) => !prev)}
        whereThisIsUsed={`vfr-view-contact`}
      >
        <Grid xs={12} item>
          <Typography className={classes.modalTitle}>
            Generate Franchise Report
          </Typography>
          <Grid direction="column" container xs={12} style={{ justifyContent: "center"}} item>
            <Typography className={classes.modalText}>
              Please specify the date range for the Franchising Report
            </Typography>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-evenly",
              }}
            >
              <Typography className={classes.modalText}>Start Date</Typography>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  sx={{
                    maxWidth: "50%",
                  }}
                  className="vni-date-picker"
                  format="MM/dd/yyyy"
                  label=""
                  maxDate={new Date()}
                  value={frDateFrom}
                  onChange={handleGenerateFrDateFrom}
                  slotProps={{
                    textField: {
                      variant: 'outlined',
                      placeholder: "mm/dd/yyyy",
                    },
                  }}
                />
              </LocalizationProvider>
              <Typography className={classes.modalText}>End Date</Typography>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  sx={{
                    maxWidth: "50%",
                  }}
                  className="vni-date-picker"
                  format="MM/dd/yyyy"
                  label=""
                  minDate={frDateFrom ?? undefined}
                  maxDate={new Date()}
                  value={frDateTo}
                  onChange={handleGenerateFrDateTo}
                  slotProps={{
                    textField: {
                      variant: 'outlined',
                      placeholder: "mm/dd/yyyy",
                    },
                  }}
                />
              </LocalizationProvider>
            </div>
          </Grid>
          <Grid
            container
            xs={12}
            spacing={1}
            style={{alignItems:"center", justifyContent:"center" }}
            item
          >
            <Grid item>
              <Button
                data-cy="okay-btn"
                className={classes.modalButtonLeft}
                style={{ marginTop: 20 }}
                onClick={() => setGenerateFRModal((prev) => !prev)}
              >
                Cancel
              </Button>
            </Grid>
            <Grid>
              <Button
                data-cy="okay-btn"
                className={classes.modalButtonRight}
                style={{ marginTop: 20 }}
                onClick={() => {
                  const currentDate = moment();
                  if (moment(frDateFrom).isAfter(currentDate) || moment(frDateTo).isAfter(currentDate)) {
                    console.log("GENERATE FR REPORT PREVENTED: the date is in the future.");
                  } else {
                    dispatch(generateFranchiseReport(frDateFrom, frDateTo, role));
                    setGenerateFRModal((prev) => !prev);
                  }
                }}
                disabled={moment(frDateFrom).isAfter(moment()) || moment(frDateTo).isAfter(moment()) ? true : false}
              >
                Generate
              </Button>
            </Grid>
          </Grid>
        </Grid>
      </Modal>
    </div>
  );
};
