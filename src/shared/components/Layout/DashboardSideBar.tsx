import React, { useEffect, useState } from "react";
import { NavLink, useLocation, useHistory, Link } from "react-router-dom";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  styled,
  Menu,
  MenuItem,
} from "@mui/material";
import { RootState } from "../../reducers/rootReducer";
import { useSelector, useDispatch } from "react-redux";
import {
  setIsDisapproveEdit,
  setIsReturnEdit,
  setDisapproveInputs,
  setReturnInputs,
} from "../../reducers/MaintenanceSlice";

import { frsRole } from "./constants";

function DashboardSideBar() {
  let location = useLocation();
  let history = useHistory();
  const dispatch = useDispatch();
  // const frsRole: any = {
  //   FRF_ENCODER: ["Encoder/BDO", "encoder"],
  //   FRF_VALIDATOR: ["Validator/Specialist", "validator"],
  //   FRF_INITIAL_APPROVER: ["Initial Approver/Supervisor", "supervisor"],
  //   FRF_FINAL_APPROVER: ["Final Approver/Manager", "manager"],
  // };
  const sideNav = [
    {
      name: "dashboard",
      label: "Dashboard",
      url: `/franchising/dashboard/`,
      svg: (
        <span>
          <svg
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9.6 22.4V15.2H14.4V22.4H20.4V12.8H24L12 2L0 12.8H3.6V22.4H9.6Z"
              fill="black"
            />
          </svg>
        </span>
      ),
    },
    {
      name: "franchise",
      label: "Franchise",
      url: `/franchising/franchise/`,
      svg: (
        <span>
          <svg
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M16.1579 11.3684V3.78947L12.3684 0L8.57895 3.78947V6.31579H1V24H23.7368V11.3684H16.1579ZM6.05263 21.4737H3.52632V18.9474H6.05263V21.4737ZM6.05263 16.4211H3.52632V13.8947H6.05263V16.4211ZM6.05263 11.3684H3.52632V8.8421H6.05263V11.3684ZM13.6316 21.4737H11.1053V18.9474H13.6316V21.4737ZM13.6316 16.4211H11.1053V13.8947H13.6316V16.4211ZM13.6316 11.3684H11.1053V8.8421H13.6316V11.3684ZM13.6316 6.31579H11.1053V3.78947H13.6316V6.31579ZM21.2105 21.4737H18.6842V18.9474H21.2105V21.4737ZM21.2105 16.4211H18.6842V13.8947H21.2105V16.4211Z"
              fill="black"
            />
          </svg>
        </span>
      ),
    },
    {
      name: "report",
      label: "Reports",
      url: `/franchising/report/`,
      svg: (
        <svg
          aria-hidden="true"
          focusable="false"
          data-prefix="fas"
          data-icon="table"
          role="img"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 512 512"
        >
          <g>
            <path
              fill="currentColor"
              d="M464 32H48C21.49 32 0 53.49 0 80v352c0 26.51 21.49 48 48 48h416c26.51 0 48-21.49 48-48V80c0-26.51-21.49-48-48-48zM224 416H64v-96h160v96zm0-160H64v-96h160v96zm224 160H288v-96h160v96zm0-160H288v-96h160v96z"
            ></path>
          </g>
        </svg>
      ),
    },
    {
      name: "maintenance",
      label: "Maintenance",
      url: `/franchising/maintenance/`,
      svg: (
        <span>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <g>
              <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z" />
            </g>
          </svg>
        </span>
      ),
    },
    {
      name: "contact",
      label: "Agents/Brokers Maintenance",
      url: `/franchising/contact/`,
      svg: (
        <span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            viewBox="0 0 16 16"
          >
            <path d="M7 14s-1 0-1-1 1-4 5-4 5 3 5 4-1 1-1 1H7zm4-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z" />
            <path
              fillRule="evenodd"
              d="M5.216 14A2.238 2.238 0 0 1 5 13c0-1.355.68-2.75 1.936-3.72A6.325 6.325 0 0 0 5 9c-4 0-5 3-5 4s1 1 1 1h4.216z"
            />
            <path d="M4.5 8a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z" />
          </svg>
        </span>
      ),
    },
    // {
    //   name:"faq",
    //   label: "FAQs",
    //   url : `/franchising/faq/`,
    //   svg : <span>
    //           <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
    //             <g>
    //               <path d="M12 0C5.376 0 0 5.376 0 12C0 18.624 5.376 24 12 24C18.624 24 24 18.624 24 12C24 5.376 18.624 0 12 0ZM13.2 20.4H10.8V18H13.2V20.4ZM15.684 11.1L14.604 12.204C13.74 13.08 13.2 13.8 13.2 15.6H10.8V15C10.8 13.68 11.34 12.48 12.204 11.604L13.692 10.092C14.136 9.66 14.4 9.06 14.4 8.4C14.4 7.08 13.32 6 12 6C10.68 6 9.6 7.08 9.6 8.4H7.2C7.2 5.748 9.348 3.6 12 3.6C14.652 3.6 16.8 5.748 16.8 8.4C16.8 9.456 16.368 10.416 15.684 11.1Z" />
    //             </g>
    //           </svg>
    //         </span>
    // }
  ];
  const [openModal, setOpenModal] = useState(false);
  const [navURL, setClickNav] = useState("");
  const isEdit = useSelector((state: RootState) => state.franchise.isEdit);
  const isDisapproveEdit = useSelector(
    (state: RootState) => state.maintenance.isDisapproveEdit
  );
  const isReturnEdit = useSelector(
    (state: RootState) => state.maintenance.isReturnEdit
  );
  const returnInputs = useSelector(
    (state: RootState) => state.maintenance.returnInputs
  );
  const disapproveInputs = useSelector(
    (state: RootState) => state.maintenance.disapproveInputs
  );
  const isNewInputs = useSelector(
    (state: RootState) => state.franchise.isNewInputs
  );
  const isNewContactInputs = useSelector(
    (state: RootState) => state.contact.isNewInputs
  );
  const { isMaintainer } =
    useSelector((state: RootState) => state.login.userRoles);
  const fileUploaded = useSelector(
    (state: RootState) => state.franchise.uploadedFile
  );
  let isObjectEmpty = !Object.keys(fileUploaded).length;
  const { canViewDashboard, canViewBrokerAgent, canViewReport } = useSelector(
    (state: RootState) => state.login.userPermissions
  );
  const role = useSelector((state: RootState) => state.login.roleAPI);
  //
  const [dashboardRoles, setDashboardRoles] = React.useState<string[]>([]);
  const subRoles = useSelector((state: RootState) => state.login.role);
  const mainRole = useSelector((state: RootState) => state.login.main_role);




  const [dashboardAnchorEl, setDashboardAnchorEl] =
    React.useState<null | HTMLElement>(null);
  const [franchiseAnchorEl, setFranchiseAnchorEl] =
    React.useState<null | HTMLElement>(null);
  const [reportAnchorEl, setReportAnchorEl] =
    React.useState<null | HTMLElement>(null);
  


  const openDashboard = Boolean(dashboardAnchorEl);
  const openFranchise = Boolean(franchiseAnchorEl);
  const openReport = Boolean(reportAnchorEl);

  const handleOnClickDashboard = (
    event: React.MouseEvent<HTMLAnchorElement>
  ) => {
    event.preventDefault();
    setDashboardAnchorEl(event.currentTarget);
  };
  const handleOnCloseDashboard = () => {
    setDashboardAnchorEl(null);
  };

  const handleOnClickFranchise = (
    event: React.MouseEvent<HTMLAnchorElement>
  ) => {
    event.preventDefault();
    setFranchiseAnchorEl(event.currentTarget);
  };
  const handleOnCloseFranchise = () => {
    setFranchiseAnchorEl(null);
  };

  const handleOnClickReport = (
    event: React.MouseEvent<HTMLAnchorElement>,
  ) => {
    event.preventDefault();
    console.log('event', event.currentTarget);
    setReportAnchorEl(event.currentTarget);
    // setCurrentRole()
  }
  const handleOnCloseReport = () => {
    setReportAnchorEl(null);
  };

  const StyledMenu = styled(Menu)({
    '& .MuiPaper-root': {
      backgroundColor: "#1e2071",
      borderRadius: 0,
    },
    marginLeft: "62px",
    marginTop: "10px",
  });

  const StyledMenuItem = (props: any) => (
    <MenuItem
      style={{
        color: "#ffffff",
        fontSize: "14px",
        textDecoration: "none",
      }}
      {...props}
    />
  );

  const handleNavClick = (e: any, url: string) => {
    if (
      isNewInputs ||
      isNewContactInputs ||
      !isObjectEmpty ||
      (isMaintainer &&
        (isDisapproveEdit ||
          isReturnEdit ||
          returnInputs !== "" ||
          disapproveInputs !== ""))
    ) {
      e.preventDefault();
      setOpenModal(true);
      setClickNav(url);
    }
  };
  const handleModalConfirmed = () => {
    if (isMaintainer) {
      dispatch(setIsDisapproveEdit(false));
      dispatch(setIsReturnEdit(false));
      dispatch(setDisapproveInputs(""));
      dispatch(setReturnInputs(""));
    }
    setOpenModal(false);
    history.push(navURL);
  };

  const handleClose = (e: any) => {
    e.preventDefault();
    setOpenModal(false);
  };

  useEffect(() => {
    if (role === "sales_head") {
      history.push("/franchising/report/");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [role]);

  useEffect(() => {
    if (subRoles.length || mainRole) {
      const _roles = subRoles
        .filter(
          (i: any) =>
            i.module === "Franchising" && Object.keys(frsRole).includes(i.name)
        )
        .map((r: any) => r.name);

      if (Object.keys(frsRole).includes(mainRole)) _roles.unshift(mainRole);
      setDashboardRoles(
        _roles
          .filter((value, index, self) => self.indexOf(value) === index)
          .sort(
            (a: any, b: any) =>
              Object.keys(frsRole).indexOf(a) - Object.keys(frsRole).indexOf(b)
          )
      );
    }
  }, [subRoles, mainRole]);



  return (
    <aside id="sidebar">
      <div className="sidebar-content">
        <div className="vni-mb-6 vni-flex-grow">
          {sideNav
            .filter((n) => n.name !== "faq")
            .filter(
              (n) =>
                n.name !== "dashboard" ||
                (n.name === "dashboard" && canViewDashboard)
            )
            .filter(
              (n) =>
                n.name !== "franchise" ||
                (n.name === "franchise" && canViewDashboard)
            )
            .filter(
              (n) =>
                n.name !== "maintenance" ||
                (n.name === "maintenance" && isMaintainer)
            )
            .filter(
              (n) =>
                n.name !== "contact" ||
                (n.name === "contact" && canViewBrokerAgent)
            )
            .filter(
              (n) =>
                n.name !== "report" || (n.name === "report" && canViewReport)
            )
            .map((content, index) => {
              if (content.name === "dashboard") {
                if (dashboardRoles.length >= 2) {
                  return (
                    <>
                      <NavLink
                        to="#"
                        className="vni-mt-5"
                        //@ts-ignore
                        activeclassname={
                          openDashboard ||
                          location.pathname.includes("/franchising/dashboard")
                            ? "active"
                            : "vni-mt-5"
                        }
                        key={index + 3}
                        data-cy={`${content.name}-btn`}
                        onClick={handleOnClickDashboard}
                        // onClick={handleOnClickDashboard}
                      >
                        {/* <div onClick={handleOnClickDashboard}> */}
                        {content.svg}
                        <p>{content.label}</p>
                        {/* </div> */}
                      </NavLink>

                      <StyledMenu
                        anchorEl={dashboardAnchorEl}
                        keepMounted
                        open={openDashboard}
                        onClose={handleOnCloseDashboard}
                      >
                        {dashboardRoles.map((_role: string, index: number) => (
                          <StyledMenuItem
                            key={index}
                            to={`${content.url}${frsRole[_role][1]}`}
                            component={Link}
                            onClick={() => {
                              handleOnCloseDashboard();
                            }}
                          >
                            {frsRole[_role][0]}
                          </StyledMenuItem>
                        ))}
                      </StyledMenu>
                    </>
                  );
                } else {
                  return (
                    <NavLink
                      key={index + 3}
                      data-cy={`${content.name}-btn`}
                      //@ts-ignore
                      activeclassname={
                        location.pathname.includes("/franchising/dashboard")
                          ? "active"
                          : "vni-mt-5"
                      }
                      className="vni-mt-5"
                      to={
                        dashboardRoles.length
                          ? `${content.url}${frsRole[dashboardRoles[0]][1]}`
                          : ""
                      }
                      onClick={(e) =>
                        handleNavClick(
                          e,
                          dashboardRoles.length
                            ? `${content.url}${frsRole[dashboardRoles[0]][1]}`
                            : ""
                        )
                      }
                    >
                      {content.svg}
                      <p>{content.label}</p>
                    </NavLink>
                  );
                }
              } else if (content.name === "franchise") {
                if (dashboardRoles.length >= 2) {
                  return (
                    <>
                      <NavLink
                        to="#"
                        className="vni-mt-5"
                        //@ts-ignore
                        activeclassname={
                          openFranchise ||
                          location.pathname.includes("/franchising/franchise")
                            ? "active"
                            : "vni-mt-5"
                        }
                        key={index + 3}
                        data-cy={`${content.name}-btn`}
                        onClick={handleOnClickFranchise}
                      >
                        {content.svg}
                        <p>{content.label}</p>
                      </NavLink>

                      <StyledMenu
                        anchorEl={franchiseAnchorEl}
                        keepMounted
                        open={openFranchise}
                        onClose={handleOnCloseFranchise}
                      >
                        {dashboardRoles.map((_role: string, index: number) => (
                          <StyledMenuItem
                            key={index}
                            to={`${content.url}${frsRole[_role][1]}`}
                            component={Link}
                            onClick={() => {
                              handleOnCloseFranchise();
                            }}
                          >
                            {frsRole[_role][0]}
                          </StyledMenuItem>
                        ))}
                      </StyledMenu>
                    </>
                  );
                } else {
                  return (
                    <NavLink
                      key={index + 3}
                      data-cy={`${content.name}-btn`}
                      //@ts-ignore
                      activeclassname={
                        location.pathname.includes("/franchising/franchise")
                          ? "active"
                          : "vni-mt-5"
                      }
                      className="vni-mt-5"
                      to={
                        dashboardRoles.length
                          ? `${content.url}${frsRole[dashboardRoles[0]][1]}`
                          : ""
                      }
                      onClick={(e) =>
                        handleNavClick(
                          e,
                          dashboardRoles.length
                            ? `${content.url}${frsRole[dashboardRoles[0]][1]}`
                            : ""
                        )
                      }
                    >
                      {content.svg}
                      <p>{content.label}</p>
                    </NavLink>
                  );
                }
              } else if (content.name === "report"){
                if (dashboardRoles.length >= 2) {
                  return (
                    <>
                      <NavLink
                        to="#"
                        className="vni-mt-5"
                        //@ts-ignore
                        activeclassname={
                          openReport ||
                          location.pathname.includes("/franchising/report")
                            ? "active"
                            : "vni-mt-5"
                        }
                        key={index + 3}
                        data-cy={`${content.name}-btn`}
                        onClick={handleOnClickReport}
                      >
                        {content.svg}
                        <p>{content.label}</p>
                      </NavLink>

                      <StyledMenu
                        anchorEl={reportAnchorEl}
                        keepMounted
                        open={openReport}
                        onClose={handleOnCloseReport}
                      >
                        {dashboardRoles.map((_role: string, index: number) => (
                          <StyledMenuItem
                            key={index}
                            to={`${content.url}${frsRole[_role][1]}`}
                            component={Link}
                            onClick={() => {
                              handleOnCloseReport();
                            }}
                          >
                            {frsRole[_role][0]}
                          </StyledMenuItem>
                        ))}
                      </StyledMenu>
                    </>
                  );
                } else {
                  return (
                    <NavLink
                      key={index + 3}
                      data-cy={`${content.name}-btn`}
                      //@ts-ignore
                      activeclassname={
                        location.pathname.includes("/franchising/report")
                          ? "active"
                          : "vni-mt-5"
                      }
                      className="vni-mt-5"
                      to={
                        dashboardRoles.length
                          ? `${content.url}${frsRole[dashboardRoles[0]][1]}`
                          : ""
                      }
                      onClick={(e) =>
                        handleNavClick(
                          e,
                          dashboardRoles.length
                            ? `${content.url}${frsRole[dashboardRoles[0]][1]}`
                            : ""
                        )
                      }
                    >
                      {content.svg}
                      <p>{content.label}</p>
                    </NavLink>
                  );
                }
              } else {
                return (
                  <NavLink
                    key={index + 3}
                    data-cy={`${content.name}-btn`}
                    //@ts-ignore
                    activeclassname={
                      // location.pathname.includes("/franchising/dashboard")
                      location.pathname === content.url ? "active" : "vni-mt-5"
                    }
                    className="vni-mt-5"
                    to={content.url}
                    onClick={(e) => handleNavClick(e, content.url)}
                  >
                    {content.svg}
                    <p>{content.label}</p>
                  </NavLink>
                );
              }
            })}
        </div>
        <div>
          {sideNav
            .filter((n) => n.name === "faq")
            .map((content, index) => (
              <NavLink
                key={index}
                data-cy={`${content.name}-btn`}
                //@ts-ignore
                activeclassname={
                  location.pathname === content.url ? "active" : "vni-mt-5"
                }
                className="vni-mt-5"
                to={content.url}
                onClick={(e) => handleNavClick(e, content.url)}
              >
                {content.svg}
                <p>{content.label}</p>
              </NavLink>
            ))}
        </div>
      </div>
      <Dialog
        id="unsaved-changes-modal"
        open={openModal}
        onClose={handleClose}
        className="vni-m-auto"
      >
        <DialogTitle className="title vni-px-5 vni-pt-3 vni-mb-8">
          <h2 className="vni-px-5" style={{ fontWeight: "bold" }}>
            Unsaved Changes
          </h2>
        </DialogTitle>
        <DialogContent>
          {isMaintainer ? (
            <p className="vni-px-5 vni-mb-8">
              {" "}
              Are you sure you want to leave this page without saving? All
              unsaved changes will be lost.{" "}
            </p>
          ) : (
            <p className="vni-px-5 vni-mb-8">
              {isEdit
                ? "Are you sure you want to cancel editing Franchise Request? This will not be saved."
                : "Are you sure you want to leave this page without saving? All unsaved changes will be lost."}
            </p>
          )}
        </DialogContent>
        <DialogActions className="vni-flex d-flex-center">
          <button
            data-cy={
              isMaintainer
                ? "maintenancepage-menu-unsaved-no-btn"
                : isEdit
                ? "editpage-menu-unsaved-no-btn"
                : "createpage-menu-unsaved-no-btn"
            }
            className="CustomPrimaryOulineButton vni-mr-5"
            onClick={handleClose}
          >
            No
          </button>
          <button
            data-cy={
              isMaintainer
                ? "maintenancepage-menu-unsaved-yes-btn"
                : isEdit
                ? "editpage-menu-unsaved-yes-btn"
                : "createpage-menu-unsaved-yes-btn"
            }
            className="CustomPrimaryButton"
            onClick={handleModalConfirmed}
          >
            Yes
          </button>
        </DialogActions>
      </Dialog>
    </aside>
  );
}

export default DashboardSideBar;
