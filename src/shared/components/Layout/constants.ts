// interface FrsRoleType {
//     FRF_ENCODER: string[];
//     FRF_VALIDATOR: string[];
//     FRF_INITIAL_APPROVER: string[];
//     FRF_FINAL_APPROVER: string[];
// }

const frsRole: any = {
  FRF_ENCODER: ["Encoder/BDO", "encoder"],
  FRF_VALIDATOR: ["Validator/Specialist", "validator"],
  FRF_INITIAL_APPROVER: ["Initial Approver/Supervisor", "supervisor"],
  FRF_FINAL_APPROVER: ["Final Approver/Manager", "manager"],
};


export { frsRole }