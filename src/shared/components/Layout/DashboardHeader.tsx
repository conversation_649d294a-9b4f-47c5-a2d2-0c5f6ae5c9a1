import React, { useState, useEffect, useRef } from "react";
import { useLocation } from "react-router-dom";
import Logo from "./Logo";
// import Notification from "./Notification";
import ContactHeader from "../ContactHeader";
import CreateFranchiseHeader from "../CreateFranchiseHeader";
import { useSelector } from "react-redux";
import { RootState } from "../../reducers/rootReducer";
import profilepic from "../../../assets/images/profilepic.png"
import { resetState as resetStateContact } from "../../reducers/ContactSlice";
import { resetState } from "../../reducers/FranchiseSlice";
import { useDispatch } from "react-redux";
import { Link } from 'react-router-dom'
import { Fade, Paper, Button, Avatar, Grid, IconButton, 
  // Dialog, DialogTitle, DialogContent, DialogActions
} from "@mui/material"
import Popper, { PopperPlacementType } from '@mui/material/Popper';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import { makeStyles } from '@mui/styles';
import { Theme } from '@mui/material/styles';

import { Modal } from "../Modal";
import {SwitchModuleModal} from "../SwitchModuleModal"
import {iProfile} from '../../models/User'
// import { decryptCredentials } from "../../../utils/LoginService";
// import { logout, setSessionExpired } from "../../reducers/LoginSlice";

const useStyles = makeStyles((_theme: Theme) => ({
  root: {
    display: 'flex',
    backgroundColor: '#ffffff'
  },
  drawer: {
    '@media (min-width: 600px)': {
      width: '72px',
      flexShrink: 0,
    },
  },
  drawerPaper: {
    backgroundColor: '#1e2071',
  },
  content: {
    flexGrow: 1,
  },
  toolbar: {
    minHeight: 64,
  },
  menuButton: {
    marginRight: '16px',
    '@media (min-width: 600px)': {
      display: 'none',
    },
  },
  logo: {
    height: '40px',
  },
  badge: {
    backgroundColor: '#ff6969',
    color: '#ffffff',
    fontWeight: 700,
  },
  grow: {
    flexGrow: 1,
  },
  sidebar: {
    maxWidth: '85px',
  },
  contentgrid: {
    maxWidth: 'calc(100% - 85px)',
    flexBasis: 'calc(100% - 85px)',
  },
  profileContent: {
    width: 280,
    padding: 15,
    borderBottom: '1px solid #B5B8C1'
  },
  profileFooter: {
    width: 280,
    padding: 15,
    textAlign: 'center'
  },
  logoutButton: {
    fontSize: 16,
    fontWeight: 500,
    backgroundColor: 'transparent',
    border: 'none',
    outline: 'none',
    color: '#B5B8C1',
    textAlign: 'center',
    cursor: 'pointer',
  },
  buttons: {
    width: '120px',
    height: '45px',
    lineHeight: '110%'
  },
  linkProfile: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: 1,
    color: '#fff',
    textDecoration: 'none',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  }

}));

function DashboardHeader() {
  const classes = useStyles();
  let location = useLocation();
  const dispatch = useDispatch();

  useEffect(()=>{
    dispatch(resetState())
    dispatch(resetStateContact())
    // eslint-disable-next-line react-hooks/exhaustive-deps
  },[location]);

  const requestAction = useSelector((state: RootState) => state.franchise.isEdit);
  // const token = useSelector((state: RootState) => state.login.token);

  const username = useSelector((state: RootState) => state.login.username);
  const firstname = useSelector((state: RootState) => state.login.firstname);
  const middlename = useSelector((state: RootState) => state.login.middlename);
  const lastname = useSelector((state: RootState) => state.login.lastname);
  const suffix = useSelector((state: RootState) => state.login.suffix);
  // const [decryptedUserName, setUsername] = React.useState("");
  // const [decryptedFirstname, setFirstname] = React.useState("");
  // const [decryptedMiddlename, setMiddlename] = React.useState("");
  // const [decryptedLastName, setLastname] = React.useState("");
  // const [fullname, setFullname] = React.useState("");

  const userProfile = useSelector((state: RootState) => state.login.profile);
  const [userData, setUserData] = useState<iProfile>()
  const mainModule = useSelector((state: RootState) => state.login.main_module);
  const moduleLists = useSelector((state: RootState) => state.login.modules);
  const isFCHeader = (location.pathname === "/franchising/franchise/create" || requestAction);
  const isFqHeader = (location.pathname === "/franchising/faq")
  const isContactEdit = useSelector((state: RootState) => state.contact.isEdit);
  const isContactHeader = (location.pathname === "/franchising/contact/create" || isContactEdit);
  // const [viewExpiredModal, setViewExpiredModal] = useState(false);

  let fullname = ""
  if (suffix){
    fullname = `${firstname} ${middlename} ${lastname} ${suffix}`
  } else {
    fullname = `${firstname} ${middlename} ${lastname}`
  }

  const [modalIsOpen, setModalIsOpen] = useState(false);
  const [switchModuleModal, setSwitchModuleModal] = useState(false);
  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(
    null,
  );
  const wrapperRef = useRef<any>(null)
  const [open, setOpen] = React.useState(false);
  const [placement, setPlacement] = React.useState<PopperPlacementType>();

  const handleClick = (newPlacement: PopperPlacementType) => (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
    setOpen(prev => placement !== newPlacement || !prev);
    setPlacement(newPlacement);
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
  }, [])

  useEffect(()=>{
    if(userProfile){
      setUserData(userProfile)
    }
  }, [userProfile])

  const isEmpty = (obj: any) => {
    if (!obj || typeof obj !== 'object') return true;
    for(var key in obj) {
        if(obj.hasOwnProperty(key))
            return false;
    }
    return true;
  }

  const handleClickOutside = (event: any) => {
    if (wrapperRef && (wrapperRef.current && !wrapperRef.current.contains(event.target))) {
      setOpen(false)
    }
  }

  const handleClose = () => {
    setModalIsOpen(false)
  }

  const hideSwitchModule  = moduleLists.length === 1 && moduleLists.filter(({ name }:any) => name === mainModule).length > 0;

  // useEffect(() => {
  //   let decryptedFName = "";
  //   let decryptedMName = "";
  //   let decryptedLName = "";
  //   let decryptedUserName = "";


  //   if (firstname) {
  //     decryptedFName = decryptCredentials(firstname);
  //     setFirstname(decryptedFName);
  //   }

  //   if (middlename) {
  //     decryptedMName = decryptCredentials(middlename);
  //     setMiddlename(decryptedMName);
  //   }

  //   if (lastname) {
  //     decryptedLName = decryptCredentials(lastname);
  //     setLastname(decryptedLName);
  //   }

  //   console.log('username', username);
  //   if (username) {
  //     decryptedUserName = decryptCredentials(username);
  //     setUsername(decryptedUserName)
  //   }

  //   let full;
  //   if (suffix) {
  //     full = `${decryptedFName} ${decryptedMName} ${decryptedLName} ${suffix}`
  //     setFullname(full);
  //   } else {
  //     full = `${decryptedFName} ${decryptedMName} ${decryptedLName}`
  //     setFullname(full);
  //   }

  // }, [firstname, middlename, lastname]);

  // useEffect(() => {
  //   if (!token) {
  //     setTimeout(() => {
  //       setViewExpiredModal(true)
  //     }, 1000)
  //   } else {
  //     setViewExpiredModal(false)
  //   }
  //   console.log('session modal', viewExpiredModal);
  // }, [token, viewExpiredModal]);

  const handleLogout = () => {
    // dispatch(logout())
    localStorage.removeItem('api_token');
    localStorage.removeItem('pm_token');
    localStorage.removeItem('user_id');
    window.location.replace('../index.html#/')
  };

  return (  
    <div
      className={isFCHeader? "vni-sticky vni-top-0 vni-z-10" : isFqHeader? "vni-sticky vni-top-0 vni-z-10":""}>
      <div className="logo-head no-shadow">
        <Logo />
        <div className="d-flex align-items-center">
          {/* <Notification notification={2} /> */}
          <div>{username}</div>
          {/* <div>{decryptedUserName}</div> */}
          <div className="user-details vni-flex vni-items-center">
            <Avatar style={{marginLeft: 10}} src={!isEmpty(userData) && userData?.body.profile_pic !== 'DEFAULT_PIC' ? `data:image/jpeg;base64,${userData?.body.profile_pic}` : profilepic} alt="avatar" />
            <IconButton edge="end" onClick={handleClick('bottom-end')} data-cy="expand-account-options">
              <ExpandMoreIcon />
            </IconButton>
              <Popper open={open} anchorEl={anchorEl} placement={placement} style={{ zIndex: 9998 }} transition>
                {({ TransitionProps }) => (
                <Fade {...TransitionProps} timeout={350}>
                <Paper ref={wrapperRef}>
                <div className={classes.profileContent}>
                  <Avatar style={{width: 120, height: 120, margin: '0 auto'}} src={!isEmpty(userData) && userData?.body.profile_pic !== 'DEFAULT_PIC' ? `data:image/jpeg;base64,${userData?.body.profile_pic}` : profilepic} alt="avatar" />
                  <div>
                    <h2 style={{ fontSize: 18, margin: 0, paddingBottom: 5, color: '#272E4C', fontWeight: 'bold' }}>{fullname && fullname}</h2>
                    {/* <p style={{ color: '#000000', opacity: 0.5, fontSize: 12, paddingBottom: 20, margin: 0 }}>{decryptedUserName && `@${decryptedUserName}`}</p> */}
                    <p style={{ color: '#000000', opacity: 0.5, fontSize: 12, paddingBottom: 20, margin: 0 }}>{username && `@${username}`}</p>
                  </div>
                  <Grid container spacing={1}>
                    <Grid item xs={6}>
                      <Button disableElevation className={classes.buttons} variant={'contained'} style={{ position: 'relative',background:'#3AB77D' }}>
                        <Link 
                        to={'/franchising/profile'}
                        onClick={() => setOpen(false)}
                        className={classes.linkProfile}
                        data-cy="view-profile-btn"
                        >View Profile</Link>
                      </Button>
                    </Grid>
                    <Grid item xs={6}>
                      <Button
                      disableElevation 
                      disabled = {hideSwitchModule}
                      onClick={()=>{
                        setSwitchModuleModal(true)
                        setOpen(false)
                      }}
                      className={classes.buttons}
                      style={{
                        border: '1px solid #3AB77D',
                        backgroundColor: 'transparent',
                        color: '#3AB77D',
                        fontWeight: 400
                      }}
                      data-cy="switch-module-btn"
                      variant={'contained'}
                      color={'secondary'}
                      >Switch modules</Button>
                    </Grid>
                  </Grid>
                  </div>
                  <div className={classes.profileFooter}>
                    <button
                    className={classes.logoutButton}
                    onClick={() => {
                    setModalIsOpen(true)
                    setOpen(false)
                    }}
                    data-cy="logout-btn"
                    >Log out</button> 
                  </div> 
                </Paper>
              </Fade>
              )}
            </Popper>
          </div>
        </div>
      </div>
      {isFqHeader? 
        <div className="create-franchise-request-head">
          <div>
            <h1 className="vni-font-bold vni-text-2xl">Frequently Asked Questions</h1>
          </div>
        </div>
        : null}
      {isFCHeader? <CreateFranchiseHeader /> : null}
      {isContactHeader? <ContactHeader /> : null}

      <Modal
        fullWidth={false}
        maxWidth="xl"
        open={modalIsOpen}
        onClose={handleClose}
        standard={{
          title: 'Log out',
          message: 'Are you sure you want to logout?',
          conditionalButtons: {
              buttonRight: <button className="CustomPrimaryButton  vni-mt-12" onClick={()=> handleLogout()} data-cy="logout-yes-btn"> Yes </button>,
              buttonleft: <button className="CustomPrimaryOulineButton vni-mt-12" onClick={()=> setModalIsOpen(false)} data-cy="logout-no-btn"> No </button>
            }
          }}
        />

        <SwitchModuleModal currentModule={"Franchising"}
          open={switchModuleModal}
          onClose={() => setSwitchModuleModal(false)}
          modulesList={moduleLists}
          />

      {/* <Dialog 
          id="session-modal"
          maxWidth='xs'
          open={viewExpiredModal}
          onClose={() => {
          dispatch(logout())
          window.location.replace('../index.html#/')
          }}
          className="vni-m-auto"
      >
          <DialogTitle className="vni-px-5 vni-pt-3 vni-mb-8">
          <h2 className="title">Session Expired</h2>
          </DialogTitle>
          <DialogContent>
          <p className="vni-mb-8">
          Your account has been logged out. Please try logging in again.
          </p>
          </DialogContent>
          <DialogActions className="vni-flex d-flex-center">
          <button className="CustomPrimaryButton vni-mr-5" data-cy="session-timeout-logout-btn" onClick={() => {
              dispatch(logout())
              window.location.replace('../index.html#/')
    
              }}>Okay</button>
          </DialogActions>
      </Dialog>  */}

    </div>
  );
}

export default DashboardHeader;
