import React, { useEffect, useState } from "react";
import { Switch, Route, useHistory, useLocation } from "react-router-dom";
// import IdleTimer from 'react-idle-timer';
import {
  Grid
} from '@mui/material'
// Layout Container
import DashboardSideBar from "./DashboardSideBar";
import DashboardHeader from "./DashboardHeader";

// Smart Container
import DashboardContainer from "../../../modules/Dashboard/DashboardContainer";
import FranchiseContainer from "../../../modules/Franchise/FranchiseContainer";
import ReportContainer from "../../../modules/Report/ReportContainer";
import MaintenanceContainer from "../../../modules/Maintenance/MaintenanceContainer";
import CreateFranchiseContainer from "../../../modules/Franchise/CreateFranchiseContainer";
import CreateContactContainer from "../../../modules/Contact/CreateContactContainer";
import ContactContainer from "../../../modules/Contact/ContactContainer";
import FrequentQuestions from "../FrequentQuestions";
import { useDispatch, useSelector } from "react-redux";
import { setUser,logout, getUserProfile } from "../../reducers/LoginSlice";
import { apiURL } from "../../../utils/Environment";
import { Dialog, DialogTitle, DialogContent, DialogActions } from "@mui/material";
import Spinner from "../Spinner";
import ProfileContainer from "../Profile/ProfileContainer";
import { RootState } from "../../reducers/rootReducer";

import { frsRole } from './constants';

import Idle from '../Idle'

// import { Components as XComponents } from '@hims_client/hims-core'

const DashboardLayout : React.FC = () => {
  const location = useLocation()
  const history = useHistory();
  const dispatch = useDispatch();
  const [openModal] = useState(false);
  const [counter, setCounter] = useState(60);
  const rbac = useSelector((state: RootState) => state.login.rbac);
  const {canViewDashboard,canAddFR,canEditFR,canViewBrokerAgent,canAddBrokerAgent,canEditBrokerAgent} = useSelector((state: RootState) => state.login.userPermissions);
  const [routePolicy, setRoutePolicy] = useState({
    viewDashBoard:true,
    viewFranchiseList:true,
    viewReport:true,
    viewSetting:true,
    createForm:true,
    editForm:true,
    viewBrAgtForm:true,
    createBrAgtForm:true,
    editBrAgtForm:true,
  });
  const session = useSelector((state:RootState) => state.login.auth.sessionExpired)
  const token = useSelector((state: RootState) => state.login.token);
  const mainRole = useSelector((state: RootState) => state.login.main_role);
  const mainModule = useSelector((state: RootState) => state.login.main_module);
  const subRoles = useSelector((state: RootState) => state.login.role);

  const [viewExpiredModal, setViewExpiredModal] = React.useState(false);
  

  console.log('SE', session) 
  // use to call the indexDB 
  useEffect(()=>{
    if(!session){

      dispatch(setUser())
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  },[setUser, session])

  useEffect(() => {
    if(location.pathname === '/franchising/') {
      // * (mainModule && mainModule === 'Franchising') && (mainRole && Object.keys(frsRole).includes(mainRole))
      if(mainRole && Object.keys(frsRole).includes(mainRole)) {
        //* For FRS main user
        if(viewDashBoard) history.push(`/franchising/dashboard/${frsRole[mainRole][1]}`)
      } else if(subRoles.length) {
        //* For for non FRS main user
        const frsSideRole: any = subRoles.find((i: any) => i.module === 'Franchising' && Object.keys(frsRole).includes(i.name))
        if(frsSideRole) history.push(`/franchising/dashboard/${frsRole[frsSideRole.name][1]}`)
      }
    }
    // console.log(mainRole && location.pathname === '/franchising/' && viewDashBoard, 22222)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mainRole, mainModule, location.pathname, subRoles])

  useEffect(()=>{
    if(token && !session){
      dispatch(getUserProfile(token));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  },[token, session])

  useEffect(()=>{
    if(rbac && rbac.length > 0){
      setRoutePolicy({
        ...routePolicy,
        viewDashBoard:canViewDashboard,
        createForm:canAddFR,
        editForm:canEditFR,
        viewBrAgtForm:canViewBrokerAgent,
        createBrAgtForm:canAddBrokerAgent,
        editBrAgtForm:canEditBrokerAgent,
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  },[rbac])

  // const handleClose = (e: any) => {
  //   e.preventDefault()
  //   setOpenModal(false);
  // };
  // const handleOnIdle = (e: any) => {
  //   setOpenModal(true);
  //   setCounter(60);
  // };
  // const handleResetCounter = (e: any) => {
  //   setOpenModal(false);
  // };
  useEffect(() => {
    if(openModal){
      counter > 0 && setTimeout(() => setCounter(counter - 1), 1000);
      if(counter === 0){
        dispatch(logout());
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [counter, openModal]);

  const {viewDashBoard,createForm,editForm,viewBrAgtForm,createBrAgtForm,editBrAgtForm} = routePolicy

  const getBaseUrl = () : string => {
    return `${apiURL.userManagement}/` || window.localStorage.CLIENT_URL || ''
  }

  const checkToken = (accessToken: any) => {
    let URL = apiURL.userManagement + '/underwriting/user/check'

    return fetch(URL, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + accessToken,
        }}).then(async (res: any) => {
            if (!res.ok) {
                let respjson = await res.json()
                let errormessage = respjson.error.message
                throw Error(errormessage)
            }
            return res ? res.json() : null
        })
  }

  const isTokenValid = async () => {
    let isValid = true,
    isPmValid = true,
    isApiValid = true;
    
    if (!localStorage.getItem('api_token') || !localStorage.getItem('pm_token')) {
      isValid = false;
    } else if (
      localStorage.getItem('api_token') &&
      localStorage.getItem('pm_token')
    ) {
      try {
        let api_token = await checkToken(localStorage.getItem('api_token'));
        let pm_token = await checkToken(localStorage.getItem('pm_token'));
        if (!api_token.status) {
          isApiValid = false;
        }
        if (!pm_token.status) {
          isPmValid = false;
        }
      } catch (error) {
        console.log(error, 'error');
        isPmValid = false;
        isApiValid = false;
      }
    }

    console.log('TOKEN VALIDITY', isValid, isPmValid, isApiValid);
    if (!isValid || !isPmValid || !isApiValid) {
      // console.log('INVALID TOKEN');
      // var a = '../customer-care/index.html#/franchising' + '/invalidSession';
      // window.location.href = a;
      //onLogout();
      setViewExpiredModal(true);
    }
  };

  React.useEffect(() => {
    isTokenValid()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token])

  return (
    <>
    <Grid container direction="row" justifyContent="flex-start">
      {/* <IdleTimer
        element={document}
        onActive={() => { console.log('User is Active') }}
        onIdle={handleOnIdle}
        timeout={idleTime.minutes}
      > */}
        <Spinner />
        <Grid item xs={1} style={{maxWidth: 73, backgroundColor: '#1e2071', minHeight: '100vh', padding: '0 8px'}}><DashboardSideBar /></Grid>
        <Grid item xs={11} style={{maxWidth: 'calc(100% - 73px)', flexBasis: 'calc(100% - 73px)'}}>
          <DashboardHeader />
          <Switch>
            {viewBrAgtForm && !viewDashBoard && <Route exact path="/franchising/" component={ContactContainer} />}
            {createForm && <Route exact path="/franchising/franchise/create" component={CreateFranchiseContainer}/>}
            {editForm && <Route exact path="/franchising/franchise/edit/:id" component={CreateFranchiseContainer}/>}
            {viewDashBoard && <Route path="/franchising/dashboard/" component={DashboardContainer} />}
            {viewDashBoard && <Route path="/franchising/franchise/" component={FranchiseContainer} />}
            {token && <Route path="/franchising/report/" component={ReportContainer} />}
            {token && <Route exact path="/franchising/faq" component={FrequentQuestions} />}
            {token && <Route exact path="/franchising/maintenance" component={MaintenanceContainer} />}
            {token && <Route exact path="/franchising/profile" component={ProfileContainer} />}
            
            {viewBrAgtForm && <Route exact path="/franchising/contact" component={ContactContainer} />}
            {createBrAgtForm && <Route exact path="/franchising/contact/create" component={CreateContactContainer} />}
            {editBrAgtForm && <Route exact path="/franchising/contact/edit/:id" component={CreateContactContainer} />}
            {/* <Route path="/franchising/*">Unauthorized Access</Route> */}
          </Switch>
        </Grid>
        {/* <Dialog 
          id="session-modal"
          maxWidth='xs'
          open={openModal}
          onClose={handleClose}
          className="vni-m-auto"
        >
          <DialogTitle className="vni-px-5 vni-pt-3 vni-mb-8">
            <h2 className="title">Session Expiring</h2>
          </DialogTitle>
          <DialogContent>
            <p className="vni-mb-8">Your session is about to expire due to inactivity. You will be logged out in {counter} seconds.</p>
          </DialogContent>
          <DialogActions className="vni-flex d-flex-center">
            <button className="CustomPrimaryOulineButton vni-mr-5" data-cy="session-timeout-logout-btn" onClick={() => {dispatch(logout())}}>Logout</button>
            <button className="CustomPrimaryButton" data-cy="session-timeout-continue-btn" onClick={handleResetCounter}>Continue Session</button>
          </DialogActions>
        </Dialog> */}
      {/* </IdleTimer> */}
      {session && (
        <Dialog 
          id="session-modal"
          maxWidth='xs'
          open={session}
          onClose={() => {
            dispatch(logout())
            window.location.replace('../index.html#/')
          }}
          className="vni-m-auto"
          style={{
            zIndex: 999
          }}
        >
          <DialogTitle className="vni-px-5 vni-pt-3 vni-mb-8">
            <h2 className="title">Session Expired</h2>
          </DialogTitle>
          <DialogContent>
            <p className="vni-mb-8">
            Your account has logged in on another deviceqwe. Please contact your administrator if it was not you.
            </p>
          </DialogContent>
          <DialogActions className="vni-flex d-flex-center">
            <button className="CustomPrimaryButton vni-mr-5" data-cy="session-timeout-logout-btn" onClick={() => {
              dispatch(logout())
              window.location.replace('../index.html#/')
              
              }}>Okay</button>
          </DialogActions>
        </Dialog>
      )}

        <Idle url={getBaseUrl()} />
    </Grid>
    {viewExpiredModal === true && (

      <Dialog 
          id="session-modal-expired"
          maxWidth='xs'
          open={viewExpiredModal}
          onClose={() => {
          dispatch(logout())
          window.location.replace('../index.html#/')
          }}
          className="vni-m-auto"
      >
          <DialogTitle className="vni-px-5 vni-pt-3 vni-mb-8">
            <h2 className="title">Session Expired</h2>
          </DialogTitle>
          <DialogContent>
            <p className="vni-mb-8">
            Your account has been logged out. Please try logging in again.
            </p>
          </DialogContent>
          <DialogActions className="vni-flex d-flex-center">
            <button className="CustomPrimaryButton vni-mr-5" data-cy="session-timeout-logout-btn" onClick={() => {
                dispatch(logout())
                window.location.replace('../index.html#/')

                }}>Okay</button>
          </DialogActions>
      </Dialog>
      )};
    </>
  );
}
export default DashboardLayout;