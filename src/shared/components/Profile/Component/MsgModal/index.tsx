import React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Grid from '@mui/material/Grid';
import { makeStyles } from '@mui/styles';
import { Theme } from '@mui/material/styles';
import clsx from 'clsx';

const useStyles = makeStyles((theme: Theme) => ({
	dialog: {
	},
	dialogTitle: {
		fontWeight: 600,
	},
	dialogContent: {
		minWidth: 320,
	},
	dialogAction: {
		marginTop: 16,
		marginBottom: theme.spacing(2),
	},
    okayButton: {
        background: '#3AB77D',
        color: '#FFFFFF',
        minWidth: 140,
        '&:hover': {
            background: '#3AB77D'
        }
    },
}));

interface MsgModalProps {
  isModalOpen: boolean;
  onClose: () => void;
  title: string;	
  message: any;
  messageMaxWidth?: string;
}

const MsgModal = (props: MsgModalProps)=> {
	const classes = useStyles();
    const reset = () => {};
    
	const handleClose = () => {
        reset();
        props.onClose();
    };

	return (
		<Dialog
			open={props.isModalOpen}
			onClose={handleClose}
			aria-labelledby="form-dialog-title"
			maxWidth="sm"
			className={classes.dialog}
		>
			<DialogTitle id="form-dialog-title" className={classes.dialogTitle}>{props.title}</DialogTitle>
			<DialogContent className={classes.dialogContent}>
				<Grid container spacing={2} justifyContent="space-between" alignItems="flex-start">
					<Grid item xs={12}>
						<span style={{display: "inline-block", maxWidth: props.messageMaxWidth}}>{props.message}</span>
					</Grid>
				</Grid>
			</DialogContent>
			<DialogActions className={clsx(classes.dialogAction)}>
				<Grid container spacing={2} justifyContent="center" alignItems="flex-start">
					<Grid item xs={4}>
						<Button
							data-cy="okay-btn"
							className={classes.okayButton}
							onClick={handleClose}
							variant="contained"
							fullWidth
							sx={{
								backgroundColor: '#3AB77D',
								color: '#FFFFFF',
								'&:hover': {
									backgroundColor: '#3AB77D'
								}
							}}>
                            Okay
						</Button>
					</Grid>
				</Grid>
			</DialogActions>
		</Dialog>
	);
};

MsgModal.defaultProps = {
	isModalOpen: false,
};

export default MsgModal;

