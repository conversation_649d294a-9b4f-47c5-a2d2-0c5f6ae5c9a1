import * as React from 'react';
import {
    Grid,
    Typography,
    Button,
} from '@mui/material';
import { makeStyles } from '@mui/styles';
import moment from 'moment'
import { ChangePassword } from './ChangePassword';

const useStyles = makeStyles(() => ({
    label: {
        color: '#272E4C',
        fontSize: 12,
        opacity: .6
    },
    value: {
        color: '#272E4C',
        fontSize: 16,
        fontWeight: 800
    }
}));

export interface IAccountsProps {
    userData: any,
    userIdDb: string,
    otherRoles?: any,
    // url: string | undefined;
    // setLoading: any
}

export const Account: React.FC<IAccountsProps> = (props: IAccountsProps)=> {
    const classes = useStyles();
    let {userData} = props
    const isEmpty = (obj: any) => {
        for(var key in obj) {
            if(obj.hasOwnProperty(key))
                return false;
        }
        return true;
    }
    let userGroup = !isEmpty(userData) && userData.body.group ? userData.body.group : {} 

    const [ChangePasswordModalVisibility, setChangePasswordModalVisibility] = React.useState(false)

    const openChangePasswordModal = () => {
        setChangePasswordModalVisibility(true)
    }

    return (
        <>
            <Grid container spacing={5}>
                <Grid item xs={12}>
                    <Typography><b>ACCOUNT</b></Typography>
                </Grid>
                <Grid item xs={6}>
                    <Typography className={classes.label}>USERNAME</Typography>
                    <Typography className={classes.value}>{!isEmpty(userData) ? userData.body.username : "N/A"}</Typography>
                </Grid>
                <Grid item xs={6}>
                    <Typography className={classes.label}>PRIMARY ROLE AND MODULE</Typography>
                    <Typography className={classes.value}>{!isEmpty(userData) && userData.body.main_role && userData.body.main_role.description + " - " + userData.body.main_module}</Typography>
                </Grid>
            </Grid>
            <Grid container spacing={5}>
                <Grid item xs={6}>
                    <Typography className={classes.label}>EMAIL</Typography>
                    <Typography className={classes.value}>{!isEmpty(userData) && userData.body.email ? userData.body.email : "N/A"}</Typography>
                </Grid>
                <Grid item xs={6}>
                    <Typography className={classes.label}>USER GROUP</Typography>
                    <Typography className={classes.value}>{!isEmpty(userData) && isEmpty(userGroup) ? "N/A" : userGroup.name}</Typography>
                </Grid>
            </Grid>
            <Grid container spacing={5}>
                <Grid item xs={6}>
                    <Typography className={classes.label}>PASSWORD</Typography>
                    <Typography className={classes.value}>{"*****"}</Typography>
                    {
                      
                               !isEmpty(userData) && props.userIdDb === userData.body._id &&
                            <>
                                 <Button sx={{ textTransform: 'none', padding: 0, color: '#3AB77D', textDecoration: 'underline', fontSize: 12, cursor: 'pointer' }} onClick={() => {
                                      openChangePasswordModal()
                                  }}>Change Password</Button>
                                <ChangePassword
                                    // setLoading={props.setLoading}
                                    // url={props.url}
                                    userId={userData.body._id}
                                    visible={ChangePasswordModalVisibility}
                                    onClose={() => { setChangePasswordModalVisibility(false) }} />
                            </> 
                       
                        }
                      
                    

                </Grid>
                <Grid item xs={6}>
                    <Typography className={classes.label}>ROLES FROM USER GROUPS</Typography>
                    <Typography className={classes.value}><u>
                        {
                            !isEmpty(userData) && userData.body.hasOwnProperty('group_roles') ? userData.body.group_roles.length > 0 ?
                                userData.body.group_roles.map((d: any, i: any) =>
                                    `${d.description}${i !== (userData.body.group_roles.length - 1) ? ', ' : ''}`
                                ) : 'N/A' : 'N/A'
                        }
                    </u></Typography>
                </Grid>
            </Grid>
            <Grid container spacing={5}>
                <Grid item xs={6}>
                    <Typography className={classes.label}>STATUS</Typography>
                    <Typography className={classes.value}><b>{!isEmpty(userData) && userData.body.is_locked && userData.body.is_locked ? "Locked" : "Unlocked"}</b></Typography>
                    {/* <Typography className={classes.linkAction}>Lock</Typography>
                        {userData.body.is_locked ? <Link>Unlock User</Link> : null} */}
                </Grid>
                <Grid item xs={6}>
                    <Typography className={classes.label}>OTHER ROLES</Typography>
                    <Typography className={classes.value}>
                        <u>
                            {
                                !isEmpty(userData) && userData.body.hasOwnProperty('roles') ? userData.body.roles.length > 0 ?
                                    userData.body.roles.map((d: any, i: any) =>
                                        `${d.hasOwnProperty('description') ? d.description : d.name}${i !== (userData.body.roles.length - 1) ? ', ' : ''}`
                                    ) : 'N/A' : 'N/A'
                            }
                        </u>
                    </Typography>
                </Grid>
            </Grid>
            <Grid container spacing={5}>
                <Grid item xs={3}>
                    <Typography className={classes.label}>SUPERVISOR</Typography>
                    {
                        !isEmpty(userData) && userData.body.reports_to ?
                            <>
                                <Typography className={classes.value}>{userData.body.reports_to.first_name} {userData.body.reports_to.last_name}</Typography>
                                <Typography style={{ color: 'rgba(60, 57, 74, 0.7)' }}><small><b>{userData.body.reports_to.job_title ? userData.body.reports_to.job_title : 'N/A'}</b></small></Typography>
                                <Typography style={{ color: 'rgba(60, 57, 74, 0.7)' }}><small><i>{userData.body.reports_to.employee_id ? userData.body.reports_to.employee_id : 'N/A'}</i></small></Typography>
                            </>
                            : <Typography className={classes.value}>N/A</Typography>
                    }
                </Grid>
            </Grid>
            <Grid container spacing={5} style={{ paddingBottom: 30 }}>
                <Grid item xs={3}>
                    <Typography className={classes.label}>EFFECTIVITY DATE</Typography>
                    <Typography className={classes.value}>{!isEmpty(userData) && userData.body.effectivity_date ? moment(userData.body.effectivity_date).format('MMM DD, YYYY') : 'N/A'}</Typography>
                </Grid>
                <Grid item xs={3}>
                    <Typography className={classes.label}>EXPIRY DATE (If applicable)</Typography>
                    <Typography className={classes.value}>{!isEmpty(userData) && userData.body.expiry_date ? moment(userData.body.expiry_date).format('MMM DD, YYYY') : 'N/A'}</Typography>
                </Grid>
            </Grid>

            <Grid container spacing={5}>
                <Grid item xs={12}>
                    <Typography><b>PROFILE</b></Typography>
                </Grid>
                <Grid item xs={3}>
                    <Typography className={classes.label}>FIRST NAME</Typography>
                    <Typography className={classes.value}>{!isEmpty(userData) && userData.body.first_name ? userData.body.first_name : "N/A"}</Typography>
                </Grid>
                <Grid item xs={3}>
                    <Typography className={classes.label}>MIDDLE NAME</Typography>
                    <Typography className={classes.value}>{!isEmpty(userData) && userData.body.middle_name ? userData.body.middle_name : "N/A"}</Typography>
                </Grid>
                <Grid item xs={3}>
                    <Typography className={classes.label}>LAST NAME</Typography>
                    <Typography className={classes.value}>{!isEmpty(userData) && userData.body.last_name ? userData.body.last_name : "N/A"}</Typography>
                </Grid>
                <Grid item xs={3}>
                    <Typography className={classes.label}>Suffix</Typography>
                    <Typography className={classes.value}>{!isEmpty(userData) && userData.body.hasOwnProperty('suffix') && userData.body.suffix !== '' ? userData.body.suffix : "N/A"}</Typography>
                </Grid>
            </Grid>
            <Grid container spacing={5}>
                <Grid item xs={3}>
                    <Typography className={classes.label}>EMPLOYEE ID NUMBER</Typography>
                    <Typography className={classes.value}>{!isEmpty(userData) && userData.body.employee_id ? userData.body.employee_id : "N/A"}</Typography>
                </Grid>
                <Grid item xs={3}>
                    <Typography className={classes.label}>DEPARTMENT</Typography>
                    <Typography className={classes.value}>{!isEmpty(userData) && userData.body.department ? userData.body.department.name : "N/A"}</Typography>
                </Grid>
                <Grid item xs={3}>
                    <Typography className={classes.label}>JOB POSITION / RANK</Typography>
                    <Typography className={classes.value}>{!isEmpty(userData) && userData.body.job_title ? userData.body.job_title : "N/A"}</Typography>
                </Grid>
            </Grid>
            <Grid container spacing={5}>
                <Grid item xs={3}>
                    <Typography className={classes.label}>SECTION</Typography>
                    <Typography className={classes.value}>{!isEmpty(userData) && userData.body.section ? userData.body.section : "N/A"}</Typography>
                </Grid>
                <Grid item xs={3}>
                    <Typography className={classes.label}>LOCATION</Typography>
                    <Typography className={classes.value}>{!isEmpty(userData) && userData.body.location ? userData.body.location : "N/A"}</Typography>
                </Grid>
            </Grid>
            {/* BEGIN MODALS */}

        </>
    )
}