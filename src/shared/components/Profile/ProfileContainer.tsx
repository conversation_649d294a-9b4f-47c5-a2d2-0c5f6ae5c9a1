import React, {useState, useEffect} from 'react'
// import { useLocation } from 'react-router-dom'
import { SubHeader } from './Component/SubHeader';
import { accountsPageStyles } from './style';
import {
	Grid,
	IconButton,
	CircularProgress,
	Avatar,
	Fade,
	Backdrop,
	Modal,
	List,
	Link,
	ListItemText,
	Button,
	Dialog,
	DialogTitle,
	DialogContent,
	DialogActions
} from '@mui/material'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faTimes } from '@fortawesome/free-solid-svg-icons'
import CreateIcon from '@mui/icons-material/Create'

import { useSelector, useDispatch } from "react-redux";
import { RootState } from "../../reducers/rootReducer";
import {Account} from "./Component/Account"
// import { iProfile } from '../../models/User';

import profilepic from "../../../assets/images/profilepic.png"
import { ImageCropper } from './ProfilePicCropper';
import FloatingButtons from './FloatingButtons';
import MsgModal from './Component/MsgModal';
import { logout, updateProfilePic } from '../../reducers/LoginSlice';
import { setIsRequesting } from '../../reducers/FranchiseSlice';
import { updateProfileImageIndexDb } from '../../../utils/CoreApi';
import { apiURL } from '../../../utils/Environment';

interface IProps {
}

const ProfileContainer: React.FC<IProps> = (props: IProps) => {

    const classes = accountsPageStyles();
    const dispatch = useDispatch();
	const token = useSelector((state: RootState) => state.login.token);

    const userProfile = useSelector((state: RootState) => state.login.profile);
    const [userData, setUserData] = useState<any>()
	const user_id = useSelector((state: RootState) => state.login.user_id);
	const isRequesting = useSelector((state: RootState) => state.franchise.isRequesting);
    // const [name, setName] = useState("");
	const [viewExpiredModal, setViewExpiredModal] = React.useState(false);

    const [userIdDb, setUserIdDb] = useState('');

    useEffect(()=>{
        if(userProfile){
		  setUserData(userProfile)
		  setOriginalData(userProfile)
          window.scrollTo(0, 0);
        }
	  }, [userProfile])
	  
	useEffect(()=>{
		if(user_id){
			setUserIdDb(user_id)
		}
	},[user_id])

    const isEmpty = (obj: any) => {
		for(var key in obj) {
			if(obj.hasOwnProperty(key))
			return false;
    	}
        return true;
    }

    
    const breadCrumbs = [
		{
			name: "ACCOUNT",
			link: "/system-admin/users"
		},
	];

	// Upload profile picture
	const [uploadModal, setUploadModal] = React.useState(false);
	const [temp64, setTemp64] = React.useState<any>(null);
	const [originalData, setOriginalData] = React.useState<any>({});
	const [modalProps, setModalProps] = React.useState<any>({
		open: false,
		title: '',
		message: '',
		onClose: () => {}
	});

	const handleUpload = (imgBase64: any) => {
		setUserData({
		  ...userData,
		  body: {...userData.body, profile_pic: (imgBase64 as string).split(',')[1]}
		//   profile_pic: (imgBase64 as string).split(',')[1]
		})
		setUploadModal(false)
	}

	const openUploadModal = () => {
		setUploadModal(true)
	}

	const closeUploadModal = () => {
		setUploadModal(false)
		setUserData(originalData)
		setTemp64(null)
	}

 	const handleAccountPhoto = (event: any) => {
 		const validFiles = ['image/jpeg', 'image/jpg', 'image/png'];

 		if (validFiles.includes(event[0].type)) {
			let reader = new FileReader();
			reader.readAsDataURL(event[0]);
			reader.onload = () => {
			  setTemp64(reader.result)
		  };
		} else {
			alert("Invalid file extension.")
		}
	};

	const updateProfile = async () => {
		// let data = JSON.parse(JSON.stringify(userData))

		// let payload = {
		// 	audit_logs: {
		// 		field_name: 'profile_pic',
		// 		old_value: userData.body.profile_pic,
		// 		newValue: data.body.profile_pic
		// 	},
		// 	body: data.body
		// }
		let newProfilePic = {profile_pic: userData.body.profile_pic}

		const updateImage = dispatch(updateProfilePic(newProfilePic))
		Promise.all([updateImage])
        .then((completed: any) => {
			dispatch(setIsRequesting(false))
            if(completed){
                for (let index = 0; index < completed.length; index++) {
                    if(completed[index]){
						const profileImg = newProfilePic.profile_pic
						Promise.all([updateProfileImageIndexDb(profileImg)])
						.then(()=>{
							setModalProps({
								...modalProps,
								open: true,
								title: 'Changes Saved',
								message: 'Your profile picture has been changed.',
								onClose: () => window.location.reload()
							})
						}).catch((error)=>{
							setModalProps({
								...modalProps,
								open: true,
								title: 'Error',
								message: 'Failed to update profile image in database.',
								onClose: () => {setModalProps({...modalProps, open: false})}
							})
						})

                    }
                }
            }
   
        }).catch((err: any) => {
            dispatch(setIsRequesting(false))
			// alert(err)
			setModalProps({
				...modalProps,
				open: true,
				title: 'Error',
				message: err?.message || 'An error occurred while updating profile picture.',
				onClose: () => {setModalProps({...modalProps, open: false})}
			})
        })

	}

	const checkToken = (accessToken: any) => {
		let URL = apiURL.userManagement + '/underwriting/user/check'
	
		return fetch(URL, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Authorization: 'Bearer ' + accessToken,
			}}).then(async (res: any) => {
				if (!res.ok) {
					let respjson = await res.json()
					let errormessage = respjson.error.message
					throw Error(errormessage)
				}
				return res ? res.json() : null
			})
	  }
	
	  const isTokenValid = async () => {
		let isValid = true,
		isPmValid = true,
		isApiValid = true;
		
		if (!localStorage.getItem('api_token') || !localStorage.getItem('pm_token')) {
		  isValid = false;
		} else if (
		  localStorage.getItem('api_token') &&
		  localStorage.getItem('pm_token')
		) {
		  try {
			let api_token = await checkToken(localStorage.getItem('api_token'));
			let pm_token = await checkToken(localStorage.getItem('pm_token'));
			if (!api_token.status) {
			  isApiValid = false;
			}
			if (!pm_token.status) {
			  isPmValid = false;
			}
		  } catch (error) {
			console.log(error, 'error');
			isPmValid = false;
			isApiValid = false;
		  }
		}
	
		console.log('TOKEN VALIDITY', isValid, isPmValid, isApiValid);
		if (!isValid || !isPmValid || !isApiValid) {
		  // console.log('INVALID TOKEN');
		  // var a = '../customer-care/index.html#/franchising' + '/invalidSession';
		  // window.location.href = a;
		  //onLogout();
		  setViewExpiredModal(true);
		}
	  };
	
	  React.useEffect(() => {
		const validateToken = async () => {
			await isTokenValid();
		};
		validateToken();
		// eslint-disable-next-line react-hooks/exhaustive-deps
	  }, [token])	

    return (

        <>
			<Modal
              aria-labelledby="transition-modal-title"
              aria-describedby="transition-modal-description"
              className={classes.modal}
              open={uploadModal}
              onClose={closeUploadModal}
              closeAfterTransition
              BackdropComponent={Backdrop}
              BackdropProps={{
                timeout: 500,
              }}>
              	<Fade in={uploadModal}>
					<div className={classes.paper}>
						<List>
							<ListItemText>
							<Link
								data-cy="close-profilepic-upload-modal"
								className={classes.closeButton}
								onClick={closeUploadModal}>
								<FontAwesomeIcon icon={faTimes} className='' />
							</Link>
							</ListItemText>
						</List>
						<p id="transition-modal-title"
							style={{ fontSize: "20px" }}>
							<b>Upload Picture</b>
						</p>
 						<ImageCropper
 							onUpload={handleUpload}
 							photo={temp64}
 							handleAccountPhoto={handleAccountPhoto}
 							classes={classes}
 							classes2={classes}
 							setPhoto={setTemp64}
 							closeUploadModal={closeUploadModal} />
					</div>
              	</Fade>
            </Modal>

			<SubHeader
				headingTitle={!isEmpty(userData) ? userData?.body.full_name??"N/A" : "N/A"}
				breadCrumbsArray={breadCrumbs}
				layoutType={'layout_1'}
			// enableButton={false}
			></SubHeader>
				{isRequesting ?
					( 
						<div className={classes.root}>
							<div className={classes.container}>
								<CircularProgress size={150}/>
							</div>
						</div>
					) : null
				}
            <div className={classes.main}>
            <Grid container xs={12}>
					<Grid item xs={3} className={classes.sideBarContainer}>
						<IconButton edge="end" onClick={() => { }}>
							<Avatar
								className={classes.userImage}
								src={!isEmpty(userData) && userData?.body.profile_pic !== 'DEFAULT_PIC' ? `data:image/jpeg;base64,${userData?.body.profile_pic}` : profilepic}
								alt="avatar" />
							<span
								onClick={openUploadModal}
								className={classes.editIconContainer}>
								<CreateIcon className={classes.editIcon} />
							</span>
						</IconButton>
					</Grid>
					<Grid item xs={9} className={classes.contentContainer}>
						{
							<Account
								// setLoading={setLoading}
								// url={props.url}
								userData={userData}
								userIdDb={userIdDb}
							></Account>
						}
					</Grid>
				</Grid>
            </div>
			{
				temp64 && temp64 !== null &&
				<FloatingButtons
					rightButtons={
						<Grid item xs={12} className={classes.userAlignRight}>
							<Button
								data-cy="cancel-floatingbtn"
								onClick={() => { setTemp64(null); setUserData(originalData); }}
								className={classes.cancelAdd}
								variant="contained" color="primary">
								Cancel
							</Button>
							<Button
								onClick={updateProfile}
								className={classes.rightButton}
								data-cy="save-floatingbtn">
								Save Changes
							</Button>
						</Grid>
					} />
			}

			<MsgModal
				isModalOpen={modalProps.open}
				onClose={modalProps.onClose}
				title={modalProps.title}
				message={modalProps.message} />

				
		{viewExpiredModal === true && (
			<Dialog 
				id="session-modal-expired"
				maxWidth='xs'
				open={viewExpiredModal}
				onClose={() => {
				dispatch(logout())
				window.location.replace('../index.html#/')
				}}
				className="vni-m-auto"
			>
				<DialogTitle className="vni-px-5 vni-pt-3 vni-mb-8">
				<h2 className="title">Session Expired</h2>
				</DialogTitle>
				<DialogContent>
				<p className="vni-mb-8">
				Your account has been logged out. Please try logging in again.
				</p>
				</DialogContent>
				<DialogActions className="vni-flex d-flex-center">
				<button className="CustomPrimaryButton vni-mr-5" data-cy="session-timeout-logout-btn" onClick={() => {
					dispatch(logout())
					window.location.replace('../index.html#/')

					}}>Okay</button>
				</DialogActions>
			</Dialog>
			)};
        </>
    )

}

export default ProfileContainer;