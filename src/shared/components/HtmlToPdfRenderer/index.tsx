import * as React from 'react';
import $ from "jquery";
import moment from 'moment';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import { Margins } from 'pdfmake/interfaces';
var htmlToPdfmake = require("html-to-pdfmake");
(pdfMake as any).vfs = pdfFonts.pdfMake.vfs;

export interface HtmlToPDFProps {
    fileName: string,
    renderType?: string,
    resetFlag?: any,
    fileType: string,
    clientID: string,
    clientName: string,
    dateTo: string,
    dateFrom: string,
    reportGeneratedAt: string,
    print:boolean,
    download:boolean,
    onStartDownload: () => void,
    onStartPrint: () => void,
    onError: () => void,
    defaultStyles?: any,
    styles?: any,
    user: any,
    generator:any
    isDTR: boolean
}

export class HtmlToPDFRenderer extends React.Component<HtmlToPDFProps>  {

    componentDidMount() {
        this.printRender();
    }

    clearExcessCell() {
        let thead = document.getElementsByTagName("thead")
        if(thead.length){
            let tr = thead[0].getElementsByTagName("tr")
            if(tr.length > 1){
                thead[0].getElementsByTagName("tr")[1].remove()
            }
        }
    }

    printRender = () => {
        this.clearExcessCell();
        var htmlData = $("#preview_data").html();
        var previewFrame = document.getElementById('exportthis');
        try {
            if(htmlData){
                this.printPdf(htmlData,previewFrame);
            }
        } catch (e){
            console.log(e)
            this.props.onError();
        }
    }
    /**
     * Handle Conversion of HTML Element into PDF using html-to-pdfmake,pdfmake
     * @reference https://stackoverflow.com/questions/37449905/generate-pdf-with-pdfmake-in-iframe-tag
     * @reference https://stackoverflow.com/questions/24535799/pdf-js-and-viewer-js-pass-a-stream-or-blob-to-the-viewer#:~:text=open(url%2C%200)%3B,viewerUrl%20%3D%20'web%2Fviewer.
     * @reference https://github.com/mozilla/pdf.js/issues/10435
     * @param htmlData string @param frame HTMLElement
     * <AUTHOR>
     */ 
    printPdf = (htmlData: any,frame: any) => {

        var html = htmlToPdfmake(htmlData, {
            defaultStyles: this.props.defaultStyles
        });
        let date = "Generated on: " + moment(this.props.reportGeneratedAt).format("LLLL").toString()
        let x:any = [50,20];
        let y:any = [50,50];
        let dateRange = `Date: ${this.props.dateFrom} to ${this.props.dateTo}`;
        localStorage.setItem('fileDateRange', `${this.props.dateFrom} to ${this.props.dateTo}`); 
        let align: "right" = "right" 
        let { isSpecialist } = this.props.user
        let generatorName = this.props.generator
        let isDTR = this.props.isDTR


        const header = () => {
            return{ stack: [
                isSpecialist || generatorName === "FRF_VALIDATOR" || isDTR ? "Daily Transaction Report" : "Franchise Report",
              dateRange,
            ],
            margin: x as Margins,
            bold: true,
            }
        };
        
        const footer = (currentPage: number, pageCount: number) =>  {
            return { 
                columns: 
                    [  
                    { text: date }
                    ,{ text: "Page " +  currentPage + " of " + pageCount, alignment: align }
                    ],
                margin: x as Margins
            }
        };

        var docDefinition = {
            header: header,
            footer: footer,
            content: [
                html
            ],
            pageMargins: y,
            defaultStyle: {fontSize:8},
            pageBreakBefore: function (currentNode: any) {
                return currentNode.style && currentNode.style.indexOf('pdf-pagebreak-before') > -1;
            }
        };
        var pdfDocGenerator = pdfMake.createPdf(docDefinition);
        let fileName =  `${this.props.fileName}.${this.props.fileType}`;
        pdfDocGenerator.getBase64((data) => {
            localStorage.setItem('fileBlobData', data); 
        });
        pdfDocGenerator.getBuffer(function (blob:any) { 
            var file = new Blob([blob], { type: 'application/pdf'});
            var url = URL.createObjectURL(file);
            console.log('FILE', file, 'URL', url);
            var filePath = encodeURIComponent(url+"#"+fileName);
            
            // electron only
            var viewerUrl = `pdf.js/web/viewer.html?file=${filePath}`;  
            // end electron only

            // debugging purposes local
            // console.log('frame filePath', filePath)
            // const viewerUrl = document.location.origin + '/' + 'pdf.js/web/viewer.html?file=' + encodeURIComponent(url+"#"+fileName);
            // console.log('frame viewerUrl', viewerUrl)
            // end debugging purposes local 
            
            localStorage.setItem('fileNamePdf', fileName); 
            
            frame.setAttribute('src',viewerUrl);
            frame.setAttribute('width',"100%"); 
            frame.setAttribute('height',"100%");
            console.log('PDF Preview created');
        });
        
        if(this.props.download) {
            pdfDocGenerator.download(fileName);
            this.props.onStartDownload();
        }
        if(this.props.print) {
            pdfDocGenerator.print();
            this.props.onStartPrint();
        }
    }
   
    render() {
        return (
            <>
            </>
        )
    }
}