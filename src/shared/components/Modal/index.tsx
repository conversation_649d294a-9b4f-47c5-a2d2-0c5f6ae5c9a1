import React from 'react'

// Material UI imports
import {
    Grid,
    Dialog,
    Typography
} from '@mui/material'

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
    faTimes
} from '@fortawesome/free-solid-svg-icons'

// Relative imports
import {
    modalStyles
} from './style'

interface IProps {
    open: true | false | false
    fullWidth: true | false | false
    onClose: () => void
    children?: React.ReactNode
    standard?: {
        title: string
        message: string
        buttonComponent?: React.ReactElement | any
        conditionalButtons?: {
            buttonleft: React.ReactElement | any
            buttonRight: React.ReactElement | any
        }
    }
    maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false
    disableBackdropClick?:boolean
    disableEscapeKeyDown?:boolean
    hideCloseButton?:boolean
    whereThisIsUsed?:string
}

export const Modal: React.FC<IProps> = (props: IProps) => {
    const {
        open,
        onClose,
        standard,
        maxWidth,
        children,
        fullWidth,
        disableEscapeKeyDown,
        hideCloseButton,
        whereThisIsUsed
    } = props

    const classes = modalStyles()

    return (
        <Dialog 
            maxWidth={maxWidth??'lg'}
            classes={{paper: classes.paper, paperWidthLg: classes.paperLg}}
            fullWidth={fullWidth}
            open={open}
            onClose={onClose}
            disableEscapeKeyDown={disableEscapeKeyDown??false}
            >
                    {
                        !hideCloseButton &&  <FontAwesomeIcon
                                                data-cy={whereThisIsUsed ? `${whereThisIsUsed}-close-modal` : "close-modal"}
                                                onClick={onClose}
                                                className={classes.closeIcon}
                                                icon={faTimes} />
                    }
                    <div className={classes.modalMain}>
                        {
                            standard ?
                                <div>
                                    <Typography className={classes.title}> {standard.title}</Typography>
                                    <Typography className={classes.messageBody}> {standard.message}</Typography>
                                    {
                                        standard.buttonComponent &&
                                        !standard.conditionalButtons && 
                                        <>
                                            <Grid container>
                                                <Grid
                                                    style={{textAlign: 'center'}}
                                                    item
                                                    xs={12}>
                                                    {standard.buttonComponent}
                                                </Grid>
                                            </Grid>
                                        </>
                                    }
                                    {
                                        !standard.buttonComponent &&
                                        standard.conditionalButtons && 
                                        <>
                                            <Grid
                                                container
                                                justifyContent="center"
                                                alignItems="center"
                                                spacing={4}>
                                                <Grid
                                                    style={{textAlign: 'right'}}
                                                    item
                                                    xs={6}>
                                                    {standard.conditionalButtons.buttonleft}
                                                </Grid>
                                                <Grid
                                                    style={{textAlign: 'left'}}
                                                    item
                                                    xs={6}>
                                                    {standard.conditionalButtons.buttonRight}
                                                </Grid>
                                            </Grid>
                                        </>
                                    }
                                </div>
                            :  children 

                        }
                    </div>
        </Dialog>
    )
}