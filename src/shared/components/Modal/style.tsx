import { makeStyles } from '@mui/styles';
import { Theme } from '@mui/material/styles';

const modalStyles = makeStyles((theme: Theme) => ({
    modalMain: {
        paddingLeft: theme.spacing(4),
        paddingRight: theme.spacing(4),

    },
    closeIcon: {
        position: 'absolute',
        cursor: 'pointer',
        fontSize: 20,
        right: 20,
        top: 27,
        color: '#555555'
    },
    title: {
        fontSize: 19,
        fontWeight: 800,
        color: '#272E4C',
        marginBottom: theme.spacing(2),
        fontFamily: 'usual'
    },
    messageBody: {
        fontSize: 14,
        color: '#272E4C',
        marginBottom: theme.spacing(2),
        fontFamily: 'usual'
    },
    paper: {
        maxHeight: '100%'
    },
    paperLg :{
        maxWidth: '1150px'
    }
}))

export {
    modalStyles
}