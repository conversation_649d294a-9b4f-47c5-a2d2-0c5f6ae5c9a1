import React from 'react'
import { Typography, List, ListItem } from '@mui/material'

interface IProps {
    headerItems: any[]
    activeIndex: number
    setActiveIndex: any
    classes?: any
}

export const Header: React.FC<IProps> = (props: IProps)=> {
    const {
        headerItems,
        activeIndex,
        setActiveIndex,
        classes
    } = props
    
    return (
        <>
            <Typography className={classes.title}>Compare Franchise Requests</Typography>
            <List className="tabs vni-flex">
                {
                    headerItems.map((item: any,i) =>{
                        return <ListItem 
                                    key={i} data-cy={"headerbar-item-" + i}
                                    onClick={()=>{setActiveIndex(item.index)}}
                                    className={`tab-link ${classes.itemLink} ${item.index === activeIndex && classes.activeItemLink}`}
                                    disableGutters>{item.label}</ListItem>
                    })
                }
            </List>
        </>
    )
}