import React, { useEffect } from 'react'
import { Grid, Select, MenuItem, Typography, Button, TextField, ListItem, List } from '@mui/material'
import { Header } from './Component/Header'
import { HeaderItems } from './const';
import { useStyles } from '../ViewFranchise/Layout/style';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../reducers/rootReducer';
import { BasicInformation } from '../ViewFranchise/Layout/Components/BasicInformation';
import { SalesChannel } from '../ViewFranchise/Layout/Components/SalesChannel';
import { Modal } from '../Modal';
import { updateFranchiseRequest, setModalContent, fetchFranchises } from '../../reducers/FranchiseSlice';
import {
  LocalizationProvider,
  DatePicker,
} from "@mui/x-date-pickers";
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { Franchise } from '../../models/Franchise';
import { useHistory, useLocation } from 'react-router-dom';
import { KycInformation } from '../ViewFranchise/Layout/Components/KycInformation';
import { SupportingDocuments } from '../ViewFranchise/Layout/Components/SupportingDocuments';
import moment from "moment";
import { startOfToday } from 'date-fns'

interface IProps {
  handleClickButton: () => any;
  selected_id:string;
  franchise: Franchise;
  userRole: string;
}

export const CompareFranchise: React.FC<IProps> = (props: any) => {
    const {selected_id,franchise,handleClickButton,userRole} = props
    const {duplicate} = franchise
    const dispatch = useDispatch();
    const location = useLocation();
    const onFranchisePage = location.pathname.includes("/franchising/franchise/");
    const history = useHistory();
    const classes = useStyles();
    const [activeIndex, setActiveIndex] = React.useState<number>(0);
    const [selectedFR, setSelectedFR] = React.useState<any>({
      leftFR:franchise,
      rightFR:duplicate.filter(({ _id }:Franchise) => _id === selected_id)[0],
    });
    const {leftFR,rightFR} = selectedFR
    const [allFR, setAllFranchise] = React.useState<any[]>([]);
    const [param, setParam] = React.useState<any>({
        reason: "",
        remarks: "",
        due_date: "",
        effectivity_date: "",
        expiry_date: "",
    });
    const [disapproveList, setDisapproveList] = React.useState<any[]>([]);
    const [paramError, setParamError] = React.useState<any>({
        reason: "",
        remarks: "",
        due_date: "",
        effectivity_date: "",
        expiry_date: "",
    });
    const [modalOpen, setModalOpen] = React.useState<boolean>(false)
    const [btnActionID, setBtnActionID] = React.useState<any>({id:"",action:""})
    const [modalObj, setModalObj] = React.useState<any>({
        title: "",
        description:"",
        button:<></>,
    });
    const {title,description,button} =  modalObj;
    const disapproveReasonList = useSelector((state: RootState) => state.maintenance.disapproveReasonList)
    const validationContent = useSelector((state: RootState) => state.franchise.validationModalContent);
    const roles = useSelector((state: RootState) => state.login.userRoles)
    const pattern = new RegExp(/\D/);

    const handleChangeFR = (e: any) => {
      const {name,value} = e.target
      setSelectedFR({...selectedFR,[name]:allFR.filter(({ _id }:Franchise) => _id === value)[0]})
    }

    const handleClose = (redirect?:boolean) => {
      setModalOpen(false)
      setParam({
        reason: "",
        remarks: "",
        due_date: "",
        effectivity_date: "",
        expiry_date: "",
      });
      setParamError({
        reason: "",
        remarks: "",
        due_date: "",
        effectivity_date: "",
        expiry_date: "",
      });
      if(redirect){
        handleClickButton()
        if(onFranchisePage){
          history.push("/franchising/");
        }else{
          dispatch(fetchFranchises());
        }
      }
    }

    const handleDefaultExpiry = (franchise: Franchise, expiry_date: any) => {
      switch (franchise.channel_of_request) {
        case "New Business Associate":
        case "New Business Agency":
          expiry_date.setMonth(expiry_date.getMonth() + 6);
          break;
        case "Business Development Officer":
          expiry_date.setMonth(expiry_date.getMonth() + 6);
          break;
        default:
          expiry_date.setDate(expiry_date.getDate() + 60);
          break;
      }
      return moment(expiry_date).format("MM/DD/YYYY");
    };

    const handleInputChange = (e: any) => {
      e.preventDefault();
      let { name, value } = e.target;
      setParamError({ ...paramError, [name]: "" });
      setParam({ ...param, [name]: value });
    }

    const handleDuplicateReason = (e: any,full_name:string) => {
      e.preventDefault();
      let { value } = e.target;
      let _id = e.target.name;
      let duplicateList = disapproveList.map((content:any) => {
        const {id,name,reason_for_disapprove,error} = content
        return {
          id,
          name:(_id === id ?full_name:name),
          reason_for_disapprove:(_id === id ?value:reason_for_disapprove),
          error:(_id === id ?"":error)
        }
      });
      setDisapproveList(duplicateList);
    }

    const handleEffectivityDate = (effectivity_date: any) => {
      let replacedDate = typeof effectivity_date === "string"?effectivity_date.replace(/\//g,"").replace(/_/g,""):"";
      if(!pattern.test(replacedDate) && replacedDate.length <= 8){
        let validExpiryDate = !param.expiry_date || (moment(param.expiry_date).isValid() && moment(param.expiry_date) >= moment(startOfToday()) && moment(param.expiry_date) >= moment(effectivity_date));
        let validDate =  moment(effectivity_date).isValid() && moment(effectivity_date) >= moment(startOfToday()) && (!param.expiry_date || !moment(param.expiry_date).isValid() || moment(param.expiry_date) >= moment(effectivity_date));
        setParam({ ...param, effectivity_date });
        setParamError({ ...paramError, effectivity_date: validDate || !effectivity_date?"":"Invalid Date Format",expiry_date:validExpiryDate?"":"Invalid Date Format" });
      }else{
        setParamError({ ...paramError, effectivity_date: "Invalid Date Format" });
      }
    };
  
    const handleExpiryDate = (expiry_date: any) => {
      let replacedDate = typeof expiry_date === "string"?expiry_date.replace(/\//g,"").replace(/_/g,""):"";
      if(!pattern.test(replacedDate) && replacedDate.length <= 8){
        let validEffectivityDate = !param.effectivity_date || (moment(param.effectivity_date).isValid() && moment(param.effectivity_date) >= moment(startOfToday()) && moment(expiry_date) >= moment(param.effectivity_date));
        let validDate =  moment(expiry_date).isValid() && moment(expiry_date) >= moment(startOfToday()) && (!param.effectivity_date || !moment(param.effectivity_date).isValid() || moment(expiry_date) >= moment(param.effectivity_date));
        setParam({ ...param, expiry_date });
        setParamError({ ...paramError, expiry_date: validDate || !expiry_date?"":"Invalid Date Format",effectivity_date:validEffectivityDate?"":"Invalid Date Format" });
      }else{
        setParamError({ ...paramError, expiry_date: "Invalid Date Format" });
      }
    };

    let remarksGrid = (
      <Grid className="vni-flex vni-flex-col vni-mt-3">
        <Typography>Remarks (Optional)</Typography>
        <TextField
          data-cy="remarks"
          className="CustomInput"
          variant="outlined"
          name="remarks"
          id="remarks"
          label=""
          multiline
          maxRows={3}
          rows={3}
          onChange={handleInputChange}
        />
      </Grid>
    );

    let reasonGrid = (
      <Grid className="vni-flex vni-mt-3">
        <Grid className="vni-w-full">
          <label htmlFor="">
            Reason <span className="vni-text-red-500">*</span>
          </label>
          <Select
            value={param.reason}
            className={
              "CustomSelect vni-w-full " + (paramError.reason ? "error" : "")
            }
            name="reason"
            onChange={handleInputChange}
            required={true}
            error={paramError.reason !== ""}
          >
            {
              disapproveReasonList.map((value: any, i) => (
                <MenuItem key={i} value={value.reason_for_disapprove}>
                  {value.reason_for_disapprove}
                </MenuItem>
              ))
            }
          </Select>
        </Grid>
      </Grid>
    );

    let disapproveContent = (
      <>
        <Typography className={classes.modalText}>
          Are you sure you want to disapprove this franchise request? <br/>Sales user
          will be notified of its status and reason for disapproval.
        </Typography>
        {reasonGrid}
        {remarksGrid}
      </>
    );

    let isRequestPlural = (btnActionID.id && leftFR && leftFR._id === btnActionID.id && leftFR.duplicate.length > 1) || (btnActionID.id && rightFR && rightFR._id === btnActionID.id && rightFR.duplicate.length > 1);
    
    let approveContent = (
      <>
        <Typography className={classes.modalText}>
          {
            ((btnActionID.id && leftFR && leftFR._id === btnActionID.id) && leftFR.is_refranchise && leftFR.is_refranchise.toLowerCase() === "no"  && "Are you sure you want to approve the request by")
            ||
            ((btnActionID.id && leftFR && leftFR._id === btnActionID.id) && leftFR.is_refranchise && leftFR.is_refranchise.toLowerCase() === "yes" && leftFR.client_id  && "Are you sure you want to approve the returning franchise request by")
            ||
            ((btnActionID.id && rightFR && rightFR._id === btnActionID.id) && rightFR.is_refranchise && rightFR.is_refranchise.toLowerCase() === "no"  && "Are you sure you want to approve the request by")
            ||
            ((btnActionID.id && rightFR && rightFR._id === btnActionID.id) && rightFR.is_refranchise && rightFR.is_refranchise.toLowerCase() === "yes" && rightFR.client_id  && "Are you sure you want to approve the returning franchise request by")
          }
          <strong>
          {
            ((btnActionID.id && leftFR && leftFR._id === btnActionID.id) && " "+leftFR.applicant_name)
            ||
            ((btnActionID.id && rightFR && rightFR._id === btnActionID.id) &&  " "+rightFR.applicant_name)
          }
          </strong> ?
          <br/>
          {
            ((btnActionID.id && leftFR && leftFR._id === btnActionID.id) && leftFR.is_refranchise && leftFR.is_refranchise.toLowerCase() === "no"  && "This will generate a client ID.")
            ||
            ((btnActionID.id && leftFR && leftFR._id === btnActionID.id) && leftFR.is_refranchise && leftFR.is_refranchise.toLowerCase() === "yes" && leftFR.client_id && `This will re-use its previous Client ID (${leftFR.client_id}).`)
            ||
            ((btnActionID.id && rightFR && rightFR._id === btnActionID.id) && rightFR.is_refranchise && rightFR.is_refranchise.toLowerCase() === "no"  && "This will generate a client ID.")
            ||
            ((btnActionID.id && rightFR && rightFR._id === btnActionID.id) && rightFR.is_refranchise && rightFR.is_refranchise.toLowerCase() === "yes" && rightFR.client_id && `This will re-use its previous Client ID (${rightFR.client_id}).`)
          }
          <br/>
          The following {isRequestPlural? "requests":"request"} will be automatically disapproved:
        </Typography>
        <Grid className="custom-scroll">
          {
            disapproveList.map((option:any, index:any) => (
              <Grid className="vni-flex vni-my-5" key={index}>
                <p className="vni-pl-4 vni-font-bold static-name-width">{option.name}</p>
                <Grid className="vni-w-3/5 vni-pl-4">
                  <label htmlFor="">
                    Reason for Disapproval <span className="vni-text-red-500">*</span>
                  </label>
                  <Select
                    value={option.reason_for_disapprove??""}
                    className={
                      "CustomSelect " + (option.error ? "error" : "")
                    }
                    name={option.id}
                    onChange={(e) => handleDuplicateReason(e,option.name)}
                    required={true}
                    error={option.error !== ""}
                  >
                    {
                      disapproveReasonList.map((value: any, i) => (
                        <MenuItem key={i} value={value.reason_for_disapprove}>
                          {value.reason_for_disapprove}
                        </MenuItem>
                      ))
                    }
                  </Select>
                </Grid>
              </Grid>
            ))
          }
        </Grid>
        <Grid container spacing={3} className="vni-flex vni-mt-3 vni-justify-between">
          <Grid item xs={6}>
            <Grid className="vni-datepicker">
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label={
                    <label htmlFor="">
                      Franchise Effectivity Date{" "}
                      <span className="vni-text-red-500">*</span>
                    </label>
                  }
                  format="MM/dd/yyyy"
                  maxDate={param.expiry_date?new Date(param.expiry_date):undefined}
                  minDate={new Date()}
                  value={
                    param.effectivity_date
                      ? new Date(param.effectivity_date)
                      : null
                  }
                  onChange={handleEffectivityDate}
                  slotProps={{
                    textField: {
                      id: "effectivity-date",
                      name: "effectivity_date",
                      placeholder: "mm/dd/yyyy",
                      error: paramError.effectivity_date !== "",
                      className: "CustomInput",
                      inputProps: {
                        'data-cy': "effectivity_date"
                      }
                    }
                  }}
                />
              </LocalizationProvider>
            </Grid>
          </Grid>
          <Grid item xs={6}>
            <Grid className="vni-datepicker">
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label={
                    <label htmlFor="">
                      Franchise Expiry Date <span className="vni-text-red-500">*</span>
                    </label>
                  }
                  format="MM/dd/yyyy"
                  minDate={param.effectivity_date?new Date(param.effectivity_date):new Date()}
                  value={
                    param.expiry_date ? new Date(param.expiry_date) : null
                  }
                  onChange={handleExpiryDate}
                  slotProps={{
                    textField: {
                      id: "expiry-date",
                      name: "expiry_date",
                      placeholder: "mm/dd/yyyy",
                      error: paramError.expiry_date !== "",
                      className: "CustomInput",
                      inputProps: {
                        'data-cy': "expiry_date"
                      }
                    }
                  }}
                />
              </LocalizationProvider>
            </Grid>
          </Grid>
        </Grid>
        {remarksGrid}
      </>
    );
    
    const handleConfirmClick = (_id:string,action:string) =>{
      const { remarks } = param;
      switch (action) {
        case "approve":
          const { effectivity_date, expiry_date } = param;
          const validReason = !(disapproveList.filter(({ reason_for_disapprove }) => !reason_for_disapprove ).length > 0);
          const validEffectivityDate = effectivity_date && moment(effectivity_date).isValid() && moment(effectivity_date) >= moment(startOfToday()) && moment(expiry_date) >= moment(effectivity_date);
          const validExpiryDate = expiry_date && moment(expiry_date).isValid() && moment(expiry_date) >= moment(startOfToday()) && moment(expiry_date) >= moment(effectivity_date);
          if(validEffectivityDate && validExpiryDate && validReason){
            let disapprove = disapproveList.map((content:any) => {
              const {id,reason_for_disapprove} = content
              return {
                id,
                reason_for_disapprove,
              }
            });
            dispatch(updateFranchiseRequest(_id, action, userRole, { disapprove,effectivity_date, expiry_date, remarks },_id));
          }else{
            setParamError({
              ...paramError,
              effectivity_date: validEffectivityDate ? "" : "This field is required.",
              expiry_date: validExpiryDate ? "" : "This field is required.",
            });
            const disapprove = disapproveList.map((content:any) => {
              const {id,name,reason_for_disapprove} = content
              return {
                id,
                name,
                reason_for_disapprove,
                error:reason_for_disapprove? "":"This field is required."
              }
            });
            setDisapproveList(disapprove)
            return;
          }
          break;
        case "disapprove":
          const { reason } = param;
          if (reason) {
            dispatch(updateFranchiseRequest(_id, action, userRole, { reason,remarks }));
          } else {
            setParamError({
              ...paramError,
              reason: "This field is required.",
            });
            return;
          }
          break;
        default:
          break;
      }
    }

    const handleOpen = (action:string,id:string) => {
      const effectivity_date: any = moment(new Date()).format("MM/DD/YYYY");
      const selected_franchise: Franchise = id && rightFR && rightFR._id === id ? rightFR : leftFR
      const expiry_date: any = handleDefaultExpiry(selected_franchise, new Date())
      setParam((state: any) => ({ ...state, effectivity_date, expiry_date }))
      setModalOpen(true)
      setDisapproveList([]);
      setBtnActionID({id,action})
      dispatch(setModalContent({
        title: "",
        p: "",
        error: false,
      }))
    }

    useEffect(() => {
      if(duplicate && duplicate.length > 0){
        let allFRContents: any = [];
        allFRContents = duplicate.map((content: Franchise) => {
          return content;
        });
        allFRContents.push(franchise);
        setAllFranchise(allFRContents);
      }
    }, [duplicate,franchise]);

    useEffect(() => {
      if (validationContent.title && validationContent.p && !validationContent.error) {
        if("Request Approved" === validationContent.title){
          setBtnActionID({
            id:validationContent.p,
            action:"approve"
          })
        }else if("Request Disapproved" === validationContent.title){
          setBtnActionID({
            id:validationContent.p,
            action:"disapprove"
          })
        }
        setModalObj({
            title: validationContent.title,
            description: validationContent.p,
            button: <Button className={classes.leftButtonOutline} data-cy={`successincompare-${btnActionID.action}-backtodashboard-btn`} onClick={() => handleClose(true)}>Back to Dashboard</Button>
        });
        setModalOpen(true);
      }else if (validationContent.title && validationContent.p && validationContent.error){
        setModalObj({
            title: validationContent.title,
            description: validationContent.p,
            button: <Button className="CustomPrimaryButton scarlet" data-cy={`errorincompare-${btnActionID.action}-okay-btn`} onClick={() => handleClose()}>Okay</Button>
        });
        setModalOpen(true);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [validationContent]);

    const validationResponse = validationContent.title === "" && validationContent.p === "";

    useEffect(() => {
      const {id,action} = btnActionID
      if(modalOpen && id && action && validationResponse){
        let header = "";
        let paragraph = "";
        let btnCancelText = "No";
        let btnConfirmText = "Yes";
        let btnConfirmClass = classes.rightButton;
        switch (action) {
            case "approve":
                header = "Approve Request";
                if(disapproveList.length < 1){
                  let duplicateList = allFR.filter(({ _id }:Franchise) => _id !== id).map((content:Franchise) => {
                    const {_id,applicant_name} = content
                    return {
                      id:_id,
                      name:applicant_name,
                      reason_for_disapprove:"",
                      error:""
                    }
                  });
                  setDisapproveList(duplicateList)
                }
                break;
            case "disapprove":
                header = "Disapprove Request";
                btnCancelText = "Cancel";
                btnConfirmText = "Disapprove";
                btnConfirmClass = classes.leftButton;
                break;
            default:
                break;
        }
        setModalObj({
          title:header,
          description:paragraph,
          button: <>
                    <Button data-cy={`${action}-no-btn`} style={{marginTop: 20}} className={classes.leftButtonOutline}  onClick={() => handleClose()}>{btnCancelText}</Button>
                    <Button data-cy={`${action}-yes-btn`} style={{marginTop: 20}} className={btnConfirmClass} onClick={() => handleConfirmClick(id,action)}>{btnConfirmText}</Button>
                  </>
        })
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [modalOpen,disapproveList,param,validationResponse]);

    const {action} = btnActionID
    const requestorArray = allFR.filter(({ _id }:Franchise) => _id === description);
    const requestorName = requestorArray.length === 1?requestorArray[0].applicant_name:"";
    const successResponse = validationContent.title !== "" && validationContent.p !== "" && !validationContent.error;
    const errorResponse = validationContent.title !== "" && validationContent.p !== "" && validationContent.error;
    
    return (
        <>
            <Grid container>
                <Grid item xs={7}>
                  <Header 
                      setActiveIndex={setActiveIndex}
                      activeIndex={activeIndex}
                      headerItems={HeaderItems}
                      classes={classes} 
                  />
                </Grid>
            </Grid>
            <Grid className="vni-flex vni-mt-5">
                <Grid container className="compare-requests-container">
                    <Grid item xs={6} className="requests-details vni-pr-8" style={{position: 'relative'}}>
                        <Grid className="vni-inline-block vni-max-w-32">
                            <Select
                                id="leftFR"
                                className="CustomSelect minimal"
                                style={{minWidth: 250, textAlign: 'left', fontWeight: 'bold'}}
                                value={leftFR?leftFR._id:""}
                                name="leftFR"
                                onChange={handleChangeFR}
                                fullWidth={true}
                                data-cy={`leftFR-${leftFR?leftFR._id:"0"}`}
                            >
                              {
                                allFR.filter(({ _id }:Franchise) => _id !== (rightFR?rightFR._id:"")).map((option:any, index:any) => (
                                  <MenuItem key={index} value={option._id} data-cy={`leftFR-option-${option._id}`}>
                                    {option.applicant_name}
                                  </MenuItem>
                                ))
                              }
                            </Select>
                        </Grid>
         
                        <Grid className="vni-justify-end vni-mt-5" style={{minHeight:400}}>
                          {
                            (activeIndex === 0 && <BasicInformation isCompare classes={classes} franchise={leftFR} />) ||
                            (activeIndex === 1 && <SalesChannel franchise={leftFR} classes={classes} />) ||
                            (activeIndex === 2 && <KycInformation franchise={leftFR}  classes={classes} compare={true} />) ||
                            (activeIndex === 3 && <SupportingDocuments deletedFile={[]} roles={roles} franchise={leftFR} classes={classes} fullRow={true} userRole={userRole} />)
                          }
                        </Grid>
                        <Grid className={classes.buttonGroup} justifyContent='flex-end' container>
                            <Grid item xs={12} style={{ textAlign: 'right', position: 'absolute', bottom: -50, marginBottom: 20 }}>
                                <Button data-cy="compareleft-disapprove-btn" className={classes.leftButton} onClick={() => handleOpen("disapprove",leftFR?leftFR._id:"")}>Disapprove</Button>
                                <Button data-cy="compareleft-approve-btn" className={classes.rightButton} onClick={() => handleOpen("approve",leftFR?leftFR._id:"")}>Approve</Button>
                            </Grid>
                        </Grid>
                    </Grid> 
                    <Grid item xs={6} className="requests-details vni-pl-8" style={{position: 'relative'}}>
                        <Grid className="vni-inline-block vni-max-w-32">
                            <Select
                                id="rightFR"
                                className="CustomSelect minimal"
                                style={{minWidth: 250, textAlign: 'left', fontWeight: 'bold'}}
                                value={rightFR?rightFR._id:""}
                                name="rightFR"
                                onChange={handleChangeFR}
                                fullWidth={true}
                                data-cy={`rightFR-${rightFR?rightFR._id:"0"}`}
                            >
                              {
                                allFR.filter(({ _id }:Franchise) => _id !== (leftFR?leftFR._id:"")).map((option:any, index:any) => (
                                  <MenuItem key={index} value={option._id} data-cy={`rightFR-option-${option._id}`}>
                                    {option.applicant_name}
                                  </MenuItem>
                                ))
                              }
                            </Select>
                        </Grid>

                        <Grid className="vni-justify-end vni-mt-5" style={{minHeight:400}}>
                          {
                            (activeIndex === 0 && <BasicInformation isCompare classes={classes} franchise={rightFR} />) ||
                            (activeIndex === 1 && <SalesChannel franchise={rightFR} classes={classes} />) ||
                            (activeIndex === 2 && <KycInformation franchise={rightFR}  classes={classes} compare={true} />) ||
                            (activeIndex === 3 && <SupportingDocuments deletedFile={[]} roles={roles} franchise={rightFR} classes={classes} fullRow={true} userRole={userRole} />)
                          }
                        </Grid>
                        <Grid className={classes.buttonGroup} justifyContent='flex-end' container>
                            <Grid item xs={12} style={{ textAlign: 'right', position: 'absolute', bottom: -50, marginBottom: 20 }}>
                                <Button data-cy="compareright-disapprove-btn" className={classes.leftButton} onClick={() => handleOpen("disapprove",rightFR?rightFR._id:"")}>Disapprove</Button>
                                <Button data-cy="compareright-approve-btn" className={classes.rightButton} onClick={() => handleOpen("approve",rightFR?rightFR._id:"")}>Approve</Button>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </Grid>
            <Modal 
              fullWidth={false}
              maxWidth='md'
              open={modalOpen}
              onClose={handleClose}
            >
                <Grid>
                    {title && 
                      <Typography className={classes.modalTitle} style={{marginBottom: 20}}>{title}</Typography>
                    }
                    {
                      (action === "approve" && successResponse && <Typography style={{maxWidth: 500}} className={classes.modalText}>Request by <strong style={{color:"#3ab77d"}}>{requestorName}</strong> has been approved. It has been successfully sent to Underwriting.<br/>The following are automatically disapproved:</Typography>) ||
                      (description && <Typography style={{maxWidth: 500}} className={classes.modalText}>{description}</Typography>)
                    }
                    {
                      ((errorResponse || (action === "disapprove" && successResponse)) && <></>) || 
                      (action === "approve" && successResponse &&  <List>
                                                                    {
                                                                      allFR.filter(({ _id }:Franchise) => _id !== description).map((option:Franchise, index:any) => (
                                                                        <ListItem key={index}>
                                                                          <strong style={{color:"#ef5350"}}>{option.applicant_name}</strong>
                                                                        </ListItem>
                                                                      ))
                                                                    }
                                                                  </List>) ||
                      (action === "approve" && approveContent) ||
                      (action === "disapprove" && disapproveContent)
                    }
                    <Grid className="btn-group" style={{ textAlign: 'center', marginTop: 20 }}>
                        {button}
                    </Grid>
                </Grid>
            </Modal>
        </>
    )
}