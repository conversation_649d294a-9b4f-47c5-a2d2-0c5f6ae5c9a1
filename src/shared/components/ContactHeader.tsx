import React from 'react';
import { NavLink, useLocation } from "react-router-dom";
import { useSelector } from 'react-redux';
import { RootState } from "../../shared/reducers/rootReducer"

const ContactHeader: React.FC = () => {
    let location = useLocation();
    let dashboard = `/franchising/contact/`;
    const isEditPage = useSelector((state: RootState) => state.contact.isEdit);
    const isCreatePage = location.pathname === "/franchising/contact/create";

    return (
        <div className="create-franchise-request-head">
            {(isEditPage || isCreatePage) &&
                <>
                    <div>
                        <span className="breadcrumbs">
                            <NavLink 
                            data-cy="dashboard"
                            to={dashboard}
                            >
                                AGENTS/BROKERS MAINTENANCE
                            </NavLink>/ 
                            <strong>{isEditPage ? `EDIT CONTACT` : `ADD CONTACT`}</strong>
                        </span>
                        <h1 className="vni-font-bold vni-text-2xl vni-uppercase">{isEditPage ? `EDIT CONTACT` : `ADD CONTACT`}</h1>
                    </div>
                </>
            }
        </div>
    );
}; 

export default ContactHeader;