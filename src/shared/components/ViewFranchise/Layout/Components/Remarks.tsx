import React from 'react'

// Material Ui imports
import {
    Grid,
} from '@mui/material'
import { Franchise } from '../../../../models/Franchise'
import moment from 'moment';

// Redux
// import { useSelector } from 'react-redux'
// import { RootState } from '../../../../reducers/rootReducer'
import { remarksForViewFranchiseModal } from '../../../../../utils/DataHelper';
import { decryptCredentials } from '../../../../../utils/LoginService';

interface IProps {
    classes?: any,
    franchise: Franchise,
    userRole: string
}

export const Remarks: React.FC<IProps> = (props: IProps)=> {
    const {
        classes,
        franchise,
        userRole
    } = props

    // const roles = useSelector((state: RootState) => state.login.userRoles)
    // const {
    //     isSalesUser 
    // } = roles

    const {
        remarks,
        reasons,
        // status
    } = franchise

    let adjustedRemarks = remarksForViewFranchiseModal(reasons??[], remarks??[], userRole)

    adjustedRemarks = adjustedRemarks?.sort((a: any, b: any)=>{
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    })

    let remarkContents: any
    let formatDate: any
    let userFullName: any

    const decryptedFullName = (string: any) => {
        const splitString = string.split(" ");
        let combined: any = [];

        splitString.map((substring: any) => {
            const decrypted = decryptCredentials(substring);
            return combined.push(decrypted);
        });
        return combined.join(" ");
    }
    

    if (adjustedRemarks && adjustedRemarks.length !== 0){
        remarkContents =  adjustedRemarks.map((content: any, index: number)=>{
            formatDate = new Date(content.created_at);
            // decryptedFullName = decryptCredentials(content.full_name);
            console.log('remarks decrypted name', content.full_name);
            userFullName = decryptedFullName(content.full_name);
            return (
                        
                <Grid key={index} container spacing={4}>
                    <Grid item xs={4}>
                        <div className={classes.value}>
                            {/* {content.full_name}  */}
                            {userFullName}
                        </div>
                    </Grid>
                    <Grid item xs={5}>
                        <div className={classes.value} style={{overflow: 'hidden', textOverflow: 'ellipsis'}}>
                            {content.message} 
                        </div>
                    </Grid>
                    <Grid item xs={3}>
                        <div className={classes.value}>
                            {moment(formatDate).format("MMMM D, YYYY")}
                        </div>
                    </Grid>
                </Grid>
                    
            ) 
        })
    }else{
        remarkContents = (
            <Grid container spacing={4}>
                <Grid item xs={4}>
                    <div className={classes.value}>
                        NA
                    </div>
                </Grid>
                <Grid item xs={5}>
                    <div className={classes.value}>
                        NA
                    </div>
                </Grid>
                <Grid item xs={3}>
                    <div className={classes.value}>
                        NA
                    </div>
                </Grid>
            </Grid>
        ) 
    }
    return (
        <>{remarkContents}</>
    )
}