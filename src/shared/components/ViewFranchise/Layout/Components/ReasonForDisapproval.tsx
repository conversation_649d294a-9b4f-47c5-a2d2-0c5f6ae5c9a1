import React from 'react'

// Material Ui imports
import {
    Grid,
    Typography
} from '@mui/material'
import { Franchise, Reason } from '../../../../models/Franchise'
import { getFranchisingRole } from '../../../../../utils/DataHelper'

interface IProps {
    classes?: any,
    franchise: Franchise,
}

export const ReasonForDisapproval: React.FC<IProps> = (props: IProps)=> {
    const {
        classes,
        franchise
    } = props

    const {
        reasons,
        status
    } = franchise

    let reason: any = []
    if (reasons){
        reason = reasons.filter((content: Reason,i: number) => {
            let valid = false; 
            let actualRole = getFranchisingRole(content.roles)
            switch (status) {
                case "DISAPPROVED":
                    valid = (content.main_role === "FRF_FINAL_APPROVER" || content.main_role === "FRF_INITIAL_APPROVER" || actualRole === "FRF_FINAL_APPROVER" || actualRole === "FRF_INITIAL_APPROVER") && content.type === "DISAPPROVE";
                    break;
                default:
                    break;
            }
            return valid;
        }).map((content: Reason) => { 
            let {reason} = content
            return  {
              reason:reason??"--",
            }
        })
    }

    let actualReason = ""
    if(reason.length !== 0){
        actualReason = reason[reason.length - 1].reason;
	}else{
        actualReason = "--"
    }
    
    return (
        <>
            <Grid container spacing={4}>
                <Grid item xs={12}>
                    <div className={classes.value}>
                        <Typography className={classes.value}>{actualReason}</Typography>
                    </div>
                </Grid>
            </Grid>

        </>
    )
}