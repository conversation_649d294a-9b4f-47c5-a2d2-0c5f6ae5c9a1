import React from "react";

// Material Ui imports
import { Grid, <PERSON>po<PERSON>, Button, List, ListItem } from "@mui/material";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTrash } from "@fortawesome/free-solid-svg-icons";
import { Modal } from "../../../Modal";
import { Franchise } from "../../../../models/Franchise";
import { useDispatch } from "react-redux";
import {
  deleteFranchiseFileUpload,
  downloadFranchiseFileUpload,
} from "../../../../reducers/FranchiseSlice";
import { IUserRole } from "../../../../models/User";
import { NavLink } from "react-router-dom";
import { getFranchisingRole } from "../../../../../utils/DataHelper";

interface IProps {
  classes?: any;
  franchise: Franchise;
  roles: IUserRole;
  deletedFile: any[];
  fullRow?: boolean;
  userRole: string;
}

export const SupportingDocuments: React.FC<IProps> = (
  props: IProps
)=> {
  const { classes, franchise, roles, deletedFile, fullRow, userRole } = props;

  const {
    isValidated,
    isReturnedToValidator,
    isApprovalInProcess,
    isFinalApprovalInProcess,
  } = franchise;
  const { isSpecialist, isSupervisor, isManager } = roles;

  const dispatch = useDispatch();

  const [innerModalProps, setInnerModalProps] = React.useState<any>({
    open: false,
    standard: {},
  });

  const handleClose = () => {
    setInnerModalProps({
      ...innerModalProps,
      open: false,
    });
  };

  const handleOpen = (_id: string) => {
    setInnerModalProps({
      ...innerModalProps,
      open: true,
      standard: {
        title: "Delete Document",
        message: "Are you sure you want to delete this document?",
        conditionalButtons: {
          buttonleft: (
            <Button
              data-cy="delete-no-btn"
              className={classes.modalButtonLeft}
              variant="outlined"
              onClick={handleClose}
            >
              {" "}
              No{" "}
            </Button>
          ),
          buttonRight: (
            <Button
              data-cy="delete-yes-btn"
              className={classes.modalButtonRight}
              onClick={() => handleDelete(_id)}
            >
              {" "}
              Yes{" "}
            </Button>
          ),
        },
      },
    });
  };

  const handleDelete = (_id: string) => {
    dispatch(deleteFranchiseFileUpload(_id));
    setInnerModalProps({
      ...innerModalProps,
      open: false,
    });
  };

  const handleDownload = (e: any, _id: string, filename: string) => {
    e.preventDefault();
    dispatch(downloadFranchiseFileUpload(_id, filename));
  };

  const supporting_docs =
    franchise.supporting_docs_obj ?? franchise.supporting_docs;
  const files = supporting_docs.filter(
    (file) => !file.purpose || file.purpose === "supporting document"
  );
  const [appointment_letter] = franchise.appointment_letter;

  return (
    <>
      <Modal
        fullWidth={false}
        maxWidth="md"
        open={innerModalProps.open}
        onClose={handleClose}
        standard={innerModalProps.standard}
      />
      <Grid container spacing={4}>
        <Grid item xs={fullRow ? 12 : 6}>
          {fullRow && (
            <Typography className={classes.tabTitle}>
              SUPPORTING DOCUMENTS (SALES USER)
            </Typography>
          )}
          <div
            className={
              fullRow
                ? "min-h-192 custom-scroll " + classes.documents
                : classes.documents
            }
          >
            <List>
              {files.map(
                (value, i) =>
                  value.main_role && value.from && value.from === "encoder" && (
                    <ListItem key={i}>
                      <Typography className={classes.document}>
                        <NavLink
                          data-cy="handle-download"
                          onClick={(e) =>
                            handleDownload(e, value._id, value.file_name)
                          }
                          to={value.path}
                        >
                          {value.file_name}
                        </NavLink>
                      </Typography>
                    </ListItem>
                  )
              )}
            </List>
          </div>
          {appointment_letter && (
            <>
              <Typography className={classes.tabAppointmentLetterTitle}>
                APPOINTMENT LETTER
              </Typography>

              <Typography className={classes.tabSubtitle}>
                {appointment_letter?.agent.toUpperCase()}
              </Typography>
              <Typography className={classes.tabSubtitle}>
                START DATE: {appointment_letter?.validFrom.toUpperCase()}
              </Typography>
              <Typography className={classes.tabSubtitle}>
                EXPIRY DATE: {appointment_letter?.validTo.toUpperCase()}
              </Typography>
              <div
                className={
                  fullRow
                    ? "min-h-192 custom-scroll " + classes.documents
                    : classes.documents
                }
              >
                <List>
                  {appointment_letter &&
                    appointment_letter.main_role &&
                    (appointment_letter.main_role === "FRF_ENCODER" ||
                      getFranchisingRole(appointment_letter.roles) ===
                        "FRF_ENCODER") && (
                      <ListItem>
                        <Typography className={classes.document}>
                          <NavLink
                            data-cy="handle-download"
                            onClick={(e) =>
                              handleDownload(
                                e,
                                appointment_letter._id,
                                appointment_letter.file_name
                              )
                            }
                            to={appointment_letter.path}
                          >
                            {appointment_letter.file_name}
                          </NavLink>
                        </Typography>
                      </ListItem>
                    )}
                </List>
              </div>
            </>
          )}
        </Grid>
        <Grid item xs={fullRow ? 12 : 6}>
          {fullRow && (
            <Typography className={classes.tabTitle}>
              SUPPORTING DOCUMENTS (SPECIALIST)
            </Typography>
          )}
          <div
            className={
              fullRow
                ? "min-h-192 custom-scroll " + classes.documents
                : classes.documents
            }
          >
            <List>
              {files.map(
                (value, i) =>
                  deletedFile.indexOf(value._id) < 0 &&
                  value.main_role && value.from && value.from === "validator" &&
                  userRole !== "encoder" && (
                    <ListItem key={i}>
                      <Typography className={classes.document}>
                        <NavLink
                          data-cy="handle-download"
                          onClick={(e) =>
                            handleDownload(e, value._id, value.file_name)
                          }
                          to={value.path}
                        >
                          {value.file_name}
                        </NavLink>
                      </Typography>
                      {(isValidated ||
                        isReturnedToValidator ||
                        isApprovalInProcess) &&
                      (isSpecialist && userRole === "validator") ? (
                        <FontAwesomeIcon
                          data-cy="delete-btn"
                          onClick={() => handleOpen(value._id)}
                          className={classes.uploadIcon}
                          icon={faTrash}
                        />
                      ) : (
                        <></>
                      )}
                    </ListItem>
                  )
              )}
            </List>
          </div>
        </Grid>
        <Grid item xs={fullRow ? 12 : 6}>
          <Typography className={classes.tabTitle}>
            SUPPORTING DOCUMENTS (SUPERVISOR)
          </Typography>
          <div
            className={
              fullRow
                ? "min-h-192 custom-scroll " + classes.documents
                : classes.documents
            }
          >
            <List>
              {files.map(
                (value, i) =>
                  deletedFile.indexOf(value._id) < 0 &&
                  value.main_role && value.from && value.from === "supervisor" &&
                    userRole !== "encoder" && (
                    <ListItem key={i}>
                      <Typography className={classes.document}>
                        <NavLink
                          data-cy="handle-download"
                          onClick={(e) =>
                            handleDownload(e, value._id, value.file_name)
                          }
                          to={value.path}
                        >
                          {value.file_name}
                        </NavLink>
                      </Typography>
                      {(isValidated ||
                        isReturnedToValidator ||
                        isApprovalInProcess) &&
                      (isSupervisor && userRole === "supervisor") ? (
                        <FontAwesomeIcon
                          data-cy="delete-btn"
                          onClick={() => handleOpen(value._id)}
                          className={classes.uploadIcon}
                          icon={faTrash}
                        />
                      ) : (
                        <></>
                      )}
                    </ListItem>
                  )
              )}
            </List>
          </div>
        </Grid>
        <Grid item xs={fullRow ? 12 : 6}>
          <Typography className={classes.tabTitle}>
            SUPPORTING DOCUMENTS (MANAGER)
          </Typography>
          <div
            className={
              fullRow
                ? "min-h-192 custom-scroll " + classes.documents
                : classes.documents
            }
          >
            <List>
              {files.map(
                (value, i) =>
                  deletedFile.indexOf(value._id) < 0 &&
                  value.main_role && value.from && value.from === "manager" &&
                  userRole !== "encoder" && (
                    <ListItem key={i}>
                      <Typography className={classes.document}>
                        <NavLink
                          data-cy="handle-download"
                          onClick={(e) =>
                            handleDownload(e, value._id, value.file_name)
                          }
                          to={value.path}
                        >
                          {value.file_name}
                        </NavLink>
                      </Typography>
                      {(isValidated ||
                        isReturnedToValidator ||
                        isApprovalInProcess ||
                        isFinalApprovalInProcess) &&
                      (isManager && userRole === "manager") ? (
                        <FontAwesomeIcon
                          data-cy="delete-btn"
                          onClick={() => handleOpen(value._id)}
                          className={classes.uploadIcon}
                          icon={faTrash}
                        />
                      ) : (
                        <></>
                      )}
                    </ListItem>
                  )
              )}
            </List>
          </div>
        </Grid>
      </Grid>
    </>
  );
};
