import React from 'react'

// Material Ui imports
import {
    Typography, Grid
} from '@mui/material'

// Relative imports
import { BasicInformation } from './BasicInformation'
import { SalesChannel } from './SalesChannel'
import { KycInformation } from './KycInformation'
import { SupportingDocuments } from './SupportingDocuments'
import { Remarks } from './Remarks'
import { ValidationResult } from './ValidationResult'
import { ReasonForReturn } from '../../Layout/Components/ReasonForReturn'
import { ReasonForDisapproval } from './ReasonForDisapproval'
import { SideItems } from '../ViewFranchise.const'
import { Franchise } from '../../../../models/Franchise'
import { IUserRole } from '../../../../models/User'

 
interface IProps {
    activeIndex: number,
    classes?: any,
    franchise: Franchise,
    roles: IUserRole,
    deletedFile:any[],
    userRole: string
}

export const Main: React.FC<IProps> = (props: IProps)=> {
    const {
       activeIndex,
       classes,
       franchise,
       roles,
       deletedFile ,
       userRole
    } = props
    // const {
    //     isSalesUser
    //  } = roles
    console.log("Main", props);
    return (
        <>
            {
                SideItems[activeIndex]['title'] && (activeIndex !== 3 || userRole === 'encoder') && (activeIndex !== 4) && (activeIndex !== 5) && <Typography className={classes.tabTitle}>{SideItems[activeIndex].title}</Typography>
            }
            {
                SideItems[activeIndex]['title'] 
                    && (activeIndex === 4)
                        &&  <Grid container spacing={4}>
                                <Grid item xs={4}>
                                    <Typography className={classes.tabTitle}>User</Typography>
                                </Grid>
                                <Grid item xs={5}>
                                    <Typography className={classes.tabTitle}>{SideItems[activeIndex].title}</Typography>
                                </Grid>
                                <Grid item xs={3}>
                                    <Typography className={classes.tabTitle}>Date</Typography>
                                </Grid>
                            </Grid>
            }

            {
                SideItems[activeIndex]['title'] 
                    && (activeIndex === 5)
                        &&  <Grid container spacing={4}>
                                 <Grid item xs={6}>
                                    <Typography className={classes.tabTitle}>{SideItems[activeIndex].title}</Typography>
                                </Grid>
                                <Grid item xs={6}>
                                    <Typography className={classes.tabTitle}>Date</Typography>
                                </Grid>
                            </Grid>
            }

            {
                SideItems[activeIndex]['title'] 
                    && (activeIndex === 3 && userRole !== 'encoder') 
                        &&  <Grid container spacing={4}>
                                <Grid item xs={6}>
                                    <Typography className={classes.tabTitle}>{SideItems[activeIndex].title + " (Sales User)"}</Typography>
                                </Grid>
                                <Grid item xs={6}>
                                    <Typography className={classes.tabTitle}>{SideItems[activeIndex].title + " (Specialist)"}</Typography>
                                </Grid>
                            </Grid>
            }
            { 
                (activeIndex === 0 && <BasicInformation classes={classes} franchise={franchise} />) ||
                (activeIndex === 1 && <SalesChannel franchise={franchise} classes={classes} />) || 
                (activeIndex === 2 && <KycInformation franchise={franchise}  classes={classes} />) || 
                (activeIndex === 3 && <SupportingDocuments deletedFile={deletedFile} roles={roles} franchise={franchise} classes={classes} userRole={userRole} />) || 
                (activeIndex === 4 && <Remarks classes={classes} franchise={franchise} userRole={userRole} />) ||
                (activeIndex === 5 && <ReasonForReturn classes={classes} franchise={franchise} userRole={userRole} />) ||
                (activeIndex === 6 && <ReasonForDisapproval classes={classes} franchise={franchise} />) ||
                (activeIndex === 7 && <ValidationResult classes={classes} franchise={franchise} />) 
            }
        </>
    )
}