import React from 'react'

// Material Ui imports
import {
    Grid,
    Typography
} from '@mui/material'
import { Franchise } from '../../../../models/Franchise'

interface IProps {
    classes?: any,
    franchise: Franchise
}

export const SalesChannel: React.FC<IProps> = (props: IProps)=> {
    const {
        classes,
        franchise
    } = props

    return (
        <>
            <Grid container spacing={4}>
                <Grid item xs={4}>
                    <Typography className={classes.label}>franchisee name</Typography>
                    <Typography className={classes.value}>{franchise.applicant_name !== "" ? franchise.applicant_name: "N.A"}</Typography>
                </Grid>
                <Grid item xs={8}>
                    <Typography className={classes.label}>contact person (If Broker)</Typography>
                    <Typography className={classes.value}>{franchise.applicant_contact_person !== "" ? franchise.applicant_contact_person: "N.A"}</Typography>
                </Grid>

                {/*  */}
                <Grid item xs={4}>
                    <Typography className={classes.label}>business address</Typography>
                    <Typography className={classes.value}>{franchise.applicant_business_address ? franchise.applicant_business_address: "N.A"}</Typography>
                </Grid>
                <Grid item xs={8}>
                    <Typography className={classes.label}>contact number</Typography>
                    <Typography className={classes.value}>{franchise.applicant_contact_number !== "" ? franchise.applicant_contact_number: "N.A"}</Typography>
                </Grid>

                {/*  */}
                <Grid item xs={12}>
                    <Typography className={classes.label}>email address</Typography>
                    <Typography className={classes.value}>{franchise.applicant_email_address !== "" ? franchise.applicant_email_address: "N.A"}</Typography>
                </Grid>
            </Grid>
        </>
    )
}