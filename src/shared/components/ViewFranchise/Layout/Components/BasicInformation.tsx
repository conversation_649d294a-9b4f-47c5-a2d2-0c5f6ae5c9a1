import React, {useState} from 'react'

// Material Ui imports
import {
    Grid,
    Typography,
    Divider,
    Tooltip
} from '@mui/material'
import { Franchise } from '../../../../models/Franchise'
// import { convertAddressRegionName } from '../../../../../utils/StringHelper'
import moment from 'moment'

interface IProps {
    classes?: any,
    franchise: Franchise,
    isCompare?: boolean,
}

export const BasicInformation: React.FC<IProps> = (props: IProps)=> {
    const {
        classes,
        franchise,
        // isCompare
    } = props

    const {
        authorized_contact_persons,
        client_id,
        channel_of_request,
        is_refranchise,
        old_account_name,
        corporate_name,
        brand_name,
        company_affiliates,
        tax_number,
        company_number,
        business_address,
        industry_class_id,
        date_time_received,
    } = franchise

    const [authContactFlag, setAuthContactFlag] = useState(true)
    const [designatedContactFlag, setDesignatedContactFlag] = useState(true)
    const authorizedContacts = authorized_contact_persons??[];
    const moreCount = authorizedContacts.length - 2
    const toggleMore = (e: { preventDefault: () => void; }) =>{
        e.preventDefault()
        setAuthContactFlag(!authContactFlag)
    }
    const toggleMoreDesignated = (e: { preventDefault: () => void; }) =>{
        e.preventDefault()
        setDesignatedContactFlag(!designatedContactFlag)
    }
    
    return (
        <>
            <Grid container>
                <Grid item xs={12}>
                    <Typography className={classes.label}>client id</Typography>
                    <Typography className={classes.value}>{client_id || "-"}</Typography>
                </Grid>
            </Grid>
            <Divider className={classes.divider} />
            <Grid container spacing={4}>
                <Grid item xs={4}>
                    <Typography className={classes.label}>channel of request</Typography>
                    <Typography className={classes.value}>{channel_of_request}</Typography>
                </Grid>
                <Grid item xs={8}>
                    <Typography className={classes.label}>date and time received</Typography>
                    <Typography className={classes.value}>{date_time_received ? moment(date_time_received).format('MM/DD/YYYY hh:mm A') : '-'}</Typography>
                </Grid>
                <Grid item xs={4}>
                    <Typography className={classes.label}>returning franchise</Typography>
                    <Typography className={classes.value}>{is_refranchise}</Typography>
                </Grid>
                <Grid item xs={4}>
                    <Typography className={classes.label}>old account name</Typography>
                    <Typography className={classes.value}>{old_account_name && old_account_name !== "" ? old_account_name : "-"}</Typography>
                </Grid>

                {/*  */}
                <Grid item xs={4}>
                    <Typography className={classes.label}>registered name</Typography>
                    <Typography className={classes.value}>{corporate_name}</Typography>
                </Grid>
                <Grid item xs={4}>
                    <Typography className={classes.label}>trade name</Typography>
                    <Typography className={classes.value}>{brand_name}</Typography>
                </Grid>
                <Grid item xs={4}>
                    <Typography className={classes.label}>specify mother company</Typography>
                    <Typography className={classes.value}>{company_affiliates && company_affiliates !== "" ? company_affiliates : "-"}</Typography>
                </Grid>

                {/*  */}
                <Grid item xs={4}>
                    <Typography className={classes.label}>tin number</Typography>
                    <Typography className={classes.value}>{tax_number}</Typography>
                </Grid>
                <Grid item xs={4}>
                    <Typography className={classes.label}>company contact no.</Typography>
                    <Typography className={classes.value}>{company_number && company_number !== "" ? company_number : "-"}</Typography>
                </Grid>
                <Grid item xs={4}>
                    <Typography className={classes.label}>registered address</Typography>
                    <Typography className={classes.value}>{business_address?`${business_address.floor.toLowerCase()!=="n/a" ?business_address.floor+",":""} ${business_address.unit.toLowerCase()!=="n/a" ?business_address.unit+",":""} ${business_address.bldg_no.toLowerCase()!=="n/a" ? business_address.bldg_no + ",":""} ${business_address.bldg_name.toLowerCase()!=="n/a" ?business_address.bldg_name+",":""} ${business_address.street.toLowerCase()!=="n/a" ?business_address.street+",":""} ${business_address.brgy.toLowerCase()!=="n/a"?business_address.brgy+",":""} ${business_address.city}, ${business_address.province ? business_address.province+",":""} ${business_address.region}${business_address.zip_code.toLowerCase()!=="n/a" ? ", " + business_address.zip_code:""}`:""}</Typography>
                </Grid>

                {/*  */}
                <Grid item xs={4}>
                    <Typography className={classes.label}>industry classification</Typography>
                    <Typography className={classes.value}>{industry_class_id && industry_class_id !== "" ? industry_class_id : "-"}</Typography>
                </Grid>
                <Grid item xs={4}>
                    <Typography className={classes.label}>authorized contact person</Typography>
                    { authContactFlag ?
                        <>
                            {
                                authorizedContacts.map((x:any,i:number)=>{
                                    return i < 2 && <Tooltip key={i} arrow placement="top-start" title={x && x.name?x.name:""}><Typography style={{whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}} className={classes.value}>{x && x.name?x.name:""}</Typography></Tooltip>
                                })
                            }
                            { 
                                authorizedContacts.length > 2 &&
                                    <Typography className={classes.fakeLink}  onClick={toggleMore}>+ {moreCount} more</Typography>
                            }
                        </>
                        :
                        <>
                            {
                                authorizedContacts.map((x:any,i:number)=>{
                                    return <Tooltip key={i} arrow placement="top-start" title={x && x.name?x.name:""}><Typography style={{whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}} className={classes.value}>{x && x.name?x.name:""}</Typography></Tooltip>
                                })
                            }
                            <Typography className={classes.fakeLink}  onClick={toggleMore} >less</Typography>
                        </>
                    }
                </Grid>
                <Grid item xs={4}>
                    <Typography className={classes.label}>contact person position</Typography>
                    { designatedContactFlag 
                        ?
                        <div>
                            {
                                authorizedContacts.map((x:any,i:number)=>{
                                    return i < 2 && <Tooltip  key={i} arrow placement="top-start" title={x && x.type?x.type:""}><Typography style={{whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}} className={classes.value}>{x && x.type?x.type:""}</Typography></Tooltip>
                                })
                            }
                            { 
                                authorizedContacts.length > 2 &&
                                    <Typography className={classes.fakeLink} onClick={toggleMoreDesignated} >+ {moreCount} more</Typography>
                            }
                            
                        </div>
                        :
                        <div>
                            {
                                authorizedContacts.map((x:any,i:number)=>{
                                    return <Tooltip key={i} arrow placement="top-start" title={x && x.type?x.type:""}><Typography style={{whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}} className={classes.value}>{x && x.type?x.type:""}</Typography></Tooltip>
                                })
                            }
                            <Typography className={classes.fakeLink} onClick={toggleMoreDesignated}>less</Typography>
                        </div>
                    }
                </Grid>
            </Grid>
        </>
    )
}