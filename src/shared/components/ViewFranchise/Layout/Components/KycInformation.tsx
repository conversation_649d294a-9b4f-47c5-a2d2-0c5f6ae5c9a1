import React, { useState } from 'react'

// Material Ui imports
import {
    Grid,
    Typography,
    Tooltip
} from '@mui/material'
import { Franchise } from '../../../../models/Franchise'
import moment from 'moment'

interface IProps {
    classes?: any,
    compare?: boolean,
    franchise: Franchise
}

export const KycInformation: React.FC<IProps> = (props: IProps)=> {
    const {
        classes,
        compare,
        franchise
    } = props
    const { stakeholders,signatories } = franchise
    const cStakeHolders = stakeholders??[];
    const cSignatories = signatories??[];
    const moreStakeholdersCount = cStakeHolders.length - 2;
    const moreSignatoriesCount = cSignatories.length - 2;
    const [stakeholdersFlag, setStakeholdersFlag] = useState(true)
    const [shTypeIdFlag, setSHTypeIdFlag] = useState(true)
    const [signatoriesFlag, setSignatoriesFlag] = useState(true)
    const [sTypeIdFlag, setSTypeIdFlag] = useState(true)
    const toggleStakeholdersMore = (e: { preventDefault: () => void; }) =>{
        e.preventDefault()
        setStakeholdersFlag(!stakeholdersFlag)
    }
    const toggleStakeholdersTypeIdMore = (e: { preventDefault: () => void; }) =>{
        e.preventDefault()
        setSHTypeIdFlag(!shTypeIdFlag)
    }
    const toggleSignatoriesMore = (e: { preventDefault: () => void; }) =>{
        e.preventDefault()
        setSignatoriesFlag(!signatoriesFlag)
    }
    const toggleSignatoriesTypeIdMore = (e: { preventDefault: () => void; }) =>{
        e.preventDefault()
        setSTypeIdFlag(!sTypeIdFlag)
    }

    return (
        <>
            <Grid container spacing={4}>
                <Grid item xs={4}>
                    <Typography className={classes.label}>ESTABLISHED (YEARS)</Typography>
                    <Typography className={classes.value}>{franchise.terms !== "" ? franchise.terms: "N.A"}</Typography>
                </Grid>
                <Grid item xs={4}>
                    <Typography className={classes.label}>no. of employees</Typography>
                    <Typography className={classes.value}>{franchise.no_of_employee !== "" ? franchise.no_of_employee: "N.A"}</Typography>
                </Grid>
                <Grid item xs={4}>
                    <Typography className={classes.label}>no. of dependents</Typography>
                    <Typography className={classes.value}>{franchise.no_of_dependents !== "" ? franchise.no_of_dependents: "N.A"}</Typography>
                </Grid>
                <Grid item xs={4}>
                    <Typography className={classes.label}>name of current/previous provider</Typography>
                    <Typography className={classes.value}>{franchise.provider_name !== "" ? franchise.provider_name: "N.A"}</Typography>
                </Grid>
                <Grid item xs={4}>
                    <Typography className={classes.label}>Effectivity Date</Typography>
                    <Typography className={classes.value}>{franchise.provider_effectivity_date_from && franchise.provider_effectivity_date_to ? `${moment(franchise.provider_effectivity_date_from).format("MM/DD/YYYY")} - ${moment(franchise.provider_effectivity_date_to).format("MM/DD/YYYY")}`: "-"}</Typography>
                </Grid>
                <Grid item xs={4}>
                    <Typography className={classes.label}>voluntary</Typography>
                    <Typography className={classes.value}>{franchise.is_voluntary_enrollees}</Typography>
                </Grid>
                <Grid item xs={4}>
                    <Typography className={classes.label}>company paid/shared</Typography>
                    <Typography className={classes.value}>{franchise.company_paid_shared ? franchise.company_paid_shared: "0"}</Typography>
                </Grid>
                <Grid item xs={4}>
                    <Typography className={classes.label}>name of board of directors</Typography>
                    { 
                        stakeholdersFlag ?
                        <>
                            {
                                cStakeHolders.map((x:any,i:number)=>{
                                    return i < 2 && <Tooltip key={i} arrow placement="top-start" title={x && x.name?x.name:""}><Typography style={{whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}} className={classes.value}>{x && x.name?x.name:""}</Typography></Tooltip>
                                })
                            }
                            { 
                                cStakeHolders.length > 2 &&
                                    <Typography className={classes.fakeLink}  onClick={toggleStakeholdersMore}>+ {moreStakeholdersCount} more</Typography>
                            }
                        </>
                        :
                        <>
                            {
                                stakeholders.map((x:any,i:number)=>{
                                    return <Tooltip key={i} arrow placement="top-start" title={x && x.name?x.name:""}><Typography style={{whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}} className={classes.value}>{x && x.name?x.name:""}</Typography></Tooltip>
                                })
                            }
                            <Typography className={classes.fakeLink}  onClick={toggleStakeholdersMore} >less</Typography>
                        </>
                        }
                    </Grid>
                    <Grid item xs={4}>
                        <Typography className={classes.label}>type of id presented</Typography>
                        { 
                            shTypeIdFlag ?
                                <div style={{marginTop: compare ? 18 : 0}}>
                                    {
                                        cStakeHolders.map((x:any,i:number)=>{
                                            return i < 2 && <Tooltip  key={i} arrow placement="top-start" title={x && x.type?x.type:""}><Typography style={{whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}} className={classes.value}>{x && x.type?x.type:""}</Typography></Tooltip>
                                        })
                                    }
                                    { 
                                        cStakeHolders.length > 2 &&
                                            <Typography className={classes.fakeLink} onClick={toggleStakeholdersTypeIdMore} >+ {moreStakeholdersCount} more</Typography>
                                    }
                                    
                                </div>
                            :
                                <div style={{marginTop: compare ? 18 : 0}}>
                                    {
                                        cStakeHolders.map((x:any,i:number)=>{
                                            return <Tooltip key={i} arrow placement="top-start" title={x && x.type?x.type:""}><Typography style={{whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}} className={classes.value}>{x && x.type?x.type:""}</Typography></Tooltip>
                                        })
                                    }
                                    <Typography className={classes.fakeLink} onClick={toggleStakeholdersTypeIdMore}>less</Typography>
                                </div>
                        }
                    </Grid>
                <Grid item xs={4}>
                    <Typography className={classes.label}>name of authorized signatories</Typography>
                    { 
                        signatoriesFlag ?
                            <>
                                {
                                    cSignatories.map((x:any,i:number)=>{
                                        return i < 2 && <Tooltip key={i} arrow placement="top-start" title={x && x.name?x.name:""}><Typography style={{whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}} className={classes.value}>{x && x.name?x.name:""}</Typography></Tooltip>
                                    })
                                }
                                { 
                                    cSignatories.length > 2 &&
                                        <Typography className={classes.fakeLink}  onClick={toggleSignatoriesMore}>+ {moreSignatoriesCount} more</Typography>
                                }
                            </>
                        :
                            <>
                                {
                                    cSignatories.map((x:any,i:number)=>{
                                        return <Tooltip key={i} arrow placement="top-start" title={x && x.name?x.name:""}><Typography style={{whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}} className={classes.value}>{x && x.name?x.name:""}</Typography></Tooltip>
                                    })
                                }
                                <Typography className={classes.fakeLink}  onClick={toggleSignatoriesMore} >less</Typography>
                            </>
                        }
                </Grid>
                <Grid item xs={4}>
                    <Typography className={classes.label}>type of id presented</Typography>
                    { 
                        sTypeIdFlag ?
                            <div style={{marginTop: compare ? 18 : 0}}>
                                {
                                    cSignatories.map((x:any,i:number)=>{
                                        return i < 2 && <Tooltip  key={i} arrow placement="top-start" title={x && x.type?x.type:""}><Typography style={{whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}} className={classes.value}>{x && x.type?x.type:""}</Typography></Tooltip>
                                    })
                                }
                                { 
                                    cSignatories.length > 2 &&
                                        <Typography className={classes.fakeLink} onClick={toggleSignatoriesTypeIdMore} >+ {moreSignatoriesCount} more</Typography>
                                }
                                
                            </div>
                        :
                            <div style={{marginTop: compare ? 18 : 0}}>
                                {
                                    cSignatories.map((x:any,i:number)=>{
                                        return <Tooltip key={i} arrow placement="top-start" title={x && x.type?x.type:""}><Typography style={{whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}} className={classes.value}>{x && x.type?x.type:""}</Typography></Tooltip>
                                    })
                                }
                                <Typography className={classes.fakeLink} onClick={toggleSignatoriesTypeIdMore}>less</Typography>
                            </div>
                        }
                </Grid>
                <Grid item xs={4}>
                    <Typography className={classes.label}>source of funds</Typography>
                    <Typography className={classes.value}>{franchise.source_of_funds !== "" ? franchise.source_of_funds: "N.A"}</Typography>
                </Grid>
            </Grid>
        </>
    )
}