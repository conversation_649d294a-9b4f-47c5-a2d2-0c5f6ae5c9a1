import React from "react";

// Material Ui imports
import { Avatar, List, ListItem, Typography } from "@mui/material";
import { Franchise } from "../../../../models/Franchise";
import { IUserRole } from "../../../../models/User";
import { apiURL } from "../../../../../utils/Environment";
import {
  remarksForViewFranchiseModal,
  getFranchisingRole,
} from "../../../../../utils/DataHelper";

interface IProps {
  sideItems: any[];
  activeIndex: number;
  setActiveIndex: any;
  classes?: any;
  franchise: Franchise;
  roles: IUserRole;
  userRole?: string;
}

export const Side: React.FC<IProps> = (props: IProps)=> {
  const { sideItems, activeIndex, setActiveIndex, classes, userRole = '' } = props;
  const {
    remarks,
    reasons,
    validation_message,
    supporting_docs,
    supporting_docs_obj,
    appointment_letter,
    isSaved,
    isSubmitted,
    isReSubmitted,
    isReceived,
    isValidated,
    isReturned,
    isEndorsed,
    isReturnedToValidator,
    isReEndorsed,
    isApprovalInProcess,
    isForFinalApproval,
    isFinalApprovalInProcess,
    isApproved,
    isDisApproved,
    company_logo,
    status,
    isExpired
  } = props.franchise;
  const { isSalesUser, isSpecialist, isSupervisor, isManager } = props.roles;
  let disabledRemarks = false;

  let adjustedRemarks = remarksForViewFranchiseModal(
    reasons ?? [],
    remarks ?? [],
    userRole
  );

  if (userRole !== 'encoder') {
    disabledRemarks = adjustedRemarks.length < 1;
  } else {
    if (
      status === "RECEIVED" ||
      status === "VALIDATED" ||
      status === "RESUBMITTED_TO_SUPERVISOR" ||
      status === "FOR_APPROVAL" ||
      status === "APPROVAL_IN_PROCESS" ||
      status === "RETURNED_TO_VALIDATOR" ||
      status === "FINAL_APPROVAL" ||
      status === "FINAL_APPROVAL_IN_PROCESS"
    ) {
      disabledRemarks = true;
    } else {
      disabledRemarks = adjustedRemarks.length < 1;
    }
  }

  let countDocsPerUser = 0;
  /* 
    check if appointment letter is present
    then increase countDocsPerUser count to
    enable Supporting Documents list item
  */
  if (appointment_letter) {
    countDocsPerUser++;
  }

  if (supporting_docs_obj) {
    if (isSalesUser && userRole === 'encoder') {
      for (let x = 0; x < supporting_docs_obj.length; x++) {
        if (
          supporting_docs_obj[x].main_role === "FRF_ENCODER" ||
          getFranchisingRole(supporting_docs_obj[x].roles) === "FRF_ENCODER"
        ) {
          countDocsPerUser++;
        }
      }

    } else if ((isSpecialist && userRole === 'validator') || (isSupervisor && userRole === 'supervisor') || (isManager && userRole === 'manager')) {
      if (supporting_docs.length > 0) {
        countDocsPerUser++;
      }
    }
  }
  const disabledDocs = countDocsPerUser === 0;
  console.log('disaablledd docs:', disabledDocs)

  const avatarSource = company_logo
    ? apiURL.userManagement + company_logo.replace("public", "")
    : "";

  return (
    <>
      <Avatar className={classes.avatar} variant="square" src={avatarSource}>
        {avatarSource ? "" : "T"}
      </Avatar>
      <Typography className={classes.infoTitle}>
        CORPORATE ACCOUNT INFORMATION
      </Typography>
      
      <List>
        {sideItems.map((item: any, i) => {
          if (isSalesUser && userRole === 'encoder') {
            if (isReturned && item.index !== 6) {
              if (item.index === 7) {
                if (validation_message !== "") {
                  return (
                    <ListItem
                    key={i}
                    data-cy={"sidebar-item-" + i}
                    onClick={() =>
                      (disabledRemarks && item.index === 4) ||
                      (disabledDocs && item.index === 3)
                      ? null
                      : setActiveIndex(item.index)
                    }
                    className={
                      `${classes.itemLink} ${
                        item.index === activeIndex && classes.activeItemLink
                      }` +
                      ((disabledRemarks && item.index === 4) ||
                      (disabledDocs && item.index === 3)
                      ? " vni-pointer-events-none"
                      : "")
                    }
                    disableGutters
                    >
                      {item.label}
                    </ListItem>
                  );
                }
              } else {
                return (
                  <ListItem
                  key={i}
                  data-cy={"sidebar-item-" + i}
                  onClick={() =>
                    (disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                    ? null
                    : setActiveIndex(item.index)
                  }
                  className={
                    `${classes.itemLink} ${
                      item.index === activeIndex && classes.activeItemLink
                    }` +
                    ((disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                    ? " vni-pointer-events-none"
                    : "")
                  }
                  disableGutters
                  >
                    {item.label}
                  </ListItem>
                );
              }
            } else if (
              (isValidated ||
                isApproved ||
                isEndorsed ||
                isReturnedToValidator ||
                isReEndorsed ||
                isApprovalInProcess ||
                isForFinalApproval ||
                isFinalApprovalInProcess) &&
                item.index !== 5 &&
                item.index !== 6
              ) {
                return (
                  <ListItem
                  key={i}
                  data-cy={"sidebar-item-" + i}
                  onClick={() =>
                    (disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                    ? null
                    : setActiveIndex(item.index)
                  }
                  className={
                    `${classes.itemLink} ${
                      item.index === activeIndex && classes.activeItemLink
                    }` +
                    ((disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                    ? " vni-pointer-events-none"
                    : "")
                  }
                  disableGutters
                  >
                  {item.label}
                </ListItem>
              );
            } else if (
              (isSaved || isSubmitted || isReceived) &&
              item.index !== 5 &&
              item.index !== 6 &&
              item.index !== 7
            ) {
              return (
                <ListItem
                  key={i}
                  data-cy={"sidebar-item-" + i}
                  onClick={() =>
                    (disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? null
                      : setActiveIndex(item.index)
                  }
                  className={
                    `${classes.itemLink} ${
                      item.index === activeIndex && classes.activeItemLink
                    }` +
                    ((disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? " vni-pointer-events-none"
                      : "")
                  }
                  disableGutters
                >
                  {item.label}
                </ListItem>
              );
            } else if (isReSubmitted && item.index !== 6 && item.index !== 7) {
              return (
                <ListItem
                  key={i}
                  data-cy={"sidebar-item-" + i}
                  onClick={() =>
                    (disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? null
                      : setActiveIndex(item.index)
                  }
                  className={
                    `${classes.itemLink} ${
                      item.index === activeIndex && classes.activeItemLink
                    }` +
                    ((disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? " vni-pointer-events-none"
                      : "")
                  }
                  disableGutters
                >
                  {item.label}
                </ListItem>
              );
            } else if (isDisApproved && item.index !== 5) {
              return (
                <ListItem
                  key={i}
                  data-cy={"sidebar-item-" + i}
                  onClick={() =>
                    (disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? null
                      : setActiveIndex(item.index)
                  }
                  className={
                    `${classes.itemLink} ${
                      item.index === activeIndex && classes.activeItemLink
                    }` +
                    ((disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? " vni-pointer-events-none"
                      : "")
                  }
                  disableGutters
                >
                  {item.label}
                </ListItem>
              );
            } else if  (isExpired && item.index !== 5 && item.index !== 6
            ) {
              return (
                <ListItem
                  key={i}
                  data-cy={"sidebar-item-" + i}
                  onClick={() =>
                    (disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? null
                      : setActiveIndex(item.index)
                  }
                  className={
                    `${classes.itemLink} ${
                      item.index === activeIndex && classes.activeItemLink
                    }` +
                    ((disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? " vni-pointer-events-none"
                      : "")
                  }
                  disableGutters
                >
                  {item.label}
                </ListItem>
              );
            }
          } else if (isSpecialist && userRole === 'validator') {
            if (
              (isReturned ||
                isReturnedToValidator ||
                isReSubmitted ||
                isReceived) &&
              item.index !== 6
            ) {
              if (item.index === 5) {
                if (reasons && reasons.length !== 0) {
                  return (
                    <ListItem
                      key={i}
                      data-cy={"sidebar-item-" + i}
                      onClick={() =>
                        (disabledRemarks && item.index === 4) ||
                        (disabledDocs && item.index === 3)
                          ? null
                          : setActiveIndex(item.index)
                      }
                      className={
                        `${classes.itemLink} ${
                          item.index === activeIndex && classes.activeItemLink
                        }` +
                        ((disabledRemarks && item.index === 4) ||
                        (disabledDocs && item.index === 3)
                          ? " vni-pointer-events-none"
                          : "")
                      }
                      disableGutters
                    >
                      {item.label}
                    </ListItem>
                  );
                }
              } else if (item.index === 7) {
                if (validation_message !== "") {
                  return (
                    <ListItem
                      key={i}
                      data-cy={"sidebar-item-" + i}
                      onClick={() =>
                        (disabledRemarks && item.index === 4) ||
                        (disabledDocs && item.index === 3)
                          ? null
                          : setActiveIndex(item.index)
                      }
                      className={
                        `${classes.itemLink} ${
                          item.index === activeIndex && classes.activeItemLink
                        }` +
                        ((disabledRemarks && item.index === 4) ||
                        (disabledDocs && item.index === 3)
                          ? " vni-pointer-events-none"
                          : "")
                      }
                      disableGutters
                    >
                      {item.label}
                    </ListItem>
                  );
                }
              } else {
                return (
                  <ListItem
                    key={i}
                    data-cy={"sidebar-item-" + i}
                    onClick={() =>
                      (disabledRemarks && item.index === 4) ||
                      (disabledDocs && item.index === 3)
                        ? null
                        : setActiveIndex(item.index)
                    }
                    className={
                      `${classes.itemLink} ${
                        item.index === activeIndex && classes.activeItemLink
                      }` +
                      ((disabledRemarks && item.index === 4) ||
                      (disabledDocs && item.index === 3)
                        ? " vni-pointer-events-none"
                        : "")
                    }
                    disableGutters
                  >
                    {item.label}
                  </ListItem>
                );
              }
            } else if (
              (isValidated ||
                isApproved ||
                isEndorsed ||
                isReEndorsed ||
                isApprovalInProcess ||
                isForFinalApproval ||
                isFinalApprovalInProcess) &&
              item.index !== 5 &&
              item.index !== 6
            ) {
              return (
                <ListItem
                  key={i}
                  data-cy={"sidebar-item-" + i}
                  onClick={() =>
                    (disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? null
                      : setActiveIndex(item.index)
                  }
                  className={
                    `${classes.itemLink} ${
                      item.index === activeIndex && classes.activeItemLink
                    }` +
                    ((disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? " vni-pointer-events-none"
                      : "")
                  }
                  disableGutters
                >
                  {item.label}
                </ListItem>
              );
            } else if (
              isSubmitted &&
              item.index !== 5 &&
              item.index !== 7 &&
              item.index !== 6
            ) {
              return (
                <ListItem
                  key={i}
                  data-cy={"sidebar-item-" + i}
                  onClick={() =>
                    (disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? null
                      : setActiveIndex(item.index)
                  }
                  className={
                    `${classes.itemLink} ${
                      item.index === activeIndex && classes.activeItemLink
                    }` +
                    ((disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? " vni-pointer-events-none"
                      : "")
                  }
                  disableGutters
                >
                  {item.label}
                </ListItem>
              );
            } else if (isDisApproved && item.index !== 5) {
              return (
                <ListItem
                  key={i}
                  data-cy={"sidebar-item-" + i}
                  onClick={() =>
                    (disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? null
                      : setActiveIndex(item.index)
                  }
                  className={
                    `${classes.itemLink} ${
                      item.index === activeIndex && classes.activeItemLink
                    }` +
                    ((disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? " vni-pointer-events-none"
                      : "")
                  }
                  disableGutters
                >
                  {item.label}
                </ListItem>
              );
            } else if  (isExpired && item.index !== 5 && item.index !== 6
            ) {
              return (
                <ListItem
                  key={i}
                  data-cy={"sidebar-item-" + i}
                  onClick={() =>
                    (disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? null
                      : setActiveIndex(item.index)
                  }
                  className={
                    `${classes.itemLink} ${
                      item.index === activeIndex && classes.activeItemLink
                    }` +
                    ((disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? " vni-pointer-events-none"
                      : "")
                  }
                  disableGutters
                >
                  {item.label}
                </ListItem>
              );
            }
          } else if (isSupervisor && userRole === 'supervisor') {
            if ((isReturned || isReturnedToValidator) && item.index !== 6) {
              if (item.index === 7) {
                if (validation_message !== "") {
                  return (
                    <ListItem
                      key={i}
                      data-cy={"sidebar-item-" + i}
                      onClick={() =>
                        (disabledRemarks && item.index === 4) ||
                        (disabledDocs && item.index === 3)
                          ? null
                          : setActiveIndex(item.index)
                      }
                      className={
                        `${classes.itemLink} ${
                          item.index === activeIndex && classes.activeItemLink
                        }` +
                        ((disabledRemarks && item.index === 4) ||
                        (disabledDocs && item.index === 3)
                          ? " vni-pointer-events-none"
                          : "")
                      }
                      disableGutters
                    >
                      {item.label}
                    </ListItem>
                  );
                }
              } else {
                return (
                  <ListItem
                    key={i}
                    data-cy={"sidebar-item-" + i}
                    onClick={() =>
                      (disabledRemarks && item.index === 4) ||
                      (disabledDocs && item.index === 3)
                        ? null
                        : setActiveIndex(item.index)
                    }
                    className={
                      `${classes.itemLink} ${
                        item.index === activeIndex && classes.activeItemLink
                      }` +
                      ((disabledRemarks && item.index === 4) ||
                      (disabledDocs && item.index === 3)
                        ? " vni-pointer-events-none"
                        : "")
                    }
                    disableGutters
                  >
                    {item.label}
                  </ListItem>
                );
              }
            } else if (
              (isValidated ||
                isApproved ||
                isEndorsed ||
                isReEndorsed ||
                isApprovalInProcess ||
                isForFinalApproval ||
                isFinalApprovalInProcess) &&
              item.index !== 5 &&
              item.index !== 6
            ) {
              return (
                <ListItem
                  key={i}
                  data-cy={"sidebar-item-" + i}
                  onClick={() =>
                    (disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? null
                      : setActiveIndex(item.index)
                  }
                  className={
                    `${classes.itemLink} ${
                      item.index === activeIndex && classes.activeItemLink
                    }` +
                    ((disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? " vni-pointer-events-none"
                      : "")
                  }
                  disableGutters
                >
                  {item.label}
                </ListItem>
              );
            } else if (
              (isReSubmitted || isReceived) &&
              item.index !== 5 &&
              item.index !== 7 &&
              item.index !== 6
            ) {
              return (
                <ListItem
                  key={i}
                  data-cy={"sidebar-item-" + i}
                  onClick={() =>
                    (disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? null
                      : setActiveIndex(item.index)
                  }
                  className={
                    `${classes.itemLink} ${
                      item.index === activeIndex && classes.activeItemLink
                    }` +
                    ((disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? " vni-pointer-events-none"
                      : "")
                  }
                  disableGutters
                >
                  {item.label}
                </ListItem>
              );
            } else if (isDisApproved && item.index !== 5) {
              return (
                <ListItem
                  key={i}
                  data-cy={"sidebar-item-" + i}
                  onClick={() =>
                    (disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? null
                      : setActiveIndex(item.index)
                  }
                  className={
                    `${classes.itemLink} ${
                      item.index === activeIndex && classes.activeItemLink
                    }` +
                    ((disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? " vni-pointer-events-none"
                      : "")
                  }
                  disableGutters
                >
                  {item.label}
                </ListItem>
              );
            } else if  (isExpired && item.index !== 5 && item.index !== 6
              ) {
                return (
                  <ListItem
                    key={i}
                    data-cy={"sidebar-item-" + i}
                    onClick={() =>
                      (disabledRemarks && item.index === 4) ||
                      (disabledDocs && item.index === 3)
                        ? null
                        : setActiveIndex(item.index)
                    }
                    className={
                      `${classes.itemLink} ${
                        item.index === activeIndex && classes.activeItemLink
                      }` +
                      ((disabledRemarks && item.index === 4) ||
                      (disabledDocs && item.index === 3)
                        ? " vni-pointer-events-none"
                        : "")
                    }
                    disableGutters
                  >
                    {item.label}
                  </ListItem>
                );
              }
          } else if (isManager && userRole === 'manager') {
            if (
              (isApproved || isForFinalApproval || isFinalApprovalInProcess || isExpired) &&
              item.index !== 5 &&
              item.index !== 6
            ) {
              return (
                <ListItem
                  key={i}
                  data-cy={"sidebar-item-" + i}
                  onClick={() =>
                    (disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? null
                      : setActiveIndex(item.index)
                  }
                  className={
                    `${classes.itemLink} ${
                      item.index === activeIndex && classes.activeItemLink
                    }` +
                    ((disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? " vni-pointer-events-none"
                      : "")
                  }
                  disableGutters
                >
                  {item.label}
                </ListItem>
              );
            } else if (isDisApproved && item.index !== 5) {
              return (
                <ListItem
                  key={i}
                  data-cy={"sidebar-item-" + i}
                  onClick={() =>
                    (disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? null
                      : setActiveIndex(item.index)
                  }
                  className={
                    `${classes.itemLink} ${
                      item.index === activeIndex && classes.activeItemLink
                    }` +
                    ((disabledRemarks && item.index === 4) ||
                    (disabledDocs && item.index === 3)
                      ? " vni-pointer-events-none"
                      : "")
                  }
                  disableGutters
                >
                  {item.label}
                </ListItem>
              );
            }
          }
          return null;
        })}
      </List>
    </>
  );
};
