import React from 'react'

// Material Ui imports
import {
    Grid,
    Typography
} from '@mui/material'
import { Franchise } from '../../../../models/Franchise'

interface IProps {
    classes?: any,
    franchise: Franchise
}

export const ValidationResult: React.FC<IProps> = (props: IProps)=> {
    const {
        classes,
        franchise
    } = props

    const {validation_message} = franchise
    
    let withLinebreak = validation_message? validation_message.replace(". ", `. \n`) : "";
    
    return (
        <>
            <Grid container spacing={4}>
                <Grid item xs={12}>
                    <div className={classes.value}>
                        <Typography className={classes.value}>{withLinebreak}</Typography>
                    </div>
                </Grid>
            </Grid>

        </>
    )
}