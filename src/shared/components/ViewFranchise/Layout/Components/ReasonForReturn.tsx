import React from 'react'

// Material Ui imports
import {
    Grid
} from '@mui/material'
import { Franchise, Reason } from '../../../../models/Franchise'
// import { useSelector } from 'react-redux'
// import { RootState } from '../../../../reducers/rootReducer'
import moment from 'moment';
import { getFranchisingRole } from '../../../../../utils/DataHelper'
interface IProps {
    classes?: any,
    franchise: Franchise,
    userRole: String
}

export const ReasonForReturn: React.FC<IProps> = (props: IProps)=> {
    const {
        classes,
        franchise,
        userRole
    } = props
    // const roles = useSelector((state: RootState) => state.login.userRoles)
 
    // const {
    //     isSalesUser 
    // } = roles
    const {
        reasons,
        status
    } = franchise

    console.log(userRole, 22222)

    return (
        <React.Fragment>
            {reasons && reasons.length && reasons.map((content: Reason,i: number) => {
                const actualRole = getFranchisingRole(content.roles)
                // eslint-disable-next-line
                if (userRole === "encooder") {    
                    if ((status === "RESUBMITTED" || status === "RECEIVED" || status === "RETURNED") && (content.main_role === "FRF_VALIDATOR" || actualRole === "FRF_VALIDATOR")){
                        return (
                            <Grid key={i} container spacing={4}>
                                <Grid key={i} item xs={6}>
                                    <div className={classes.value}>
                                        {content.reason} 
                                    </div>
                                </Grid>
                                <Grid item xs={6}>
                                    <div className={classes.value}>
                                        {moment(new Date(content.created_at)).format("MMMM D, YYYY")}
                                    </div>
                                </Grid>
                            </Grid>
                                
                        ) 
                    } 
                } else{    
                    return (
                        <Grid key={i} container spacing={4}>
                            <Grid key={i} item xs={6}>
                                <div className={classes.value}>
                                    {content.reason}
                                </div>
                            </Grid>
                            <Grid item xs={6}>
                                <div className={classes.value}>
                                    {moment(new Date(content.created_at)).format("MMMM D, YYYY")}
                                </div>
                            </Grid>
                        </Grid>
                    ) 
                }
                return null;
            })}
        </React.Fragment>
    )
}