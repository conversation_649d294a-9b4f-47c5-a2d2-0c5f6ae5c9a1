const safeAccess = (
  value: any,
  path: any,
  def: any = undefined,
  delimeter: any = "."
): any => {
  if (typeof path === "object") {
    return (
      path.reduce(
        (obj: any, key: any) =>
          obj && obj[key] !== "undefined" ? obj[key] : undefined,
        value
      ) || def
    );
  } else if (typeof path === "string") {
    return (
      path
        .split(delimeter)
        .reduce(
          (obj, key) =>
            obj && obj[key] !== "undefined" ? obj[key] : undefined,
          value
        ) || def
    );
  }
};

export { safeAccess };
