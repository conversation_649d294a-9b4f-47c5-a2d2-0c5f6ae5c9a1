import React, { useState, useEffect } from "react";
import { useHistory, NavLink } from "react-router-dom";

// Material Ui imports
import {
  Grid,
  Typography,
  Button,
  Box,
  List,
  ListItem,
  TextField,
  Select,
  MenuItem,
  CircularProgress,
  // Input,
  InputBase,
} from "@mui/material";

import Autocomplete from "@mui/material/Autocomplete";

import { LocalizationProvider, DatePicker } from "@mui/x-date-pickers";
import { AdapterMoment } from "@mui/x-date-pickers/AdapterMoment";

//* Channel Of Request list
import { channelOfRequestList } from "../../../../utils/Environment";

// Relative imports
import { SideItems } from "./ViewFranchise.const";
import { useStyles } from "./style";
import { Side } from "./Components/Side";
import { Main } from "./Components/Main";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../reducers/rootReducer";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUpload } from "@fortawesome/free-solid-svg-icons";
import {
  updateFranchiseOnUpload,
  setOnDeleteFileCompleted,
  uploadDocuments,
  setIsRequesting,
  setOnUploadFileRejected,
  IErrorUploading,
  updateFranchiseDueDate,
  updateFranchiseExpiryDate,
  setValidatorUploaded,
  setModalContent,
  fetchFranchise,
  setIsUpdateDueExpiry,
  setFranchiseViewUploadModal,
  closeFranchiseViewUploadModal,
  fetchBDOUsers,
  submitRefranchiseRequest,
} from "../../../reducers/FranchiseSlice";

import { safeAccess } from "./utils";

import { fetchContacts } from "../../../reducers/ContactSlice";
import { fetchDisapproveReasonList } from "../../../reducers/MaintenanceSlice";
import { fileTypes, TOTAL_MAX_SIZE } from "../../../../utils/Environment";
import { Modal } from "../../Modal";
import moment from "moment";
// import MomentUtils from "@date-io/moment";
import { getFranchisingRole } from "../../../../utils/DataHelper";
// import { ContactSupportOutlined } from "@material-ui/icons";

interface IProps {
  handleClickButton: (e: any, action: string) => any;
  handleClickApproveUsers: (e: any) => any;
  userRole: string;
  setViewFranchiseModal: (e: boolean) => any;
  isMultipleRole?: boolean;
}

export const ViewFranchise: React.FC<IProps> = (props: IProps) => {
  const { setViewFranchiseModal, isMultipleRole = false } = props
  const dispatch = useDispatch();
  const classes = useStyles();
  const history = useHistory();
  const allowedFiles = fileTypes.join(",");
  const isVFRUploadedFile = useSelector(
    (state: RootState) => state.franchise.isVFRUploadedFile
  );
  const [activeIndex, setActiveIndex] = React.useState<number>(
    isVFRUploadedFile ? 3 : 0
  );
  const franchise = useSelector((state: RootState) => state.franchise.details);
  const {
    canReturnFR,
    canSubmitFR,
    canEditFR,
    canCancelFR,
    canValidateFR,
    canApproveFR,
    canDisapproveFR,
    canUploadDocs,
  } = useSelector((state: RootState) => state.login.userPermissions);
  const sessionExpired = useSelector(
    (state: RootState) => state.login.auth.sessionExpired
  );
  const contactList = useSelector(
    (state: RootState) => state.contact.contactList
  );
  const {
    isSaved,
    isReturned,
    isSubmitted,
    isReSubmitted,
    isReceived,
    isValidated,
    isReturnedToValidator,
    isApprovalInProcess,
    isForFinalApproval,
    isFinalApprovalInProcess,
    isApproved,
    isDisApproved,
    supporting_docs_obj,
    return_due_date,
    effectivity_date,
    expiry_date,
    account_status,
    proposal_status,
    applicant_name,
    supervisor_pm_user,
    duplicate,
    submitted_at,
    manager_pm_user,
    is_reassignment_pending = false,
  } = franchise;

  const roles = useSelector((state: RootState) => state.login.userRoles);
  const { isSalesUser, isSpecialist, isSupervisor, isManager } = roles;
  const [colorStatus, setColorStatus] = useState("");
  const [formattedStatus, setFormattedStatus] = useState("");
  const [deletedFile, setDeletedFile] = useState<any[]>([]);
  const [rejectedFiles, setRejectedFiles] = useState([] as any[]);
  const fileDeleted = useSelector(
    (state: RootState) => state.franchise.deletedFile
  );
  const rejectedFile = useSelector(
    (state: RootState) => state.franchise.rejectedFile
  );
  const { handleClickButton, handleClickApproveUsers } = props;

  //PMAKER ID
  const pmaker_uid = useSelector((state: RootState) => state.login.pmaker_uid);
  const [isClaimer, setIsClaimer] = useState<boolean>(true);
  const bdoUserList = useSelector(
    (state: RootState) => state.franchise.bdoUserListData
  );
  const drlData = useSelector(
    (state: RootState) => state.maintenance.disapproveReasonList
  );
  // upload modal
  const uploadModal = useSelector(
    (state: RootState) => state.franchise.franchiseViewUploadModal
  );
  /**
   * Handle Upload Files.
   * @param event object
   */
  const handleUploadDocuments = (event: any) => {
    const accepted = [] as File[];
    const rejected = [];
    const fileList = event.dataTransfer
      ? event.dataTransfer.files
      : event.target.files;
    console.log("files", fileList, typeof fileList);
    let acceptedTotalSize = 0;
    if (supporting_docs_obj) {
      for (var i = 0; i < supporting_docs_obj.length; i++) {
        const file = supporting_docs_obj[i];
        if (
          file.main_role &&
          (file.main_role === "FRF_ENCODER" ||
            getFranchisingRole(file.roles) === "FRF_ENCODER" ||
            file.main_role === "FRF_VALIDATOR" ||
            getFranchisingRole(file.roles) === "FRF_VALIDATOR" ||
            file.main_role === "FRF_INITIAL_APPROVER" ||
            getFranchisingRole(file.roles) === "FRF_INITIAL_APPROVER" ||
            file.main_role === "FRF_FINAL_APPROVER" ||
            getFranchisingRole(file.roles) === "FRF_FINAL_APPROVER")
        ) {
          file.file_size && (acceptedTotalSize += file.file_size);
        }
      }
    }
    for (var j = 0; j < fileList.length; j++) {
      const file = fileList.item(j);
      console.log("file from fileList on LOOP", file, typeof file);
      acceptedTotalSize += file.size;
      console.log(
        "acceptedTotalSize on LOOP",
        acceptedTotalSize,
        typeof acceptedTotalSize
      );
      console.log(
        "acceptedTotalSize on LOOP greater than TOTAL_MAX_SIZE",
        acceptedTotalSize > TOTAL_MAX_SIZE
      );
      if (fileTypes.indexOf(file.type) < 0) {
        Object.defineProperty(file, "invalidFile", {
          value: true,
        });
        rejected.push(file);
        dispatch(
          setFranchiseViewUploadModal({
            status: "invalid",
            message: "File Invalid",
          })
        );
      } else if (acceptedTotalSize > TOTAL_MAX_SIZE) {
        Object.defineProperty(file, "exceedFile", {
          value: true,
        });
        rejected.push(file);
        dispatch(
          setFranchiseViewUploadModal({
            status: "exceed",
            message: "File size limit exceeded",
          })
        );
      } else {
        accepted.push(file);
      }
    }

    console.log(
      "acceptedTotalSize on AFTER LOOP",
      acceptedTotalSize,
      typeof acceptedTotalSize
    );
    console.log(
      "acceptedTotalSize on AFTER LOOP greater than TOTAL_MAX_SIZE",
      acceptedTotalSize > TOTAL_MAX_SIZE
    );

    if (rejected.length > 0) {
      setRejectedFiles([...rejectedFiles, ...rejected]);
      return;
    }

    console.log("REMAINING FILES TO BE UPLOADED ==> ", accepted);

    let fileUploaded = Object.keys(accepted).map((key: any) => {
      const filed = accepted[key];
      // return file;
      return dispatch(uploadDocuments(filed, props.userRole));
    });

    console.log("fileUploaded", fileUploaded, typeof fileUploaded);

    // return;

    Promise.all(fileUploaded)
      .then((completed: any) => {
        dispatch(
          setFranchiseViewUploadModal({
            status: 200,
            message: "File upload successful",
          })
        );
        console.log("completed", completed, typeof completed);
        let uploadedFile = [];
        if (completed) {
          for (let index = 0; index < completed.length; index++) {
            if (completed[index]) {
              const element = completed[index].data;
              uploadedFile.push(element._id);
            }
          }
        }
        console.log(
          "franchise supporting_docs",
          uploadedFile,
          typeof uploadedFile
        );
        if (uploadedFile.length) {
          let supporting_docs = [...franchise.supporting_docs, ...uploadedFile];
          dispatch(updateFranchiseOnUpload(franchise, supporting_docs));
          //@ts-ignore
          dispatch(setValidatorUploaded(true));
        }
        dispatch(setIsRequesting(false));
      })
      .catch((e) => {
        console.log("UPLOAD ERROR:", e);
        console.log(e, typeof e);
        let errMsg = "Server upload error.";
        if (e && e.response) {
          if (e.response.status && e.response.status === 413) {
            errMsg = "413 Request Entity Too Large";
          } else {
            if (e.response.text) {
              errMsg = e.response.text;
            }
          }
        }
        dispatch(
          setOnUploadFileRejected({
            name: "",
            lastModified: 0,
            type: "",
            message: errMsg,
          })
        );
        dispatch(
          setFranchiseViewUploadModal({
            status: 413,
            message: "Oops! Something went wrong",
          })
        );
        dispatch(setIsRequesting(false));
      });
  };

  const handleEdit = () => {
    history.push(`/franchising/franchise/edit/${franchise._id}`);
  };

  useEffect(() => {
    if (!sessionExpired) {
      if (["supervisor", "manager"].includes(props.userRole)) {
        dispatch(fetchBDOUsers(props.userRole));
        dispatch(fetchContacts());
        if (drlData.length === 0) {
          dispatch(fetchDisapproveReasonList());
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [drlData]);

  useEffect(() => {
    if (isSubmitted || isReSubmitted || isApproved) {
      setColorStatus("request-status green");
    } else if (isDisApproved) {
      setColorStatus("request-status scarlet");
    } else {
      setColorStatus("request-status yellow");
    }

    if(isMultipleRole) {
      if (props.userRole === "encoder") {
        switch (franchise.status) {
          case "FOR_APPROVAL":
            setFormattedStatus("For Approval");
            break;
          case "FINAL_APPROVAL":
            setFormattedStatus("For Final Approval");
            break;
          case "FINAL_APPROVAL_IN_PROCESS":
            setFormattedStatus("For Final Approval");
            break;
          case "APPROVAL_IN_PROCESS":
            setFormattedStatus("Approval In Process");
            break;
          case "RETURNED_TO_VALIDATOR":
            setFormattedStatus("Returned");
            break;
          case "RESUBMITTED_TO_SUPERVISOR":
            setFormattedStatus("Resubmitted To Supervisor");
            break;
          default:
            setFormattedStatus(franchise.status);
            break;
        }
      } else if (props.userRole === "validator") {

        switch (franchise.status) {
          case "RECEIVED":
            setFormattedStatus("Validation In-Process");
            break;
          case "VALIDATED":
            setFormattedStatus("Validation Complete");
            break;
          case "FOR_APPROVAL":
            setFormattedStatus("For Approval");
            break;
          case "FINAL_APPROVAL":
            setFormattedStatus("For Approval");
            break;
          case "FINAL_APPROVAL_IN_PROCESS":
            setFormattedStatus("Approval In Process");
            break;
          case "APPROVAL_IN_PROCESS":
            setFormattedStatus("Approval In Process");
            break;
          case "RETURNED":
            setFormattedStatus("Returned to Sales User");
            break;
          case "RETURNED_TO_VALIDATOR":
            setFormattedStatus("Returned");
            break;
          case "RESUBMITTED_TO_SUPERVISOR":
            setFormattedStatus("Resubmitted To Supervisor");
            break;
          default:
            setFormattedStatus(franchise.status);
            break;
        }
      
      } else if (props.userRole === "supervisor") {

        switch (franchise.status) {
          case "FOR_APPROVAL":
            setFormattedStatus("For Approval");
            break;
          case "FINAL_APPROVAL":
            setFormattedStatus("For Final Approval");
            break;
          case "FINAL_APPROVAL_IN_PROCESS":
            setFormattedStatus("For Final Approval");
            break;
          case "RESUBMITTED_TO_SUPERVISOR":
            setFormattedStatus("Resubmitted To Supervisor");
            break;
          case "APPROVAL_IN_PROCESS":
            setFormattedStatus("Approval In Process");
            break;
          default:
            if (
              franchise.status === "RESUBMITTED" ||
              franchise.status === "RECEIVED" ||
              franchise.status === "RETURNED_TO_VALIDATOR"
            ) {
              setFormattedStatus("Returned");
            } else {
              setFormattedStatus(franchise.status);
            }
            break;
        }
      
      } else if (props.userRole === "manager") {

        switch (franchise.status) {
          case "FINAL_APPROVAL":
            setFormattedStatus("For Approval");
            break;
          case "FINAL_APPROVAL_IN_PROCESS":
            setFormattedStatus("Approval In Process");
            break;
          case "EXPIRING_FRANCHISE":
            setFormattedStatus("Expiring Franchise");
            break;
          default:
            setFormattedStatus(franchise.status);
            break;
        }
  
        //IS CLAIMER VALIDATION?
        if (pmaker_uid === manager_pm_user) {
          setIsClaimer(true);
        } else {
          setIsClaimer(false);
        }
      
      }
    } else {
      if (isSalesUser) {
        switch (franchise.status) {
          case "FOR_APPROVAL":
            setFormattedStatus("For Approval");
            break;
          case "FINAL_APPROVAL":
            setFormattedStatus("For Final Approval");
            break;
          case "FINAL_APPROVAL_IN_PROCESS":
            setFormattedStatus("For Final Approval");
            break;
          case "APPROVAL_IN_PROCESS":
            setFormattedStatus("Approval In Process");
            break;
          case "RETURNED_TO_VALIDATOR":
            setFormattedStatus("Returned");
            break;
          case "RESUBMITTED_TO_SUPERVISOR":
            setFormattedStatus("Resubmitted To Supervisor");
            break;
          default:
            setFormattedStatus(franchise.status);
            break;
        }
      } else if (isSpecialist) {
        switch (franchise.status) {
          case "RECEIVED":
            setFormattedStatus("Validation In-Process");
            break;
          case "VALIDATED":
            setFormattedStatus("Validation Complete");
            break;
          case "FOR_APPROVAL":
            setFormattedStatus("For Approval");
            break;
          case "FINAL_APPROVAL":
            setFormattedStatus("For Approval");
            break;
          case "FINAL_APPROVAL_IN_PROCESS":
            setFormattedStatus("Approval In Process");
            break;
          case "APPROVAL_IN_PROCESS":
            setFormattedStatus("Approval In Process");
            break;
          case "RETURNED":
            setFormattedStatus("Returned to Sales User");
            break;
          case "RETURNED_TO_VALIDATOR":
            setFormattedStatus("Returned");
            break;
          case "RESUBMITTED_TO_SUPERVISOR":
            setFormattedStatus("Resubmitted To Supervisor");
            break;
          default:
            setFormattedStatus(franchise.status);
            break;
        }
      } else if (isSupervisor) {
        switch (franchise.status) {
          case "FOR_APPROVAL":
            setFormattedStatus("For Approval");
            break;
          case "FINAL_APPROVAL":
            setFormattedStatus("For Final Approval");
            break;
          case "FINAL_APPROVAL_IN_PROCESS":
            setFormattedStatus("For Final Approval");
            break;
          case "RESUBMITTED_TO_SUPERVISOR":
            setFormattedStatus("Resubmitted To Supervisor");
            break;
          case "APPROVAL_IN_PROCESS":
            setFormattedStatus("Approval In Process");
            break;
          default:
            if (
              franchise.status === "RESUBMITTED" ||
              franchise.status === "RECEIVED" ||
              franchise.status === "RETURNED_TO_VALIDATOR"
            ) {
              setFormattedStatus("Returned");
            } else {
              setFormattedStatus(franchise.status);
            }
            break;
        }
      } else if (isManager) {
        switch (franchise.status) {
          case "FINAL_APPROVAL":
            setFormattedStatus("For Approval");
            break;
          case "FINAL_APPROVAL_IN_PROCESS":
            setFormattedStatus("Approval In Process");
            break;
          case "EXPIRING_FRANCHISE":
            setFormattedStatus("Expiring Franchise");
            break;
          default:
            setFormattedStatus(franchise.status);
            break;
        }

        //IS CLAIMER VALIDATION?
        if (pmaker_uid === manager_pm_user) {
          setIsClaimer(true);
        } else {
          setIsClaimer(false);
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [franchise]);

  useEffect(() => {
    if (
      Object.keys(fileDeleted).length > 0 &&
      fileDeleted.constructor === Object
    ) {
      if (fileDeleted._id) {
        setDeletedFile([...[fileDeleted._id], ...deletedFile]);
        // dispatch(fetchFranchise(franchise._id))
        dispatch(setOnDeleteFileCompleted({}));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fileDeleted]);

  useEffect(() => {
    if (
      Object.keys(rejectedFile).length > 0 &&
      rejectedFile.constructor === Object
    ) {
      setRejectedFiles([...rejectedFiles, ...[rejectedFile]]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rejectedFile]);

  var invalidFile = rejectedFiles.some((value) => {
    return value.invalidFile;
  });
  var exceedFile = rejectedFiles.some((value) => {
    return value.exceedFile;
  });
  var serverFileError = rejectedFiles.some((value) => {
    return value.message;
  });

  const handleCloseUploadModal = () => {
    dispatch(setOnUploadFileRejected({} as IErrorUploading));
    setRejectedFiles([]);
  };

  const checkIfExpired = (expiry_date: any) => {
    const expiryConter = moment()
      .startOf("day")
      .diff(moment(expiry_date), "days");

    return expiryConter < 0
  }

  let actualEffectivityDate = moment(effectivity_date);
  let actualExpiryDate = moment(expiry_date);
  let validityCount = actualExpiryDate.diff(actualEffectivityDate, "days");
  let validityPhrase =
    validityCount > 1 ? `${validityCount} Days` : `${validityCount} Day`;

  let validityStatusColor = "";
  if (validityCount > 10) {
    validityStatusColor = "vni-uppercase vni-text-ocean_green";
  } else if (validityCount > 0 && validityCount <= 10) {
    validityStatusColor = "vni-uppercase vni-text-gold";
  } else {
    validityStatusColor = "vni-uppercase vni-text-red";
  }

  const [deadline, setDeadline] = useState<any>();
  const [invalidDeadline, setInvalidDeadline] = useState(false);
  const [openConfirmationModal, setOpenConfirmationModal] = useState(false);
  const [openErrorModal, setOpenErrorModal] = useState(false);
  const [newExpiry, setNewExpiry] = useState<any>();

  const [reAssignModal, setReassignModal] = useState<boolean>(false);
  const [acceptReAssign, setAcceptReassign] = useState<boolean>(false);
  const [confirmedReassignModal, setConfirmedReassignedModal] =
    useState<boolean>(false);
  const [newFranchiseeName, setNewFranchiseeName] = useState<{
    // full_name: string;
    id: string;
    reason: string;
    channel_request: string;
    franchisee_name: string;
    contact_person: string;
    authorized_signatory: string;
    business_address: string;
    contact_number: string;
    email_address: string;
  }>({
    id: "",
    // full_name: "",
    reason: "",
    channel_request: "",
    franchisee_name: "",
    contact_person: "",
    authorized_signatory: "",
    business_address: "",
    contact_number: "",
    email_address: "",
  });
  const [franchiseeNameOptions, setFranchiseeNameOptions] = useState<any>([]);
  // const [newReason, setNewReason] = useState<string>("");
  // const [newEffectivityDate, setNewEffectivityDate] = useState<string>(
  //   `${new Date()}`
  // );
  const [isReassignFormValid, setIsReassignFormValid] =
    useState<boolean>(false);
  // const [validEffectivityDate, setValidEffectivityDate] =
  //   useState<boolean>(true);

  let isUpdateDueExpiry = useSelector(
    (state: RootState) => state.franchise.isUpdateDueExpiry
  );

  const validationModalContent = useSelector(
    (state: RootState) => state.franchise.validationModalContent
  );
  const [confirmValues, setConfirmValues] = useState({
    action: "",
    h1: "",
    p: "",
  });

  useEffect(() => {
    if (
      [
        "In-House",
        "Called-In Request for Proposal",
        "Walk-In Request for Proposal",
        "Business Development Officer",
      ].includes(newFranchiseeName.channel_request)
    ) {
      // const bdo = bdoUserList.map((bdo: any) => bdo.full_name)
      setFranchiseeNameOptions(bdoUserList);
    } else if (newFranchiseeName.channel_request === "Broker") {
      const brkrs = contactList
        .filter((c: any) => c.type === "Broker" && c.status === "Active")
        .map((brk: any) => ({ ...brk, full_name: brk.name, user_id: brk._id }));
      setFranchiseeNameOptions(brkrs);
      // setNewFranchiseeName((state) => ({ ...state, franchisee_name: "" }));
    } else if (
      newFranchiseeName.channel_request === "New Business Agency" ||
      newFranchiseeName.channel_request === "New Business Associate"
    ) {
      const agnts = contactList
        .filter((c: any) => c.type === "Agent" && c.status === "Active")
        .map((agnts: any) => ({
          ...agnts,
          full_name: agnts.name,
          user_id: agnts._id,
        }));
      setFranchiseeNameOptions(agnts);
    } else {
      setFranchiseeNameOptions([]);
    }

    setNewFranchiseeName((state) => ({
      ...state,
      franchisee_name: "",
      id: "",
    }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [newFranchiseeName.channel_request]);

  const checkReassignFormValid = (obj: any) => {
    const requiredFields = [
      "franchisee_name",
      "business_address",
      "channel_request",
      "contact_number",
      "email_address",
      "reason",
      "contact_person",
      "authorized_signatory",
    ];
    let isValid = true;
    for (const field of requiredFields) {
      if (obj[field] === "") {
        isValid = false;
        break;
      }
    }
    setIsReassignFormValid(isValid);
  };

  useEffect(() => {
    checkReassignFormValid(newFranchiseeName);
  }, [newFranchiseeName]);

  const handleCloseConfirmationModal = () => {
    setOpenConfirmationModal(false);
    if (confirmValues.action === "due_date" && return_due_date) {
      setDeadline(null);
      setInvalidDeadline(false);
    } else if (confirmValues.action === "expiry_date" && expiry_date) {
      setNewExpiry(null);
      setHideTextContent(moment(expiry_date).format("MMMM DD, yyyy"));
    }
  };

  const handleCloseErrorModal = () => {
    dispatch(fetchFranchise(franchise._id));
    setOpenErrorModal(false);
    dispatch(setIsUpdateDueExpiry(false));
    dispatch(setModalContent({ title: "", p: "", error: false }));
  };

  // Update Due Date for Return Franchise Request
  const handleDeadlineChange = (e: any) => {
    // if (return_due_date && moment(return_due_date).format('MMMM DD, yyyy') !== moment(e).format('MMMM DD, yyyy')){
    if (return_due_date && moment(e).format("MMMM DD, yyyy")) {
      // const validDeadlineCondition = moment(e).isValid() && moment(e).startOf('day') >= moment().add(1, `d`).startOf('day');
      const validDeadlineCondition =
        moment(e).isValid() &&
        moment(e).startOf("day") >= moment().startOf("day");
      if (validDeadlineCondition) {
        setDeadline(new Date(e));
        setConfirmValues({
          ...confirmValues,
          action: "due_date",
          h1: "Update Franchise Due Date",
          p: "Are you sure you want to update the Franchise's Due Date?",
        });
        setOpenConfirmationModal(true);
      }
      setInvalidDeadline(!validDeadlineCondition);
    }
  };

  const handleConfirmedUpdate = (e: any, action: string) => {
    switch (action) {
      case "due_date":
        handleCloseConfirmationModal();
        dispatch(updateFranchiseDueDate(franchise._id, deadline));
        break;

      case "expiry_date":
        handleCloseConfirmationModal();
        dispatch(updateFranchiseExpiryDate(franchise._id, newExpiry));
        break;
    }
  };

  useEffect(() => {
    if (isUpdateDueExpiry && validationModalContent.error) {
      setOpenErrorModal(true);
    } else {
      setOpenErrorModal(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [validationModalContent.error]);

  // Update Expiry Date for Approved Franchise
  const [hideTextContent, setHideTextContent] = useState<any>();

  const handleExpiryChange = (e: any, action: string) => {
    let inputValue: any = document.getElementById("expiry_date");
    if (expiry_date && effectivity_date) {
      const validExpirylineCondition =
        moment(e).isValid() &&
        (moment(e).startOf("day") ||
          moment(e).startOf("day") > moment(effectivity_date).startOf("day"));

      if (validExpirylineCondition) {
        setNewExpiry(new Date(e));
        setConfirmValues({
          ...confirmValues,
          action: action,
          h1: "Update Franchise Expiry Date",
          p: "Are you sure you want to update the Franchise's expiration date?",
        });
        setOpenConfirmationModal(true);
        setHideTextContent(moment(e).format("MMMM DD, yyyy"));
      } else {
        setHideTextContent(inputValue.value);
      }
      setInvalidDeadline(!validExpirylineCondition);
    }
  };

  const duplicateCount =
    duplicate && duplicate.length > 0 ? duplicate.length : 0;

  const resizable = (e: any, hide: HTMLSpanElement | null) => {
    if (e !== null && hide !== null) {
      resize();
      e.addEventListener("input", resize);
    }

    function resize() {
      if (e !== null && hide !== null) {
        hide.textContent = hideTextContent;
        e.style.width = hide.offsetWidth + "px";
      }
    }
  };

  useEffect(() => {
    setHideTextContent(moment(expiry_date).format("MMMM DD, yyyy"));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [franchise]);

  useEffect(() => {
    let expiryValue: any = document.getElementById("expiry_date");
    let expiryTextContent = document.getElementById("hide");

    if (expiryValue) {
      resizable(expiryValue, expiryTextContent);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hideTextContent]);

  console.log("STAT", isManager, canUploadDocs, isClaimer, account_status);
  return (
    <>
      <Modal
        fullWidth={false}
        maxWidth="sm"
        onClose={handleCloseUploadModal}
        open={rejectedFiles.length > 0}
      >
        <Grid>
          <Typography
            className={classes.modalTitle}
            style={{ marginBottom: 20 }}
          >
            Upload Error
          </Typography>
          {(rejectedFiles.length > 1 && (
            <>
              {invalidFile && (
                <>
                  <Typography className={classes.modalText}>
                    File type not supported.
                  </Typography>
                  <List>
                    {rejectedFiles.map(
                      (value, i) =>
                        value.invalidFile && (
                          <ListItem key={i}>
                            <Typography className={classes.modalText}>
                              File : {value.name}
                            </Typography>
                          </ListItem>
                        )
                    )}
                  </List>
                </>
              )}
              {exceedFile && (
                <Typography className={classes.modalText}>
                  File size limit exceeded. Maximum upload file size is 1GB.
                </Typography>
              )}
              {serverFileError && (
                <>
                  <Typography className={classes.modalText}>
                    Server upload error.
                  </Typography>
                </>
              )}
            </>
          )) ||
            (rejectedFiles.length > 0 && (
              <Typography className={classes.modalText}>
                {(rejectedFiles[0].invalidFile && "File type not supported.") ||
                  (rejectedFiles[0].exceedFile &&
                    "File size limit exceeded. Maximum upload file size is 1GB.") ||
                  rejectedFiles[0].message}
              </Typography>
            ))}
        </Grid>
        <Grid style={{ textAlign: "center" }}>
          <Button
            data-cy="okay-btn"
            style={{ marginTop: 20 }}
            className={classes.rightButton}
            onClick={handleCloseUploadModal}
          >
            Okay
          </Button>
        </Grid>
      </Modal>

      {/* Update due date/update expiry date confirmation modal*/}
      <Modal
        fullWidth={confirmValues.action === "expiry_date" ? true : false}
        maxWidth={confirmValues.action === "expiry_date" ? "md" : "sm"}
        open={openConfirmationModal}
        onClose={handleCloseConfirmationModal}
      >
        <Grid>
          <Typography
            className={classes.modalTitle}
            style={{ marginBottom: 20 }}
          >
            {confirmValues.h1}
          </Typography>
          <Typography className={classes.modalTextExpiry}>
            {confirmValues.p}
          </Typography>

          {confirmValues.action === "expiry_date" && (
            <Grid
              container
              style={{
                textAlign: "center",
                marginTop: "2rem",
                marginBottom: "2rem",
              }}
            >
              <Grid xs={6}>
                <Typography className={classes.label}>
                  Effectivity Date :
                </Typography>
                <LocalizationProvider dateAdapter={AdapterMoment}>
                  <DatePicker
                    format="MM/DD/yyyy"
                    minDate={moment(effectivity_date).add(1, "d")}
                    value={moment(effectivity_date)}
                    onChange={(e) => handleExpiryChange(e, "expiry_date")}
                    disabled={true}
                    slotProps={{
                      textField: {
                        className: "no-underline-date " + validityStatusColor,
                        error: invalidDeadline,
                        inputProps: {
                          "data-cy": "expiry_date",
                          disabled:
                            !isManager ||
                            validityCount < 0 ||
                            account_status === "ACTIVE" ||
                            !isClaimer,
                        },
                      },
                    }}
                  />
                </LocalizationProvider>
              </Grid>

              <Grid xs={6}>
                <Typography className={classes.label}>
                  New Expiry Date :
                </Typography>
                <LocalizationProvider dateAdapter={AdapterMoment}>
                  <DatePicker
                    format="MM/DD/yyyy"
                    minDate={moment(effectivity_date).add(1, "d")}
                    value={moment(newExpiry ?? expiry_date)} // Correct: Pass Moment object
                    onChange={(e) => setNewExpiry(e)}
                    slotProps={{
                      textField: {
                        className: "no-underline-date " + validityStatusColor,
                        error: invalidDeadline,
                        inputProps: {
                          "data-cy": "new_expiry_date",
                        },
                      },
                    }}
                  />
                </LocalizationProvider>
              </Grid>
            </Grid>
          )}

          <Grid style={{ textAlign: "center" }}>
            <Button
              data-cy="cancel-update-btn"
              style={{ marginTop: 20 }}
              className={classes.leftButton}
              onClick={handleCloseConfirmationModal}
            >
              No
            </Button>
            <Button
              data-cy="confirm-update-btn"
              style={{ marginTop: 20 }}
              className={classes.rightButton}
              onClick={(e) => handleConfirmedUpdate(e, confirmValues.action)}
            >
              Yes
            </Button>
          </Grid>
        </Grid>
      </Modal>

      {/* Error modal for update due/expiry */}
      <Modal
        fullWidth={false}
        maxWidth="sm"
        open={openErrorModal}
        onClose={handleCloseErrorModal}
      >
        <Grid>
          <Typography
            className={classes.modalTitle}
            style={{ marginBottom: 20 }}
          >
            {validationModalContent.title}
          </Typography>
          <Typography className={classes.modalText}>
            {validationModalContent.p}
          </Typography>
          <Grid style={{ textAlign: "center" }}>
            <Button
              data-cy="okay-error-btn"
              style={{ marginTop: 20 }}
              className={classes.rightButton}
              onClick={handleCloseErrorModal}
            >
              Okay
            </Button>
          </Grid>
        </Grid>
      </Modal>

      <Grid container className="vni-flex">
        <Grid item xs={2}>
          <Typography className={classes.title}>
            <span style={{ wordWrap: "break-word"}}>{franchise.corporate_name}</span>
          </Typography>
        </Grid>
        <Grid item xs={!isApproved ? 6 : 3} style={{ paddingLeft: 60 }}>
          <small className="vni-uppercase vni-font-bold vni-py-2">
            Franchise Status
            <Box component="span" className={colorStatus}>
              {formattedStatus}
            </Box>
            <br />
            <Box component="span">
              Date and Time Submitted:{" "}
              {moment(submitted_at).format("MM/DD/YYYY - hh:mm:ss a")}
            </Box>
          </small>

          {franchise.isReturned ? (
            isSalesUser ? (
              <Box
                component="span"
                className="vni-text-manatee vni-block vni-p-2"
              >
                Kindly submit on or before:{" "}
                <Box component="span">
                  {moment(return_due_date).format("MMMM D, YYYY")}
                </Box>
              </Box>
            ) : isSpecialist ? (
              <>
                <Box
                  component="span"
                  className="vni-text-manatee vni-flex vni-items-center vni-py-2"
                  style={{ fontSize: 13 }}
                >
                  Deadline:
                  <Box component="span" style={{ marginLeft: 10 }}>
                    <LocalizationProvider dateAdapter={AdapterMoment}>
                      <DatePicker
                        format="MMMM DD, yyyy"
                        minDate={moment().startOf("day")}
                        value={deadline ?? return_due_date}
                        onChange={handleDeadlineChange}
                        slotProps={{
                          textField: {
                            style: { maxWidth: 200 },
                            id: "return_deadline",
                            className: "no-underline-date",
                            variant: "standard",
                            inputProps: {
                              "data-cy": "return_deadline"
                            },
                            error: invalidDeadline
                          }
                        }}
                      />
                    </LocalizationProvider  >
                  </Box>
                </Box>
              </>
            ) : null
          ) : null}

          {isApproved && (
            <>
              <Typography
                className="vni-mt-3 vni-font-bold"
                style={{ fontWeight: "bold", color: "#272E4C", fontSize: 14 }}
              >
                <>
                  <Box component="span" className={validityStatusColor}>
                    {validityPhrase}
                  </Box>{" "}
                  Validity Period
                </>
              </Typography>
            </>
          )}
        </Grid>
        {isApproved ? (
          <>
            <Grid item style={{ paddingLeft: 18 }}>
              <small className="vni-uppercase vni-font-bold">
                Account Status
                <Box component="span" className={colorStatus}>
                  {account_status}
                </Box>
              </small>
              <Typography
                className="vni-mt-3 vni-font-bold"
                style={{ fontWeight: "bold", color: "#272E4C", fontSize: 14 }}
              >
                <Box component="span" className={validityStatusColor}>
                  {moment(effectivity_date).format("MMMM D, YYYY")}
                </Box>{" "}
                Franchise Effectivity Date
              </Typography>
            </Grid>
            <Grid item style={{ paddingLeft: 18 }}>
              <small className="vni-uppercase vni-font-bold">
                Proposal Status
                <Box component="span" className={colorStatus}>
                  {proposal_status}
                </Box>
              </small>
              <Typography
                className="vni-mt-3 vni-font-bold vni-expirydate"
                style={{ fontWeight: "bold", color: "#272E4C", fontSize: 14 }}
              >
                <div className="pickerOne">
                  <span id="hide"></span>
                  <LocalizationProvider dateAdapter={AdapterMoment}>
                    <DatePicker
                      format="MMMM DD, YYYY"
                      minDate={moment(effectivity_date).add(1, "d")}
                      value={moment(newExpiry ?? expiry_date)}
                      onChange={(e) => handleExpiryChange(e, "expiry_date")}
                      disabled={
                        !isManager ||
                        validityCount < 0 ||
                        account_status === "ACTIVE" ||
                        !isClaimer
                      }
                      slotProps={{
                        textField: {
                          style: { maxWidth: 200 },
                          id: "expiry_date",
                          className:
                            "no-underline-date " + validityStatusColor,
                          error: invalidDeadline,
                          variant: "standard",
                          inputProps: {
                            "data-cy": "expiry_date",
                            disabled:
                              !isManager ||
                              validityCount < 0 ||
                              account_status === "ACTIVE" ||
                              !isClaimer,
                          },
                        },
                      }}
                    />
                  </LocalizationProvider>
                </div>
                Franchise Expiration Date
                {((isManager &&
                  // isClaimer &&
                  validityCount >= 0) ||
                  isSupervisor) && (
                  <div className="pickerTwo">
                    <LocalizationProvider dateAdapter={AdapterMoment}>
                      <DatePicker
                        format="MMMM DD, yyyy"
                        minDate={moment(effectivity_date).add(1, "d")} // Correct: Pass a Moment object
                        value={moment(newExpiry ?? expiry_date)} // Correct: Pass Moment object
                        onChange={(e) => handleExpiryChange(e, "expiry_date")}
                        slotProps={{
                          textField: {
                            style: { maxWidth: 200 },
                            id: "expiry_date_icon",
                            className: "no-underline-date date2",
                            variant: "standard",
                            error: invalidDeadline,
                            inputProps: {
                              "data-cy": "expiry_date_icon",
                            },
                          },
                        }}
                      />
                    </LocalizationProvider>
                  </div>
                )}
              </Typography>
            </Grid>
          </>
        ) : null}

        {(isSpecialist || isSupervisor || isManager) &&
          (isValidated ||
            isReturnedToValidator ||
            isApprovalInProcess ||
            isFinalApprovalInProcess) && (
            <Grid item xs={2}>
              <div className={classes.fakeLink} style={{ opacity: 1 }}>
                <FontAwesomeIcon
                  className={classes.uploadIcon}
                  icon={faUpload}
                />
                <label
                  htmlFor="upload_document"
                  className={classes.fakeLink}
                  style={{ opacity: 1 }}
                >
                  <Typography className={classes.uploadLink}>
                    Upload Documents
                  </Typography>
                </label>
                <input
                  type="file"
                  id="upload_document"
                  data-cy="upload_document"
                  multiple
                  hidden
                  accept={allowedFiles}
                  onChange={handleUploadDocuments}
                  onClick={(event: any) => {
                    event.target.value = null;
                  }}
                />
              </div>
              <Typography
                style={{
                  fontSize: "0.90rem",
                  color: "#555555",
                }}
              >
                The system accepts file size up to 1GB only and file types:
                docx, pdf, xls, jpg, png, tif
              </Typography>
            </Grid>
          )}
      </Grid>
      {
        /***
         * Remove isManager to test Duplicate FR
         * Use initial approver who approved the duplicate requests
         */
        isManager && (
          <Grid container className="vni-flex vni-items-center">
            <Grid item xs={2}></Grid>
            <Grid item xs={10} style={{ paddingLeft: 50 }}>
              <Typography className="vni-text-manatee vni-block vni-p-2 vni-requestedBy-section">
                Requested by: {applicant_name}
                {(isFinalApprovalInProcess || isForFinalApproval) &&
                  duplicateCount > 0 && (
                    <NavLink
                      to={"#"}
                      data-cy="compare-franchise"
                      className="vni-inline vni-font-bold vni-text-ocean_green vni-underline"
                      onClick={handleClickApproveUsers}
                    >
                      +{duplicateCount} more
                    </NavLink>
                  )}
              </Typography>
            </Grid>
          </Grid>
        )
      }
      <Grid className={classes.body} container>
        <Grid className={classes.leftSide} item xs={3}>
          <Side
            setActiveIndex={setActiveIndex}
            classes={classes}
            activeIndex={activeIndex}
            sideItems={SideItems}
            franchise={franchise}
            roles={roles}
            userRole={props.userRole}
          />
        </Grid>
        <Grid className={classes.rightSide} item xs={9}>
          <Main
            deletedFile={deletedFile}
            classes={classes}
            franchise={franchise}
            activeIndex={activeIndex}
            roles={roles}
            userRole={props.userRole}
          />
        </Grid>
      </Grid>
      <Grid className={classes.buttonGroup} justifyContent="flex-end" container>
        <Grid item xs={12} style={{ textAlign: "right" }}>
          {props.userRole === "supervisor" &&
          isSupervisor &&
          isApprovalInProcess &&
          canReturnFR ? (
            <Button
              data-cy="return-validator-btn"
              className={classes.leftButtonRedOutline}
              onClick={(e) => handleClickButton(e, "return_validator")}
            >
              Return
            </Button>
          ) : null}
          {((props.userRole === "supervisor" &&
            isSupervisor &&
            isApprovalInProcess) ||
            (props.userRole === "manager" &&
              isManager &&
              (isForFinalApproval || isFinalApprovalInProcess))) &&
            canDisapproveFR && (
              <Button
                data-cy="disapprove-btn"
                className={classes.leftButton}
                onClick={(e) => handleClickButton(e, "disapprove")}
              >
                Disapprove
              </Button>
            )}
          {((props.userRole === "supervisor" &&
            isSupervisor &&
            isApprovalInProcess) ||
            (props.userRole === "manager" &&
              isManager &&
              (isForFinalApproval || isFinalApprovalInProcess))) &&
            canApproveFR && (
              <Button
                data-cy="approve-btn"
                className={classes.rightButton}
                onClick={(e) => handleClickButton(e, "approve")}
              >
                Approve
              </Button>
            )}
          {props.userRole === "validator" &&
            isSpecialist &&
            (isReceived || isValidated || isReturnedToValidator) &&
            canReturnFR && (
              <Button
                data-cy="return-btn"
                className={classes.leftButton}
                onClick={(e) => handleClickButton(e, "return")}
              >
                Return
              </Button>
            )}
          {props.userRole === "validator" &&
          isSpecialist &&
          (isSubmitted || isReceived || isReSubmitted) &&
          canValidateFR ? (
            <Button
              data-cy="validate-btn"
              className={classes.rightButton}
              onClick={(e) => handleClickButton(e, "validate")}
            >
              Validate
            </Button>
          ) : null}
          {props.userRole === "validator" &&
          isSpecialist &&
          (isReturnedToValidator || (isValidated && supervisor_pm_user)) &&
          canSubmitFR ? (
            <Button
              data-cy="re-endorse-btn"
              className={classes.rightButton}
              onClick={(e) => handleClickButton(e, "reendorse")}
            >
              Endorse
            </Button>
          ) : null}
          {props.userRole === "validator" &&
          isSpecialist &&
          isValidated &&
          !supervisor_pm_user &&
          canSubmitFR ? (
            <Button
              data-cy="endorse-btn"
              className={classes.rightButton}
              onClick={(e) => handleClickButton(e, "endorse")}
            >
              Endorse
            </Button>
          ) : null}
          {props.userRole === "encoder" &&
          isSalesUser &&
          isSaved &&
          canCancelFR ? (
            <Button
              data-cy="cancel-btn"
              className={classes.leftButton}
              onClick={(e) => handleClickButton(e, "cancel")}
            >
              Cancel Franchise
            </Button>
          ) : null}
          {props.userRole === "encoder" &&
          isSalesUser &&
          isSaved &&
          canSubmitFR ? (
            <Button
              data-cy="submit-btn"
              className={classes.rightButton}
              onClick={(e) => handleClickButton(e, "submit")}
            >
              Submit
            </Button>
          ) : null}
          {props.userRole === "encoder" &&
          isSalesUser &&
          isReturned &&
          canEditFR ? (
            <Button
              data-cy="edit-btn"
              className={classes.leftButtonOutline}
              onClick={handleEdit}
            >
              Edit
            </Button>
          ) : null}
          {props.userRole === "encoder" &&
          isSalesUser &&
          isReturned &&
          canSubmitFR ? (
            <Button
              data-cy="re-submit-btn"
              className={classes.rightButton}
              onClick={(e) => handleClickButton(e, "resubmit")}
            >
              Submit
            </Button>
          ) : null}
          {((props.userRole === "manager" && isManager) ||
            (props.userRole === "supervisor" && isSupervisor)) &&
          isApproved && checkIfExpired(expiry_date) && !is_reassignment_pending ? (
            <Button
              data-cy="re-submit-btn"
              className={classes.rightButton}
              onClick={(e) => {
                setReassignModal((prev) => !prev);
              }}
            >
              REASSIGN FRANCHISE
            </Button>
          ) : null}
        </Grid>
      </Grid>

      <Modal
        fullWidth={true}
        maxWidth="sm"
        open={uploadModal.toggle}
        onClose={() => dispatch(closeFranchiseViewUploadModal())}
        whereThisIsUsed={`vfr-view-contact`}
      >
        <Grid>
          <Typography
            className={classes.modalTitle}
            style={{ marginBottom: 20 }}
          >
            Upload Files
          </Typography>
          <Typography className={classes.modalText}>
            {uploadModal.message}
          </Typography>
          <Grid className="btn-group" style={{ textAlign: "right" }}>
            <Button
              data-cy="okay-btn"
              style={{ marginTop: 20 }}
              className={classes.rightButton}
              onClick={() => dispatch(closeFranchiseViewUploadModal())}
            >
              Okay
            </Button>
          </Grid>
        </Grid>
      </Modal>

      <Modal
        fullWidth={true}
        maxWidth="md"
        open={reAssignModal}
        onClose={() => {
          setReassignModal((prev) => !prev);
          setNewFranchiseeName({
            id: "",
            // full_name: "",
            reason: "",
            channel_request: "",
            franchisee_name: "",
            contact_person: "",
            authorized_signatory: "",
            business_address: "",
            contact_number: "",
            email_address: "",
          });
          // setNewReason("");
        }}
      >
        <Grid>
          <Typography className={classes.title} style={{ marginBottom: 20 }}>
            Reassign Franchise
          </Typography>
          <Grid container spacing={5}>
            <Grid item xs={6}>
              <Typography className={classes.modalText}>
                Current Franchisee
              </Typography>
              <TextField
                variant="outlined"
                value={applicant_name || ""}
                fullWidth
                disabled
              />
            </Grid>
            <Grid item xs={6}>
              <Typography className={classes.modalText}>
                Reason for Reassignment{" "}
                <span className="vni-text-red-500">*</span>
              </Typography>
              <Select
                variant="outlined"
                fullWidth
                onChange={(e: any) => {
                  const value = e.target.value.trim() as string;

                  setNewFranchiseeName((state) => ({
                    ...state,
                    reason: value,
                  }));
                }}
                required
                value={newFranchiseeName.reason}
              >
                {drlData.length > 0 ? (
                  drlData.map((drl: any) => (
                    <MenuItem value={drl.reason_for_disapprove}>
                      {drl.reason_for_disapprove}
                    </MenuItem>
                  ))
                ) : (
                  <MenuItem disabled>List Loading...</MenuItem>
                )}
              </Select>
            </Grid>
          </Grid>
          <h3 className="vni-block vni-text-xl vni-font-bold vni-mb-3 vni-mt-3">
            New Franchise Applicant's Information
          </h3>

          <Grid container spacing={5}>
            <Grid item xs={6}>
              <Typography className={classes.modalText}>
                Channel of Request <span className="vni-text-red-500">*</span>
              </Typography>
              <Select
                variant="outlined"
                fullWidth
                onChange={(e: any) => {
                  const value = e.target.value.trim() as string;

                  setNewFranchiseeName((state) => ({
                    ...state,
                    channel_request: value,
                  }));
                }}
                required
                value={newFranchiseeName.channel_request}
              >
                {channelOfRequestList.map((option, index) => (
                  <MenuItem
                    data-cy={`channel-option-${index + 1}`}
                    key={index}
                    value={option.value}
                  >
                    {option.name}
                  </MenuItem>
                ))}
              </Select>
            </Grid>
            <Grid item xs={6}>
              <Typography className={classes.modalText}>
                Authorized Signatory{" "}
                <span className="vni-text-red-500">*</span>
              </Typography>
              <InputBase
                className={classes.inputBase}
                fullWidth
                inputProps={{
                  style: {
                    padding: "0 8px",
                  },
                }}
                onChange={(e: any) => {
                  const value = e.target.value as string;

                  setNewFranchiseeName((state) => ({
                    ...state,
                    authorized_signatory: value,
                  }));
                }}
                value={newFranchiseeName.authorized_signatory}
              />
            </Grid>
          </Grid>

          <Grid container spacing={5}>
            <Grid item xs={6}>
              <Typography className={classes.modalText}>
                Franchisee Name <span className="vni-text-red-500">*</span>
              </Typography>

              <Autocomplete
                freeSolo={
                  newFranchiseeName.channel_request !==
                  "Business Development Officer"
                }
                value={newFranchiseeName.franchisee_name}
                disableClearable
                options={franchiseeNameOptions}
                getOptionLabel={(option: any) => {
                  let _option = "";

                  if (typeof option === "object") _option = option.full_name;
                  else if (typeof option === "string") _option = option;

                  return _option;
                }}
                onChange={(event: any, value: any) => {
                  setNewFranchiseeName((state) => ({
                    ...state,
                    id: safeAccess(value, "user_id", ""),
                    franchisee_name: safeAccess(value, "full_name", ""),
                    contact_number: safeAccess(value, "contact_number", ""),
                    contact_person: safeAccess(value, "contact_person", ""),
                    authorized_signatory: safeAccess(
                      value,
                      "authorized_signatory"
                    ),
                    email_address:
                      safeAccess(value, "email_address", "") ||
                      safeAccess(value, "email", ""),
                    business_address: `${safeAccess(
                      value,
                      "business_address.unit",
                      ""
                    )} ${safeAccess(
                      value,
                      "business_address.floor",
                      ""
                    )} ${safeAccess(
                      value,
                      "business_address.bldg_name",
                      ""
                    )} ${safeAccess(
                      value,
                      "business_address.street",
                      ""
                    )} ${safeAccess(
                      value,
                      "business_address.brgy",
                      ""
                    )} ${safeAccess(
                      value,
                      "business_address.city",
                      ""
                    )} ${safeAccess(
                      value,
                      "business_address.province",
                      ""
                    )} ${safeAccess(
                      value,
                      "business_address.zip_code",
                      ""
                    )} ${safeAccess(
                      value,
                      "business_address.province",
                      ""
                    )}`.trim(),
                  }));
                }}
                onInputChange={(event, value) => {
                  if (
                    newFranchiseeName.channel_request !==
                    "Business Development Officer"
                  ) {
                    setNewFranchiseeName((state) => ({
                      ...state,
                      franchisee_name: value,
                    }));
                  }
                }}
                renderInput={(params) => {
                  const { InputLabelProps, InputProps, ...rest } = params;
                  return (
                    <InputBase
                      style={{ padding: "0 8px" }}
                      {...params.InputProps}
                      className={classes.inputBase}
                      {...rest}
                    />
                  );
                }}
              />
            </Grid>

            <Grid item xs={6}>
              <Typography className={classes.modalText}>
                Contact Person <span className="vni-text-red-500">*</span>
              </Typography>
              <InputBase
                className={classes.inputBase}
                fullWidth
                inputProps={{
                  style: {
                    padding: "0 8px",
                  },
                }}
                onChange={(e: any) => {
                  const value = e.target.value as string;

                  setNewFranchiseeName((state) => ({
                    ...state,
                    contact_person: value,
                  }));
                }}
                value={newFranchiseeName.contact_person}
              />
            </Grid>
          </Grid>
          <Grid container spacing={5}>
            <Grid item xs={6}>
              <Typography className={classes.modalText}>
                Business Address <span className="vni-text-red-500">*</span>
              </Typography>

              <InputBase
                id="standard-multiline-static"
                className={classes["inputBase-multiline"]}
                multiline
                rows={6}
                inputProps={{
                  style: {
                    paddingLeft: 8,
                  },
                }}
                onChange={(e: any) => {
                  const value = e.target.value as string;

                  setNewFranchiseeName((state) => ({
                    ...state,
                    business_address: value,
                  }));
                }}
                value={newFranchiseeName.business_address}
                fullWidth
              />
            </Grid>
            <Grid item xs={6}>
              <Typography className={classes.modalText}>
                Contact Number <span className="vni-text-red-500">*</span>
              </Typography>
              <InputBase
                className={classes.inputBase}
                fullWidth
                inputProps={{
                  style: {
                    padding: "0 8px",
                  },
                }}
                onChange={(e: any) => {
                  const value = e.target.value.trim() as string;
                  setNewFranchiseeName((state) => ({
                    ...state,
                    contact_number: value,
                  }));
                }}
                value={newFranchiseeName.contact_number}
              />

              <Typography
                className={classes.modalText}
                style={{ marginTop: 20 }}
              >
                Email Address <span className="vni-text-red-500">*</span>
              </Typography>
              <InputBase
                className={classes.inputBase}
                fullWidth
                inputProps={{
                  style: {
                    padding: "0 8px",
                  },
                }}
                onChange={(e: any) => {
                  const value = e.target.value.trim() as string;
                  setNewFranchiseeName((state) => ({
                    ...state,
                    email_address: value,
                  }));
                }}
                value={newFranchiseeName.email_address}
              />
            </Grid>
          </Grid>
          <Grid
            spacing={4}
            className="btn-group"
            style={{ textAlign: "right" }}
          >
            <Button
              style={{
                marginTop: 20,
                backgroundColor: "#2a334b",
                color: "#fff",
                marginRight: 10,
              }}
              className={classes.modalButtonLeft}
              onClick={() => {
                setNewFranchiseeName({
                  id: "",
                  reason: "",
                  channel_request: "",
                  franchisee_name: "",
                  contact_person: "",
                  authorized_signatory: "",
                  business_address: "",
                  contact_number: "",
                  email_address: "",
                });
                setReassignModal((prev) => !prev);
              }}
            >
              Cancel
            </Button>
            <Button
              style={{ marginTop: 20 }}
              className={classes.rightButton}
              onClick={() => setAcceptReassign((prev) => !prev)}
              disabled={!isReassignFormValid}
            >
              Reassign Franchise
            </Button>
          </Grid>
        </Grid>
      </Modal>

      <Modal
        fullWidth={true}
        maxWidth="sm"
        open={acceptReAssign}
        onClose={() => {
          setAcceptReassign((prev) => !prev);
        }}
      >
        <Grid>
          <Typography className={classes.title} style={{ marginBottom: 20 }}>
            Confirmation
          </Typography>
          <Grid container spacing={1}>
            <Typography className={classes.modalText}>
              Are you sure you want to reassign the franchise to{" "}
              {newFranchiseeName?.franchisee_name}? This will be sent to
              Underwriting for approval. If approved, the following actions will
              be done:
            </Typography>
            <ul className={classes.confirmationList}>
              <li>
                <Typography className={classes.modalText}>
                  Create a new, approved FRF for the new franchisee
                </Typography>
              </li>
              <li>
                <Typography className={classes.modalText}>
                  Change the status of the old FRF to EXPIRED
                </Typography>
              </li>
            </ul>
          </Grid>
          <Grid className="btn-group" style={{ textAlign: "center" }}>
            <Button
              style={{ marginTop: 35 }}
              className={classes.leftButtonOutline}
              onClick={() => {
                setAcceptReassign((prev) => !prev);
              }}
            >
              No
            </Button>
            <Button
              style={{ marginTop: 35 }}
              className={classes.rightButton}
              onClick={() => {
                setConfirmedReassignedModal((prev) => !prev);
                dispatch(
                  setModalContent({
                    title: "",
                    p: "",
                    error: false,
                  })
                );
                dispatch(
                  submitRefranchiseRequest(franchise._id, {
                    franchisee_id: newFranchiseeName.id,
                    franchisee_name: newFranchiseeName.franchisee_name,
                    reason_for_refranchise: newFranchiseeName.reason,
                    reassign_to_channel_request:
                      newFranchiseeName.channel_request,
                    reassign_to_business_address:
                      newFranchiseeName.business_address,
                    reassign_to_contact_person:
                      newFranchiseeName.contact_person,
                    reassign_to_authorized_signatory:
                      newFranchiseeName.authorized_signatory,
                    reassign_to_contact_number:
                      newFranchiseeName.contact_number,
                    reassign_to_email_address: newFranchiseeName.email_address,
                    client_id: franchise.client_id,
                  })
                );
              }}
            >
              Yes
            </Button>
          </Grid>
        </Grid>
      </Modal>

      <Modal
        fullWidth={true}
        maxWidth="sm"
        open={confirmedReassignModal}
        onClose={() => setConfirmedReassignedModal((prev) => !prev)}
      >
        <Grid>
          <Typography className={classes.title} style={{ marginBottom: 20 }}>
            {validationModalContent.title}
          </Typography>
          <Grid
            // justifyContent="center"
            container
            spacing={5}
            style={{ justifyContent: "center", marginBottom: "2rem" }}
          >
            {validationModalContent.p ? (
              <Typography
                className={classes.modalText}
                style={{
                  marginTop: "2rem",
                  marginBottom: "1rem",
                  padding: "1rem",
                }}
              >
                {validationModalContent.p}
              </Typography>
            ) : (
              <CircularProgress color="primary" />
            )}
          </Grid>
          <Grid className="btn-group" style={{ textAlign: "center" }}>
            {validationModalContent.p && (
              <Button
                style={{ marginTop: 20 }}
                className={classes.rightButton}
                onClick={() => {
                  // setNewEffectivityDate("");
                  setNewFranchiseeName({
                    id: "",
                    reason: "",
                    channel_request: "",
                    franchisee_name: "",
                    contact_person: "",
                    authorized_signatory: "",
                    business_address: "",
                    contact_number: "",
                    email_address: "",
                  });
                  setConfirmedReassignedModal((prev) => !prev);
                  setReassignModal((prev) => !prev);
                  setAcceptReassign((prev) => !prev);
                  dispatch(
                    setModalContent({
                      title: "",
                      p: "",
                      error: false,
                    })
                  );
                  setViewFranchiseModal(false);
                }}
              >
                OK
              </Button>
            )}
          </Grid>
        </Grid>
      </Modal>
    </>
  );
};