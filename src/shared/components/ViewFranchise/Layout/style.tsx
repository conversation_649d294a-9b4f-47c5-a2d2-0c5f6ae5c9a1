import { makeStyles } from '@mui/styles';
import { Theme } from '@mui/material/styles';

const useStyles = makeStyles((_theme: Theme) => ({
  container: {},
  title: {
    fontSize: 24,
    fontWeight: 800,
    fontFamily: "usual",
    color: "#272E4C",
  },
  label: {
    textTransform: "uppercase",
    color: "#7D8294",
    fontSize: 12,
    fontFamily: "usual",
  },
  value: {
    color: "#272E4C",
    fontWeight: 600,
    fontSize: 16,
    fontFamily: "usual",
  },
  body: {
    marginTop: '32px',
  },
  leftSide: {
    borderRight: "1px solid #E6E6E9",
    maxWidth: 200,
    fontFamily: "usual",
  },
  rightSide: {
    paddingLeft: 64,
    maxWidth: "calc(100% - 200px)",
    flexBasis: "calc(100% - 200px)",
    fontFamily: "usual",
  },
  avatar: {
    minWidth: 150,
    minHeight: 150,
  },
  itemLink: {
    color: "#272E4C",
    fontSize: 14,
    cursor: "pointer",
    "&:hover": {
      color: "#2C2E8E",
      textDecoration: "underline",
    },
  },
  activeItemLink: {
    color: "#2C2E8E",
    textDecoration: "underline",
    fontWeight: 800,
  },
  infoTitle: {
    color: "#BEC0C9",
    fontSize: 14,
    fontWeight: 800,
    paddingTop: '16px',
  },
  tabTitle: {
    color: "#9A9A9A",
    fontSize: 16,
    fontWeight: 800,
    paddingBottom: '24px',
    textTransform: "uppercase",
  },
  tabAppointmentLetterTitle: {
    color: "#9A9A9A",
    fontSize: 16,
    fontWeight: 800,
    textTransform: "uppercase",
  },
  tabSubtitle: {
    color: "#9A9A9A",
    fontSize: 14,
    fontWeight: 800,
    textTransform: "uppercase",
  },
  divider: {
    marginTop: '16px',
    marginBottom: "24px !important",
  },
  buttonGroup: {
    marginTop: '40px',
  },
  leftButton: {
    background: "#EF5350",
    color: "#FFFFFF",
    minWidth: 140,
    marginRight: '16px',
    "&:hover": {
      background: "#EF5350",
    },
  },
  leftButtonOutline: {
    background: "#FFFFFF",
    border: ".5px solid #3AB77D",
    borderRadius: "4px",
    color: "#3AB77D",
    minWidth: 140,
    marginRight: '16px',
    "&:hover": {
      background: "#FFFFFF",
    },
  },
  leftButtonRedOutline: {
    background: "#FFFFFF",
    border: ".5px solid #EF5350",
    borderRadius: "4px",
    color: "#EF5350",
    minWidth: 140,
    marginRight: '16px',
    "&:hover": {
      background: "#FFFFFF",
    },
  },
  rightButton: {
    background: "#3AB77D",
    color: "#FFFFFF",
    minWidth: 140,
    "&:hover": {
      background: "#3AB77D",
    },
  },
  document: {
    color: "#3AB77D",
    textDecoration: "underline",
  },
  uploadLink: {
    fontWeight: 800,
    color: "#3AB77D",
    textDecoration: "underline",
  },
  uploadIcon: {
    color: "#3AB77D",
    position: "relative",
    marginTop: 5,
    marginRight: '8px',
  },
  fakeLink: {
    display: "flex",
    justifyContent: "flex-start",
    cursor: "pointer",
    fontWeight: 600,
    textDecoration: "underline",
    opacity: 0.5,
  },
  documents: {
    display: "flex",
    "& svg": {
      cursor: "pointer",
      display: "none",
      marginLeft: '8px',
    },
    "&:hover": {
      "& svg": {
        display: "block",
      },
    },
  },
  modalButtonLeft: {
    minWidth: 100,
    backgroundColor: "transparent",
    color: "#3AB77D",
    borderColor: "#3AB77D",
    "&:hover": {
      backgroundColor: "transparent",
    },
  },
  modalButtonRight: {
    minWidth: 100,
    color: "#FFFFFF",
    backgroundColor: "#3AB77D",
    "&:hover": {
      backgroundColor: "#3AB77D",
    },
  },
  modalTitle: {
    color: "#272E4C",
    fontWeight: 800,
    fontSize: 18,
    fontFamily: "usual",
  },
  modalText: {
    color: "#272E4C",
    fontWeight: 400,
    fontSize: 14,
    fontFamily: "usual",
  },
  modalTextExpiry: {
    color: "#272E4C",
    fontWeight: 400,
    fontSize: 14,
    fontFamily: "usual",
    textAlign: "center",
  },
  inputBase: {
    height: "41px",
    padding: "3px",
    fontSize: "1rem",
    borderRadius: "5px",
    border: "1px solid #272E4C4D",
    outline: "none",
    boxShadow: "0 0 0px 1000px #ffffff inset",
  },
  "inputBase-multiline": {
    fontSize: "1rem",
    borderRadius: "5px",
    border: "1px solid #272E4C4D",
    outline: "none",
    boxShadow: "0 0 0px 1000px #ffffff inset",
  },
  confirmationList: {
    marginTop: "1rem",
    marginLeft: "2rem",
    "& li": {
      listStyleType: "disc",
    }
   },
}));

export { useStyles };
