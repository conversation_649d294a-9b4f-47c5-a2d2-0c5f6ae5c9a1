import React from 'react'
import {
    Grid
} from '@mui/material'
import {
    PageSubHeaderStyles
} from './styles'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';

interface BtnProps {
    label: string
    icon: IconDefinition
    action: any
}

interface IProps {
    title: string
    children?: React.ReactNode
    buttons?: BtnProps[]
}

export const PageSubHeader: React.FC<IProps> = (props: IProps)=> {
    const {
        title,
        buttons,
        children
    } = props

    const classes = PageSubHeaderStyles()

    return (
        <>
            <Grid container className={classes.container} alignItems="center">
                <Grid item xs={buttons ? 6 : 12}>
                    <h1 className="vni-font-bold vni-text-3xl">{title}</h1>
                </Grid>
            {
                buttons &&
                    <Grid item xs={6}>
                        <div className={classes.linkInlines}>
                            {
                                buttons.map((b,i) =>
                                    <span key={i} onClick={b.action} className={classes.linkStyledText} data-cy={i === 0 ? "view-generated-reports-link" : "generate-daily-transaction-report-link"}>
                                        <FontAwesomeIcon icon={b.icon} className={classes.linkIcon} />
                                        {b.label}
                                    </span>
                                )
                            }
                        </div>
                    </Grid>
            }
            </Grid>
            {
                children && <Grid container className={classes.container} alignItems="center">
                                {children}
                            </Grid> 
            }
        </>
    )
}