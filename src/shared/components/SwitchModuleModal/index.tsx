import React, {useEffect} from 'react'
import {
    Di<PERSON>,
    Grid,
    Button
} from '@mui/material'
import { makeStyles } from '@mui/styles';
import { Theme } from '@mui/material/styles';

const useStyles = makeStyles((theme: Theme) => ({
    main: {
        padding: theme.spacing(5),
        backgroundColor: '#FFFFFF',
        position: 'relative',
        minWidth: 300
    },
    title: {
        color: '#272E4C',
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 0
    },
    subtitle: {
        color: '#272E4C',
        fontSize: 12
    },
    bodyText: {
        marginTop: 20,
        marginBottom: 15,
        fontSize: 14,
        color: '#272E4C'
    },
    modules: {},
    module: {
        marginTop: 5,
        marginBottom: 5,
        fontSize: 14,
        color: '#272E4C',
        display: 'block'
    },
    selected: {
        color: '#3AB77D'
    },
    fillButton: {
        backgroundColor: '#3AB77D !important',
        color: '#FFFFFF',
        width: 125,
        height: 40
    },
    outlineButton: {
        backgroundColor: 'transparent !important',
        borderColor: '#3AB77D',
        color: '#3AB77D',
        width: 125,
        height: 40
    }
}))

interface IProps {
    open: boolean
    onClose: () => void;
    currentModule: string;
    modulesList: any
}

export const SwitchModuleModal: React.FC<IProps> = (props: IProps)=> {

    const classes = useStyles()

    // const moduleLists = useSelector((state: RootState) => state.login.modules);
    const [modules, setModules] = React.useState<any[]>([])
    const [selectedModule, setSelectedModule] = React.useState<string>('')

    useEffect(() => {
        setModules(props.modulesList)
    }, [props.modulesList])
    
    return(
        <Dialog
            maxWidth="sm"
            onClose={() => {props.onClose(); setSelectedModule('')}}
            open={props.open}>
                <div className={classes.main}>
                    <h2 className={classes.title}>Switch Modules</h2>
                    <span className={classes.subtitle}>You’re currently in {props.currentModule}</span>
                    <p className={classes.bodyText}>
                        You can access modules that you have access to.<br /> Choose one:
                    </p>

                    <div className={classes.modules}>
                        {
                            modules && modules.length > 0 ?
                                modules.map((module: any, index) => module.name !== props.currentModule ?
                                    <span
                                        data-cy={`switchmodule-${module.name}`}
                                        key={index}
                                        onClick={() => {
                                            setSelectedModule(module.dashboard_url)
                                        }}
                                        className={`${classes.module} ${selectedModule === module.dashboard_url && classes.selected}`}>
                                            {module.name}
                                    </span> : '')
                                : null
                        }
                    </div>

                    <Grid container spacing={2} style={{marginTop: 60}}>
                        <Grid item xs={6} style={{textAlign: 'center'}}>
                            <Button className={classes.outlineButton} data-cy="switchmodule-cancel-btn" onClick={() => {props.onClose(); setSelectedModule('')}} variant="outlined">Cancel</Button>
                        </Grid>
                        <Grid item xs={6} style={{textAlign: 'center'}}>
                            <Button
                                data-cy="switchmodule-switch-btn"
                                className={classes.fillButton}
                                disabled={(selectedModule === '')}
                                onClick={() => {
                                    window.location.replace(`../${selectedModule}`)
                                }}>Switch</Button>
                        </Grid>
                    </Grid>
                </div>
        </Dialog>
    );

}