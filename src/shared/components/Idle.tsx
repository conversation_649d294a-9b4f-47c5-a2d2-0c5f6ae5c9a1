import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { logout } from '../reducers/LoginSlice';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button } from '@mui/material';

interface IdleProps {
  url: string;
  timeout?: number;
}

const Idle: React.FC<IdleProps> = ({ url, timeout = 1000 }) => {
  const dispatch = useDispatch();
  const [openModal, setOpenModal] = useState(false);
  const [counter, setCounter] = useState(60);
  const idleTimeoutRef = useRef<NodeJS.Timeout | null>(null);


  const resetIdleTimer = useCallback(() => {
    if (idleTimeoutRef.current) {
      clearTimeout(idleTimeoutRef.current);
    }
    idleTimeoutRef.current = setTimeout(() => {
      setOpenModal(true);
      setCounter(60);
    }, timeout * 60 * 1000);
  }, [timeout]);

  const handleResetCounter = useCallback(() => {
    setOpenModal(false);
    setCounter(60);
    resetIdleTimer();
  }, [resetIdleTimer]);

  const handleClose = useCallback(() => {
    setOpenModal(false);
    dispatch(logout());
    window.location.href = url;
  }, [dispatch, url]);

  const handleActivity = useCallback(() => {
    if (!openModal) {
      resetIdleTimer();
    } else {
      handleResetCounter();
    }
  }, [openModal, resetIdleTimer, handleResetCounter]);

  useEffect(() => {
    const events = ['mousedown', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => document.addEventListener(event, handleActivity, true));
    resetIdleTimer();

    return () => {
      events.forEach(event => document.removeEventListener(event, handleActivity, true));
      if (idleTimeoutRef.current) {
        clearTimeout(idleTimeoutRef.current);
      }
    };
  }, [timeout, openModal, handleActivity, resetIdleTimer]);

  useEffect(() => {
    if (openModal && counter > 0) {
      const timer = setTimeout(() => setCounter(c => c - 1), 1000);
      if (counter === 1) {
        clearTimeout(timer);
        handleClose();
      }
      return () => clearTimeout(timer);
    }
  }, [openModal, counter, handleClose]);

  return (
    <Dialog open={openModal} maxWidth="xs">
      <DialogTitle>Session Expiring</DialogTitle>
      <DialogContent>
        <p>Your session is about to expire due to inactivity. You will be logged out in {counter} seconds.</p>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Logout</Button>
        <Button onClick={handleResetCounter} variant="contained">Continue Session</Button>
      </DialogActions>
    </Dialog>
  );
};

export default Idle;
