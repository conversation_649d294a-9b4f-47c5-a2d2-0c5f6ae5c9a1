import { CSSProperties } from 'react';
import { withStyles, makeStyles, createStyles } from '@mui/styles';
import { Theme } from '@mui/material/styles';
import Checkbox from '@mui/material/Checkbox';
import iconImage from '../../../assets/images/select-arrow.png'

const CustomCheckbox = withStyles({
    root: {
        backgroundColor: 'white',
        padding: '0px 0px 0px 0px',
        position: 'relative',
        left: '50px',
        bottom: '1.5px',
        height: '16px',
        width: '16px',
        '&$checked': {
            color: '#3AB77D'
        }
    },
    checked: {}
})(Checkbox);

const AutocompleteInputStyle = {
    width: '100%',
    height: '42px',
    fontSize: '15px',
    borderRadius: '8px',
    outline: 'none',
    backgroundColor: "transparent",
    backgroundImage: `url(${iconImage})`,
    backgroundPosition: "right 10px center",
    backgroundSize: "10px",
    backgroundRepeat: "no-repeat",
    paddingTop: '0px',
    paddingBottom: '0px',
    paddingLeft: '11px',
    paddingRight: '25px',
} as CSSProperties;

const AutocompleteWithoutArrow = {
    width: '100%',
    height: '42px',
    fontSize: '15px',
    borderRadius: '8px',
    outline: 'none',
    backgroundColor: "transparent",
    paddingTop: '0px',
    paddingBottom: '0px',
    paddingLeft: '11px',
    paddingRight: '25px',
} as CSSProperties;

const CustomOptionStyle = makeStyles((theme: Theme) =>
    createStyles({    
        option: {
            '&[aria-selected="true"]': {
            backgroundColor: 'transparent',
        },
        '&[data-focus="true"]': {
            backgroundColor: theme.palette.action.selected,
        },
        }
    }),
);

export {
    CustomCheckbox,
    AutocompleteInputStyle,
    AutocompleteWithoutArrow,
    CustomOptionStyle
}