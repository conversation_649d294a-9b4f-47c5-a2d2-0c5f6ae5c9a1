export const FrReportCols = [
  {
    name: "dtr_date",
    title: "Date",
    sortEnabled: false,
  },
  {
    name: "client_id",
    title: "Client ID",
    sortEnabled: false,
  },
  {
    name: "corporate_name",
    title: "Account Name",
    sortEnabled: false,
  },
  {
  	name: 'industryClass',
  	title: 'Industry Type',
        sortEnabled: false
  },
  {
    name: "expiryDate",
    title: "Expiry Date",
    sortEnabled: false,
  },
  {
    name: "status",
    title: "Status",
    sortEnabled: false,
  },
  {
    name: "status_updated_at",
    title: "Updated At",
    sortEnabled: false
  }
]

const columns = [
  {
    name: "dtr_date",
    title: "Date",
    sortEnabled: false,
  },
  {
    name: "client_id",
    title: "Client ID",
    sortEnabled: false,
  },
  {
    name: "corporate_name",
    title: "Registered Corporate Name",
    sortEnabled: false,
  },
  {
  	name: 'received_status',
  	title: 'Received',
        sortEnabled: false
  },
  {
    name: "validated_status",
    title: "Validated",
    sortEnabled: false,
  },
  {
    name: "approved_status",
    title: "Approved",
    sortEnabled: false,
  },
  {
    name: "disapproved",
    title: "Disapproved",
    sortEnabled: false,
  },
  // {
  //   name: "tax_number",
  //   title: "TIN Number",
  //   sortEnabled: false,
  // },
  {
    name: "reason_for_disapproval",
    title: "Reason For Disapproval",
    sortEnabled: false,
  },
  {
    name: "status",
    title: "Status",
    sortEnabled: false,
  },
];

const columnExtensions = [
  {
    columnName: "dtr_date",
    width: "120px",
  },
  {
    columnName: "client_id",
    width: "120px",
  },
  {
    columnName: "franchisee_name",
    width: "150px",
  },
  {
    columnName: "tax_number",
    width: "150px",
  },
  {
    columnName: "reason",
    width: "220px",
  },
  {
    columnName: "status",
    width: "150px",
  },
  {
    // columnName: 'remarks',
    width: "150px",
  },
];

export { columns, columnExtensions };
