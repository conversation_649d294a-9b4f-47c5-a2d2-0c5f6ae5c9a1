import React, { useEffect } from "react";
import { <PERSON>rid, <PERSON>, Typography, Button } from "@mui/material";
import { Modal } from "../Modal";
import { useHistory } from "react-router-dom";
import { TableComponent } from "../TableComponent";
import { HtmlToPDFRenderer } from "../HtmlToPdfRenderer";
import { PreviewStyles } from "./style";
import {
  fetchGeneratedReport,
  sendReport,
  setResponseObj,
  setSearchItems,
  setReportView,
} from "../../reducers/ReportSlice";
import { useDispatch, useSelector } from "react-redux";
import { setUser } from "../../reducers/LoginSlice";
import { RootState } from "../../reducers/rootReducer";
import { FrReportCols, columns, columnExtensions } from "./ViewPDFReport.const";
import { StatusHistory } from "../../models/Report";
import { Franchise } from "../../models/Franchise";
import Spinner from "../Spinner";
import moment from "moment";
import { getReasonRemarkForReport } from "../../../utils/DataHelper";
import { Document, Page, View } from "@react-pdf/renderer";

interface IProps {}
export const ViewPDFReport: React.FC<IProps> = (props: any)=> {
  const { match } = props;
  let { id } = match.params;
  const classes = PreviewStyles();
  const dispatch = useDispatch();
  const history = useHistory();
  const [isDTR, setIsDTR] = React.useState<boolean>(false);
  const [openModal, setOpenModal] = React.useState<any>(false);
  const [print, setPrint] = React.useState<any>(false);
  const [download, setDownload] = React.useState<any>(false);
  const token = useSelector((state: RootState) => state.login.token);
  const role = useSelector((state: RootState) => state.login.roleAPI);
  const reportView = useSelector((state: RootState) => state.report.reportView);
  const previewDates = useSelector(
    (state: RootState) => state.report.previewData
  );
  const reportList = useSelector((state: RootState) => state.report.reportList);
  const { DTRfrom, DTRto } = previewDates;
  let dateFrom = "";
  let dateTo = "";
  const {
    report,
    status_history,
    _id,
    generated_date_from,
    generated_date_to,
    franchises,
    requested_by_name,
    created_at,
    send_to,
    requested_by_id
  } = reportView;
  
  if (generated_date_from && generated_date_to) {
    dateFrom = moment(generated_date_from).format("MMMM D, YYYY");
    dateTo = moment(generated_date_to).format("MMMM D, YYYY");
  } else {
    dateFrom = moment(DTRfrom).format("MMMM D, YYYY");
    dateTo = moment(DTRto).format("MMMM D, YYYY");
  }

  // if (isDTR) {
  //   const currentDate = moment().format("MMMM D, YYYY") + " 4:00 PM";
  //   const yesterdayDate =
  //     moment().subtract(1, "day").format("MMMM D, YYYY") + " 4:01 PM";
  //   dateFrom = moment(yesterdayDate).format("MMMM D, YYYY hh:mm A");
  //   dateTo = moment(currentDate).format("MMMM D, YYYY hh:mm A");
  // }

  const [generator, setGenerator] = React.useState<any>();
  const [tableRows, setTableRows] = React.useState<any>([]);
  const responseObj = useSelector(
    (state: RootState) => state.report.responseObj
  );
  const [modalContent, setModalContent] = React.useState<any>({
    title: "",
    description: "",
    button: <></>,
  });
  const { title, description, button } = modalContent;
  const isRequesting = useSelector(
    (state: RootState) => state.franchise.isRequesting
  );
  const userRoles = useSelector((state: RootState) => state.login.userRoles);
  const { canPrintReport, canDownloadReport } = useSelector(
    (state: RootState) => state.login.userPermissions
  );
  const { isSalesUser } = userRoles;

  const [renderPDF, setRenderPDF] = React.useState<any>(false);

  // use to call the indexDB
  useEffect(() => {
    dispatch(setUser());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [setUser]);
  // use to call the view fetch
  useEffect(() => {
    if (token && role) {
      if (id) {
        dispatch(fetchGeneratedReport(id));
      }
      window.scrollTo(0, 0);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token, role]);

  const handleCloseModal = () => {
    setOpenModal(false);
  };

  const handleSendReport = () => {
    const pdfBlob =  localStorage.getItem('fileBlobData');
    const pdfName =  localStorage.getItem('fileNamePdf');
    const pdfDateRange =  localStorage.getItem('fileDateRange');
    const filteredUser = requested_by_name
    const filteredUserId = requested_by_id
    
    const pdfData = { filename: pdfName, filecontent: pdfBlob, pdfdaterange: pdfDateRange, filtereduser: filteredUser, filtereduserid: filteredUserId}
    console.log('file pdfName', pdfName)
    console.log('file pdfBlob', filteredUser)
    
    dispatch(sendReport(_id, requested_by_name ?? "", pdfData));
    localStorage.removeItem('fileBlobData');
    localStorage.removeItem('fileNamePdf');
    localStorage.removeItem('pdfDateRange');
    //setRenderPDF(false);
  };

  const handleOpenModal = () => {
    setOpenModal(true);
    dispatch(
      setResponseObj({
        title: "",
        description: "",
        error: false,
      })
    );
  };

  const handleClosePreview = () => {
    dispatch(setSearchItems({}));
    dispatch(setReportView({}));
    // history.push("/franchising/report/");
    history.goBack();
  };

  const handleIframeOnLoad = () => {
    let iframe: any;
    iframe = document.getElementById("exportthis");
    if (!canPrintReport) {
      try {
        if (iframe) {
          iframe.contentWindow.postMessage("print", window.location);
        }
      } catch (error) {
        console.log("PRINT Policy ERROR", error);
      }
    }
    if (!canDownloadReport) {
      try {
        if (iframe) {
          iframe.contentWindow.postMessage("download", window.location);
        }
      } catch (error) {
        console.log("DOWNLOAD Policy ERROR", error);
      }
    }
  };

  useEffect(() => {
    if (responseObj.title || responseObj.description) {
      setModalContent({
        title: responseObj.title,
        description: responseObj.description,
        button: (
          <Button
            className={classes.rightButton}
            data-cy="sendmodal-confirmation-okay-btn"
            onClick={handleCloseModal}
          >
            Okay
          </Button>
        ),
      });
    } else {
      setModalContent({
        title: "Send Report",
        description: `Are you sure you want to send report to ${
          requested_by_name ?? ""
        }?`,
        button: (
          <>
            <Button
              className={classes.leftButtonOutline}
              data-cy="sendmodal-no-btn"
              onClick={handleCloseModal}
            >
              No
            </Button>
            <Button
              className={classes.rightButton}
              data-cy="sendmodal-yes-btn"
              onClick={handleSendReport}
            >
              Yes
            </Button>
          </>
        ),
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [responseObj, requested_by_name]);

  useEffect(() => {
    if (report) {
      setIsDTR(report.includes("DTR") ? true : false);
    }
  }, [report]);
  /* comment this to test no records found */
  useEffect(() => {
    let tableContents: any = [];
    if (isDTR) {
      const daily = reportList.reduce(
        (acc, value: any) => acc.concat(value.franchises),
        []
      );
      // let transactionCount: any = {};
      if (daily && daily.length > 0) {
        tableContents = daily.map((content: Franchise) => {
          let {
            corporate_name,
            expiry_date,
            status,
            status_updated_at,
            client_id,
            applicant_name,
            // tax_number,
            reasons,
            remarks,
            // industry_class_id,
            submitted_at = ""
          } = content;
          let actualStatus = "";
          if (status) {
            actualStatus =
              status[0].toUpperCase() + status.slice(1).toLowerCase();
          }

          let { reason, remark } = getReasonRemarkForReport(
            status,
            reasons ?? [],
            remarks ?? [],
            userRoles,
            true
          );

          return {
            corporate_name,
            // industryClass: industry_class_id,
            expiryDate: moment(expiry_date).format("MM/DD/YYYY"),
            compareDate: moment(status_updated_at).format(
              "MMMM D, YYYY hh:mm A"
            ),
            dtr_date: moment(submitted_at ? submitted_at : status_updated_at).format("MM/DD/YYYY"),
            client_id: client_id ? client_id : "--",
            franchisee_name: applicant_name,
            // tax_number: tax_number,
            reason: reason,
            status: actualStatus,
            remarks: remark,
          };
        });

        tableContents = tableContents.filter((con: any) => {
          const dateDtrCompare = con.compareDate;
          if (
            moment(dateDtrCompare).isBetween(
              moment(dateFrom),
              moment(dateTo),
              undefined,
              "[)"
            )
          )
            return true;
          return false;
        });
      } else {
        tableContents = [];
      }
    } else if (franchises && franchises.length > 0) {
      tableContents = franchises.map((content: Franchise) => {
        
        let {
          corporate_name,
          industry_class_id,
          expiry_date,
          status,
          status_updated_at,
          client_id,
          applicant_name,
          tax_number,
          reasons,
          remarks,
          submitted_at = ""
        } = content;
        let actualStatus = "";
        if (status) {
          actualStatus =
          status[0].toUpperCase() + status.slice(1).toLowerCase();
        }
        
        let { reason } = getReasonRemarkForReport(
          status,
          reasons ?? [],
          remarks ?? [],
          userRoles,
          true
          );

        const stat = status_history?.filter(sh => sh.franchise._id === content._id)

        const received = stat.find(s => s.status === 'RECEIVED')
        const validated = stat.find(s => s.status === 'FOR_APPROVAL')
        const approved = stat.find(s => s.status === 'APPROVED')         
        const disapproved = stat.find(s => s.status === 'DISAPPROVED')

        return {
         corporate_name,
          industryClass: industry_class_id,
          expiryDate: moment(expiry_date).format("MM/DD/YYYY"),
          // dtr_date: moment(status_updated_at).format("MM/DD/YYYY"),
          dtr_date: moment(submitted_at ? submitted_at : status_updated_at).format("MM/DD/YYYY"),
          client_id: client_id ? client_id : "--",
          franchisee_name: applicant_name,
          tax_number: tax_number,
          reason: reason,
          status: actualStatus,
          status_updated_at: moment(status_updated_at).format("MM/DD/YYYY"),
          received_status: moment(received?.created_at).format("MM/DD/YYYY"),
          validated_status: moment(validated?.created_at).format("MM/DD/YYYY"),
          approved_status: moment(approved?.created_at).format("MM/DD/YYYY"),
          disapproved_status: moment(disapproved?.created_at).format("MM/DD/YYYY")
        };
      });
    } else if (status_history && status_history.length > 0) {
      tableContents = status_history.map((content: StatusHistory) => {
        let actualStatus: string;
        let { status, franchise, dtr_date } = content;
        let {
          corporate_name,
          industry_class_id,
          expiry_date,
          client_id,
          applicant_name,
          tax_number,
          reasons,
          remarks,
        } = franchise;
        switch (status) {
          case "RECEIVED":
            actualStatus = "Validation In-Process";
            break;
          case "VALIDATED":
            actualStatus = "Validation Complete";
            break;
          case "FOR_APPROVAL":
            actualStatus = "For Approval";
            break;
          case "FINAL_APPROVAL":
            actualStatus = "For Final Approval";
            break;
          case "APPROVAL_IN_PROCESS":
            actualStatus = "Approval In Process";
            break;
          case "RESUBMITTED_TO_SUPERVISOR":
            actualStatus = "Resubmitted To Supervisor";
            break;
          case "RETURNED_TO_VALIDATOR":
            actualStatus = "Returned";
            break;
          case "RETURNED":
            actualStatus = "Returned to Sales User";
            break;
          case null:
            actualStatus = "";
            break;
          default:
            actualStatus =
              status[0].toUpperCase() + status.slice(1).toLowerCase();
            break;
        }

        let { reason, remark } = getReasonRemarkForReport(
          status,
          reasons ?? [],
          remarks ?? [],
          userRoles,
          true
        );

        return {
          corporate_name,
          industryClass: industry_class_id,
          expiryDate: moment(expiry_date).format("MM/DD/YYYY"),
          dtr_date: moment(dtr_date).format("MM/DD/YYYY"),
          client_id: client_id ? client_id : "--",
          franchisee_name: applicant_name,
          tax_number: tax_number,
          reason: reason,
          status: actualStatus,
          remarks: remark,
        };
      });
    } else {
      tableContents = [];
    }
    console.log('report table contents', tableContents);
    setTableRows(tableContents);

    if(Object.keys(reportView).length !== 0) {
   
    setRenderPDF(
        Object.keys(reportView).length > 0 && reportView.constructor === Object
      );

      let generatedby: any =  reportView.roles;
      
      setGenerator(generatedby && generatedby[0] && generatedby[0].name);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [reportView]);

  const disableSend = send_to ? true : !requested_by_name;
  let sampleCount: any = {};

  tableRows.map((content: any) => {
    if (content.status in sampleCount) {
      return sampleCount[content.status]++;
    } else {
      return sampleCount[content.status] = 1;
    }
  });

  return (
    <>
      <Grid
        container
        direction="row"
        style={{ backgroundColor: "#848484" }}
        justifyContent="flex-start"
      >
        <Spinner />
        <Grid className="vni-flex-grow">
          <Grid className="vni-sticky vni-top-0 vni-z-10">
            <Grid className="vni-bg-lucky vni-p-4 vni-text-white d-flex vni-justify-between">
              <Grid style={{ display: "flex", alignItems: "center" }}>
                <Box
                  data-cy="close-button"
                  component="span"
                  className="vni-text-5xl line-height-06 vni-font-hairline vni-pr-10 vni-cursor-pointer"
                  onClick={handleClosePreview}
                >
                  ×
                </Box>{" "}
                <Box
                  component="span"
                  className="text-24"
                  style={{ marginTop: 2 }}
                >
                  Preview Sheet
                </Box>
              </Grid>
              <Grid className="vni-flex vni-items-center">
                {requested_by_name && !isSalesUser ? (
                  <Typography
                    className="vni-mr-5"
                    style={{ fontSize: 13, marginRight: 10 }}
                  >
                    Sales User: {requested_by_name}
                  </Typography>
                ) : null}
                {!isSalesUser ? (
                  <Button
                    className={classes.primaryButton}
                    onClick={handleOpenModal}
                    disabled={disableSend}
                    data-cy="send-report-btn"
                  >
                    Send Report
                  </Button>
                ) : null}
              </Grid>
            </Grid>
            {!isRequesting && (
              <Modal
                fullWidth={false}
                maxWidth="md"
                open={openModal}
                onClose={handleCloseModal}
              >
                <Grid>
                  <Typography variant="h5" className={classes.modalTitle}>
                    {title}
                  </Typography>
                  <Typography className={classes.modalMessage}>
                    {description}
                  </Typography>
                  <Grid className="btn-group" style={{ textAlign: "center" }}>
                    {button}
                  </Grid>
                </Grid>
              </Modal>
            )}
          </Grid>
          <iframe
            onLoad={handleIframeOnLoad}
            className="main mark-bg-gray mark-mt-minus-61"
            data-cy="preview_data"
            src=""
            id="exportthis"
            style={{ height: "calc(100vh - 80px)" }}
            title="previewFrame"
          ></iframe>
          <div id="preview_data" style={{ display: "none" }}>
            <Document>
              <Page size="A4" style={{ }}>
                <View>
                  {(tableRows.length > 0 && (
                    <TableComponent
                      rows={tableRows}
                      columns={isDTR ? columns : FrReportCols}
                      columnExtensions={columnExtensions}
                      enablePaging={false}
                      customNoDataMessage="No Records Found"
                    />
                  )) || (
                    <div className="vni-text-center vni-italic vni-underline vni-text-gray-700 vni-py-40">
                      No Records Found
                    </div>
                  )}
                  {sampleCount && Object.keys(sampleCount).map((value: any) => (
                    <div
                      className="vni-text-center vni-italic vni-underline vni-text-gray-700 vni-py-40"
                      style={{ fontSize: 12, marginRight: 10 }}
                      key={`${(Math.random() + 1).toString(36).substring(7)}-${sampleCount[value]}-val`}
                    >
                      <b>{value}:</b> {sampleCount[value]}
                    </div>
                  ))}
                  {tableRows.length > 0 && (
                    <Typography variant="subtitle1" className={classes.modalMessage}>
                      Total : {tableRows.length}
                    </Typography>
                  )}
                </View>

              </Page>
            </Document>
          </div>
        </Grid>
      </Grid>
      {renderPDF === true && (
        <HtmlToPDFRenderer
          clientID={"clientID"}
          clientName={"clientName"}
          fileType={"pdf"}
          fileName={report}
          dateFrom={dateFrom}
          dateTo={dateTo}
          generator={generator}
          reportGeneratedAt={created_at}
          print={print}
          download={download}
          onStartDownload={() => {
            console.log("downloading is done....");
            setDownload(false);
          }}
          onStartPrint={() => {
            console.log("printing is done....");
            setPrint(false);
          }}
          onError={() => {
            console.log("error....");
            setPrint(false);
            setDownload(false);
          }}
          user={userRoles}
          isDTR={isDTR}
        />
      )}
    </>
  );
};
