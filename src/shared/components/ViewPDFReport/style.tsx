import { makeStyles } from '@mui/styles';
import { Theme } from '@mui/material/styles';

const PreviewStyles = makeStyles((_theme: Theme) => ({
    primaryButton: {
        fontFamily: 'usual, sans-serif',
        textTransform: 'none',
        fontWeight: 600,
        padding: '12px 43px',
        textAlign: 'center',
        fontSize: 14,
        backgroundColor: "#3AB77D",
        border: 'none',
        color: "white",
        minWidth: 150,
        outline: "none",
        cursor: 'pointer',
        '&:hover': {
            background: '#3AB77D'
        }
    },
    modalTitle: {
        fontFamily: 'usual, sans-serif',
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 20
    },
    modalMessage: {
        fontFamily: 'usual, sans-serif',
        fontSize: 14,
        marginBottom: 20
    },
    leftButtonOutline: {
        fontFamily: 'usual, sans-serif',
        textTransform: 'none',
        fontWeight: 600,
        padding: '12px 43px',
        textAlign: 'center',
        fontSize: 14,
        background: '#FFFFFF',
        border: '.5px solid #3AB77D',
        borderRadius: '4px',
        color: '#3AB77D',
        marginRight: '16px',
        '&:hover': {
            background: '#FFFFFF'
        }
    },
    rightButton: {
        fontFamily: 'usual, sans-serif',
        textTransform: 'none',
        fontWeight: 600,
        padding: '12px 43px',
        textAlign: 'center',
        fontSize: 14,
        backgroundColor: "#3AB77D",
        border: 'none',
        color: "white",
        outline: "none",
        cursor: 'pointer',
        '&:hover': {
            background: '#3AB77D'
        }
    }
}))

export {
    PreviewStyles
}