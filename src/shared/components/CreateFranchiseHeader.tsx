import React, { useState } from 'react';
import { NavLink, useHistory } from "react-router-dom";
import { RootState } from "../../shared/reducers/rootReducer"
import { useSelector } from 'react-redux';
import { Dialog, DialogTitle, DialogContent, DialogActions } from "@mui/material";


const CreateFranchiseHeader: React.FC = () => {
    let history = useHistory();
    const tab = useSelector((state: RootState) => state.franchise.tab);
    const requestAction = useSelector((state: RootState) => state.franchise.isEdit);
    const newFranchise = useSelector((state: RootState) => state.franchise.new);
    const {canAddFR, canEditFR} = useSelector((state: RootState) => state.login.userPermissions);



    let dashboard = `/franchising/` ;
    const [openModal, setOpenModal] = useState(false);
    const [navURL, setClickNav] = useState("");
    const isEdit= useSelector((state: RootState) => state.franchise.isEdit);
    const isNewInputs = useSelector((state: RootState) => state.franchise.isNewInputs);
    const fileUploaded = useSelector((state:RootState) => state.franchise.uploadedFile);
    const {canUploadDocs} = useSelector((state: RootState) => state.login.userPermissions);
    let isObjectEmpty = !Object.keys(fileUploaded).length;
    
    const handleNavClick = (e: any,url: string) => {
        if(isNewInputs || !isObjectEmpty){
        e.preventDefault();
        setOpenModal(true);
        setClickNav(url)
        }
    };
    const handleModalConfirmed = () => {
        setOpenModal(false);
        history.push(navURL)
    };
    
    const handleClose = (e: any) => {
        e.preventDefault()
        setOpenModal(false);
    };

    return (
        <div className="create-franchise-request-head">
            {(canAddFR || canEditFR) &&
                <>
                    <div>
                        <span className="breadcrumbs">
                            <NavLink 
                            data-cy="dashboard"
                            to={dashboard}
                            onClick={(e) => handleNavClick(e,dashboard)}
                            >
                                DASHBOARD
                            </NavLink>/ 
                            <strong>{(requestAction)? `EDIT FRANCHISE REQUEST`: `CREATE FRANCHISE REQUEST`}</strong>
                        </span>
                        <h1 className="vni-font-bold vni-text-2xl vni-uppercase">{(requestAction)? `EDIT FRANCHISE REQUEST`: `CREATE FRANCHISE REQUEST`}</h1>
                    </div>
                    <div className="progress-bar">
                        <ul>
                            <li className={tab === 0 ?"current":"done"}>{(requestAction)? `Edit Request`: `Create Request`}</li>
                            {canUploadDocs && <li className={tab === 1 ?'vni-text-center current':(tab === 2 ? "vni-text-center done":"vni-text-center")}>Upload Requirements</li>}
                            <li style={{minWidth:canUploadDocs?"150px":"98px"}} className={tab === 2 || (!canUploadDocs && tab===1) ?'vni-text-right current':"vni-text-right"}>{!requestAction? "Submit": newFranchise.status === "SUBMITTED" || newFranchise.status === "RESUBMITTED" ? "Update" : "Submit"}</li>
                        </ul>
                    </div>
                </>
            }
            <Dialog 
                id="unsaved-changes-modal"
                open={openModal}
                onClose={handleClose}
                className="vni-m-auto"
            >
                <DialogTitle className="title vni-px-5 vni-pt-3 vni-mb-8">
                    <h2 className="vni-px-5" style={{fontWeight: "bold"}}>Unsaved Changes</h2>
                </DialogTitle>
                <DialogContent>
                <p className="vni-px-5 vni-mb-8">{isEdit ? "Are you sure you want to cancel editing Franchise Request? This will not be saved." : "Are you sure you want to leave this page without saving? All unsaved changes will be lost."}</p>
                </DialogContent>
                <DialogActions className="vni-flex d-flex-center">
                <button data-cy={isEdit ? "editpage-breadcrumbs-unsaved-no-btn" : "createpage-breadcrumbs-unsaved-no-btn"} className="CustomPrimaryOulineButton vni-mr-5" onClick={handleClose}>No</button>
                <button data-cy={isEdit ? "editpage-breadcrumbs-unsaved-yes-btn" : "createpage-breadcrumbs-unsaved-yes-btn"} className="CustomPrimaryButton" onClick={handleModalConfirmed}>Yes</button>
                </DialogActions>
            </Dialog>
        </div>
    );
}; 

export default CreateFranchiseHeader;