import { createSlice } from "@reduxjs/toolkit";
import { AppThunk, AppDispatch } from "../../store";
import { RootState } from "./rootReducer";

import {
  getFranchiseRequestLogs,
  viewGeneratedReport,
  getGeneratedReport,
  postGeneratedReport,
  sendGeneratedReport,
  viewEncoderReport,
  postGenerateFranchisingReport,
  postGeneratedReportBySalesHead
} from "../../services/ReportService";

import { setIsRequesting } from "./FranchiseSlice";
import { ViewGeneratedReport, searchCriteria } from "../models/Report";
import moment from "moment";
import { endOfToday } from "date-fns/esm";
import { setSessionExpired } from "./LoginSlice";

export const reportSlice = createSlice({
  name: "report",
  initialState: {
    transactionLogs: [],
    reportList: [],
    encoderReportList: [],
    reportView: {} as ViewGeneratedReport,
    responseObj: {
      title: "",
      description: "",
      error: false,
    },
    isReportComplete: false,
    error: false,
    previewData: {
      isDTR: false,
      isGDTR: false,
      DTRfrom: "",
      DTRto: "",
      GTRfrom: "",
      GTRto: "",
    },
    filteredSalesUser: "",
    search: {} as searchCriteria,
  },
  reducers: {
    resetReportState: (state) => {
      state.error = false;
      state.isReportComplete = false;
      state.responseObj = {
        title: "",
        description: "",
        error: false,
      };
    },
    resetPreviewData: (state) => {
      state.previewData = {
        isDTR: false,
        isGDTR: false,
        DTRfrom: "",
        DTRto: "",
        GTRfrom: "",
        GTRto: "",
      };
    },
    getTransactionLogs: (state, action) => {
      state.transactionLogs = action.payload;
      state.error = false;
    },
    setReportList: (state, action) => {
      state.reportList = action.payload;
      state.error = false;
    },
    setReportView: (state, action) => {
      state.reportView = action.payload;
      state.error = false;
    },
    setGenerateReportStatus: (state, action) => {
      state.isReportComplete = action.payload.status;
      state.error = false;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    setResponseObj: (state, action) => {
      state.responseObj.title = action.payload.title;
      state.responseObj.description = action.payload.description;
      state.responseObj.error = action.payload.error;
    },
    setpreviewData: (state, action) => {
      state.previewData.isDTR = action.payload.isDTR;
      state.previewData.isGDTR = action.payload.isGDTR;
      state.previewData.DTRfrom = action.payload.DTRfrom;
      state.previewData.DTRto = action.payload.DTRto;
      state.previewData.GTRfrom = action.payload.GTRfrom;
      state.previewData.GTRto = action.payload.GTRto;
    },
    setSearchItems: (state, action) => {
      state.search = action.payload;
    },
    setDTR: (state) => {
      state.previewData.isGDTR = false;
      state.previewData.isDTR = true;
    },
  },
});

export const {
  resetReportState,
  resetPreviewData,
  getTransactionLogs,
  setReportList,
  setReportView,
  setGenerateReportStatus,
  setpreviewData,
  setSearchItems,
  setDTR,
  setError,
  setResponseObj,
} = reportSlice.actions;

/**
 * Handle API Call for Get : Fetch Report
 * <AUTHOR>
 * @url `api/reports/${role}
 */
export const fetchFranchiseRequestLogs =
  // @ts-ignore
  (reportRole?: string): AppThunk => async (dispatch: AppDispatch, getState: () => RootState) => {
    dispatch(setIsRequesting(true));
    let token = getState().login.token;
    let role = getState().login.roleAPI;
    let { isSalesUser } = getState().login.userRoles;
    const response = getFranchiseRequestLogs(reportRole, token);
    await response
      .then((a: any) => {
        if (a === undefined) {
          console.log("error");
          dispatch(setError(true));
        } else {
          if (reportRole ? reportRole === "encoder" : isSalesUser) {
            dispatch(setReportList(a.data));
          } else {
            console.log('TRANSACTIONN LOGGGSS')
            dispatch(getTransactionLogs(a.data));
            dispatch(setIsRequesting(false));
          }
        }
      })
      .catch(function (e) {
        console.log("catch", e);
        if (e.response) {
          switch (e.response.status) {
            case 500:
              console.log("Oops, something went wrong.");
              dispatch(setIsRequesting(false));
              dispatch(setError(true));
              break;
            case 401:
              dispatch(setSessionExpired(true))
              break;
          }
        }
      })
      .finally(() => dispatch(setIsRequesting(false)));
  };

/**
 * Handle API Call for Post : Generate Transaction Report
 * @param dateFrom
 * @param dateTo
 * <AUTHOR>
 * @url `api/reports/${role}/generate`
 */
export const generateDailyReport =
  (dateFrom: string, dateTo: string, role: string): AppThunk =>
  // @ts-ignore
  async (dispatch: AppDispatch, getState: () => RootState) => {
    let token = getState().login.token;
    // let role = getState().login.roleAPI;
    let filter = getState().report.search;
    let param: any = {};
    
    const previousDay = moment(dateFrom).set({ hour: 16, minute: 0, second: 0, millisecond: 0 }).subtract(1, 'day');
    const startTime = moment(previousDay).set({ hour: 16, minute: 1, second: 0, millisecond: 0 });
    const endTime = moment(dateTo).set({ hour: 16, minute: 0, second: 0, millisecond: 0 });

    console.log("EME Start Time:", startTime.format()); // previous day 4:01 PM
    console.log("EME End Time:", endTime.format()); // same day 4:00 PM

    if (
      moment(endTime) >= moment(startTime) &&
      moment(startTime) <= moment(endOfToday()) &&
      moment(endTime) <= moment(endOfToday())
    ) {
      param = {
        date_from: moment(startTime).format("MM/DD/YYYY"),
        date_to: moment(endTime).format("MM/DD/YYYY"),
        filter: filter,
      };
    }

    dispatch(setIsRequesting(true));
    // const response = postGeneratedReport(role, param, token).then((res) => {
    //   if (res.status === 200) {
    //     dispatch(setIsRequesting(false));
    //     dispatch(setReportView(res.data))
    //     //@ts-ignore
    //     dispatch(fetchFranchiseRequestLogs());
    //   }
    // }).catch(e => {
    //   dispatch(setReportView({ status_history: [], franchises: [] }));
    //   dispatch(setGenerateReportStatus({ status: false }));
    //   dispatch(
    //     setpreviewData({
    //       isDTR: true,
    //       isGDTR: false,
    //       GTRfrom: "",
    //       GTRto: "",
    //     })
    //     );
    //   dispatch(setIsRequesting(false));
    // });
    let response;
    try {
      response = await postGeneratedReport(role, param, token);
      if (response.status === 200) {
        dispatch(setIsRequesting(false));
        dispatch(setReportView(response.data));
        //@ts-ignore
        dispatch(fetchFranchiseRequestLogs(role));
      };
    } catch (e) {
      dispatch(setReportView({ status_history: [], franchises: [] }));
      dispatch(setGenerateReportStatus({ status: false }));
      dispatch(
        setpreviewData({
          isDTR: true,
          isGDTR: false,
          GTRfrom: "",
          GTRto: "",
        })
        );
      dispatch(setIsRequesting(false));
    }

    return response;
    // return await response;
  };


/**
 * Handle API Call for Post : Generate Franchise Report
 * @param dateFrom
 * @param dateTo
 * @url `/franchise/reports/${role}/generate/Franchising-report`
 */
export const generateFranchiseReport =
  (dateFrom: Date, dateTo: Date, role: string): AppThunk =>
  // @ts-ignore
  async (dispatch: AppDispatch, getState: () => RootState) => {
    let token = getState().login.token;
    // let roles = getState().login.roleAPI;
    let param = {
      date_from: moment(dateFrom).format("MM/DD/YYYY"),
      date_to: moment(dateTo).format("MM/DD/YYYY"),
    };

    dispatch(setIsRequesting(true));
    const response = postGenerateFranchisingReport(role, param, token);
    return await response.then((res) => {
      if (res.status === 200) {
        dispatch(setIsRequesting(false));
        //@ts-ignore
        dispatch(fetchFranchiseRequestLogs(role));
      }
    });
  };

/**
 * Handle API Call for GET : View Generated Daily Transaction Report List
 * <AUTHOR>
 * @url `/franchise/reports/${role}/generate`
 */
export const fetchGeneratedReports =
  // @ts-ignore
  (): AppThunk => async (dispatch: AppDispatch, getState: () => RootState) => {
    let token = getState().login.token;
    let role = getState().login.roleAPI;
    dispatch(setIsRequesting(true));
    const promise = getGeneratedReport(role, token);
    promise
      .then((a) => {
        dispatch(setIsRequesting(false));
        if (a) {
          dispatch(setReportList(a.data));
        }
      })
      .catch((e) => {
        dispatch(setIsRequesting(false));
        if (e) {
          dispatch(setError(true));
        }
      });
  };

/**
 * Handle API Call for GET : View Specific Daily Transaction Report - Preview
 * @param _id generated report id
 * @action GET
 * <AUTHOR>
 * @url `/franchise/reports/${role}/generate/${id}`
 */
export const fetchGeneratedReport =
  (_id: string): AppThunk =>
  // @ts-ignore
  async (dispatch: AppDispatch, getState: () => RootState) => {
    let token = getState().login.token;
    let role = getState().login.roleAPI;
    let { isSalesUser, isSpecialist, isSupervisor, isManager } = getState().login.userRoles;
    let isMultipleRole = isSalesUser && isSpecialist && isSupervisor && isManager;
    dispatch(setIsRequesting(true));
    const promise = isSalesUser && !isMultipleRole
      ? viewEncoderReport(_id, role, token)
      : viewGeneratedReport(_id, role, token);
    promise
      .then((a) => {
        dispatch(setIsRequesting(false));
        if (a) {
          dispatch(setReportView(a.data));
        }
      })
      .catch((e) => {
        dispatch(setIsRequesting(false));
        if (e) {
          dispatch(setError(true));
        }
      });
  };

/**
 * Handle API Call for Send Report - Preview
 * @param _id generated report id
 * @param user_id user id
 * <AUTHOR>
 * @action PATCH
 * @url `/franchise/reports/${role}/generate/${id}/send`
 * @body {"user_id": string;}
 */
export const sendReport =
  (_id: string, fullName: string, formData: any): AppThunk =>
  // @ts-ignore
  async (dispatch: AppDispatch, getState: () => RootState) => {
    let token = getState().login.token;
    let role = getState().login.roleAPI;
    dispatch(setIsRequesting(true));
    const promise = sendGeneratedReport(_id, role, formData, token );
    promise
      .then((a) => {
        dispatch(setIsRequesting(false));
        dispatch(
          setResponseObj({
            title: "Report Sent",
            description: `Report has been sent to ${fullName}`,
            error: false,
          })
        );
        dispatch(setReportView(a.data));
      })
      .catch((e) => {
        dispatch(setIsRequesting(false));
        dispatch(
          setResponseObj({
            title: "Unable to Send Report",
            description: "Oops, something went wrong.",
            error: true,
          })
        );
      });
  };


/**
 * Handle API Call for Post : Generate Franchise Report
 * @param dateFrom
 * @param dateTo
 * @url `/franchise/reports/${role}/generate/Franchising-report`
 */
export const fetchFranchiseReportsBySalesHead =
(memberIds: string[]): AppThunk =>
// @ts-ignore
async (dispatch: AppDispatch, getState: () => RootState) => {
  let token = getState().login.token;
  let roles = getState().login.roleAPI;

  dispatch(setIsRequesting(true));
  const response = postGeneratedReportBySalesHead(roles, { memberIds: memberIds}, token);
  return await response.then((res) => {
    if (res.status === 200) {
      dispatch(setIsRequesting(false));
      dispatch(setReportList(res.data));
      //@ts-ignore
      dispatch(fetchFranchiseRequestLogs());
    }
  });
};

export default reportSlice.reducer;
