import { createSlice } from "@reduxjs/toolkit";
import {
  getUserData,
  updateUserPassword,
  updateProfAvatar
} from "../../services/UserService";
import { RootState } from "./rootReducer";
import { AppThunk, AppDispatch } from "../../store";
import { IUserRole } from "../models/User";
import { getIndexDb, logoutService, authService } from "../../utils/CoreApi";
import {setIsRequesting} from "./FranchiseSlice"

import { iProfile } from "../models/User";
import { CREATE_FR, VIEW_DASHBOARD, SEARCH_FR, EDIT_FR, UPLOAD_DOCS, SUBMIT_FR, VIEW_FR, CANCEL_FR, RETURN_FR, VALIDATE_FR, APPROVE_FR, DISAPPROVE_FR, GENERATE_REPORT, VIEW_REPORT, DOWNLOAD_REPORT, PRINT_REPORT, ADD_SETTING, EDIT_SETTING, ARCHIVE_SETTING, VIEW_BR_AGT, ADD_BR_AGT, EDIT_BR_AGT, DEACTIVATE_BR_AGT, } from "../../utils/Environment";
import { hasPermission } from "../../utils/RBACHelper";
import { decryptCredentials, fieldsToEncrypt } from "../../utils/LoginService";

export const slice = createSlice({
  name: "login",
  initialState: {
    login: {},
    auth: {
      isAuthenticated: false,
      user: {},
      token: localStorage.getItem("token"),
      loading: true,
      sessionExpired: false,
    },
    userRoles: {
      isSalesUser: false,
      isSpecialist: false,
      isSupervisor: false,
      isManager: false,
      isMaintainer:false,
      isAbtMaintainer:false,
      isSalesHead: false,
    } as IUserRole,
    userPermissions:{
      canViewDashboard:false, // Done. Need Further Test. ***Done retest***
      canSendEmail:false, // Pending : Backend Implementation.
      canAddFR:false, // Done. Need Further Test. 
      canEditFR:false, // Done. Need Further Test.
      canViewFR:false, // Done. Need Further Test. ***Done retest***
      canSubmitFR:false, // Done. Need Further Test. ***Done retest***
      canSearchFR:false, // Done. Need Further Test.  
      canCancelFR:false, // Done. Need Further Test. ***Done retest***
      canReturnFR:false, // Done. Need Further Test.
      canValidateFR:false, // Done. Need Further Test.
      canApproveFR:false, // Done. Need Further Test.
      canDisapproveFR:false, // Done. Need Further Test.
      canUploadDocs:false, // Done. Need Further Test.
      canGenerateReport:false, // Done. Need Further Test. ***Done retest***
      canViewReport:false, // Done. Need Further Test. ***Done retest***
      canDownloadReport:false, // Done. Need Further Test. ***Done retest***
      canPrintReport:false, // Done. Need Further Test. ***Done retest***
      canAddSetting:false, // Done. Need Further Test.
      canEditSetting:false, // Done. Need Further Test.
      canArchiveSetting:false, // Done. Need Further Test.
      canViewBrokerAgent:false,
      canAddBrokerAgent:false,
      canEditBrokerAgent:false,
      canDeactivateBrokerAgent:false,
    },
    group_name: "" ,
    token: "",
    user_id: "",
    main_role: "",
    role: [],
    rbac: [],
    firstname: "",
    middlename: "",
    lastname: "",
    suffix: "",
    fullname: "",
    username: "",
    roleAPI: "",
    main_module:"",
    modules: [] as any,
    profile: {} as iProfile,
    error: false,
    pmaker_uid:"",  //PMAKER ID
  },
  reducers: {
    userLoginFail: (state) => {
      localStorage.removeItem("token");
    },
    setIndexDBToken: (state, action) => {
      state.token = action.payload;
    },
    setIndexDBUser_Id: (state, action) => {
      state.user_id = action.payload;
    },
    setIndexDBPMaker_Id: (state, action) => {  //PMAKER ID
      state.pmaker_uid = action.payload;
    },

    setIndexDBMainRole: (state, action) => {
      if (action.payload) {
        state.main_role = action.payload;
        switch (action.payload) {
          case "FRF_ENCODER":
            state.userRoles.isSalesUser = true;
            state.roleAPI = "encoder";
            break;
          case "FRF_VALIDATOR":
            state.userRoles.isSpecialist = true;
            state.roleAPI = "validator";
            break;
          case "FRF_INITIAL_APPROVER":
            state.userRoles.isSupervisor = true;
            state.roleAPI = "supervisor";
            break;
          case "FRF_FINAL_APPROVER":
            state.userRoles.isManager = true;
            state.roleAPI = "manager";
            break;
          case "FRF_SALES_HEAD":
            state.userRoles.isSalesHead = true;
            state.roleAPI = "sales_head";
            break;
  
          default:
            break;
        }
      }
    },
    setIndexDBRole: (state, action) => {
      if (action.payload) {
        state.role = action.payload;
        action.payload.forEach((element: { role_id: any; name: any }) => {
          switch (element.name) {
            case "FRF_ENCODER":
              state.userRoles.isSalesUser = true;
              state.roleAPI = "encoder";
              break;
            case "FRF_VALIDATOR":
              state.userRoles.isSpecialist = true;
              state.roleAPI = "validator";
              break;
            case "FRF_INITIAL_APPROVER":
              state.userRoles.isSupervisor = true;
              state.roleAPI = "supervisor";
              break;
            case "FRF_FINAL_APPROVER":
              state.userRoles.isManager = true;
              state.roleAPI = "manager";
              break;
            case "FRF_TABLE_MAINTAINER":
              state.userRoles.isMaintainer = true;
              break;
            case "FRF_ABT_MAINTAINER":
              state.userRoles.isAbtMaintainer = true;
              state.roleAPI = "abt_maintainer";
              break;
            case "FRF_SALES_HEAD": 
              state.userRoles.isSalesHead = true;
              state.roleAPI = "sales_head";
              break;

            default:
              break;
          }
        });
      }
    },
    setIndexDBFirstName: (state, action) => {
      state.firstname = action.payload;
    },
    setIndexDBMiddleName: (state, action) => {
      state.middlename = action.payload;
    },
    setIndexDBLastName: (state, action) => {
      state.lastname = action.payload;
    },
    setIndexDBSuffix: (state, action) => {
      state.suffix = action.payload;
    },
    setIndexDBFullName: (state, action) => {
      state.fullname = action.payload;
    },
    setIndexDBUserName: (state, action) => {
      state.username = action.payload;
    },
    setIndexDBMainModule: (state, action) => {
      state.main_module = action.payload;
    },
    setIndexDBGroupName: (state, action) => {
      state.group_name = action.payload;
    },
    setIndexDBRBAC: (state, action) => {
      state.rbac = action.payload;
      console.log('RBAC PAYLOAD',action.payload)
      console.log('RBAC POLICY ID',action.payload.map((rbac: any) =>{ return {module:rbac.module,policy_id:rbac.policy_id};}))
      state.userPermissions.canViewDashboard = hasPermission(action.payload,VIEW_DASHBOARD);
      state.userPermissions.canAddFR = hasPermission(action.payload,CREATE_FR);
      state.userPermissions.canEditFR = hasPermission(action.payload,EDIT_FR);
      state.userPermissions.canViewFR = hasPermission(action.payload,VIEW_FR);
      state.userPermissions.canSubmitFR = hasPermission(action.payload,SUBMIT_FR);
      state.userPermissions.canSearchFR = hasPermission(action.payload,SEARCH_FR);
      state.userPermissions.canCancelFR = hasPermission(action.payload,CANCEL_FR);
      state.userPermissions.canReturnFR = hasPermission(action.payload,RETURN_FR);
      state.userPermissions.canValidateFR = hasPermission(action.payload,VALIDATE_FR);
      state.userPermissions.canApproveFR = hasPermission(action.payload,APPROVE_FR);
      state.userPermissions.canDisapproveFR = hasPermission(action.payload,DISAPPROVE_FR);
      state.userPermissions.canUploadDocs = hasPermission(action.payload,UPLOAD_DOCS);
      state.userPermissions.canGenerateReport = hasPermission(action.payload,GENERATE_REPORT);
      state.userPermissions.canViewReport = hasPermission(action.payload,VIEW_REPORT);
      state.userPermissions.canDownloadReport = hasPermission(action.payload,DOWNLOAD_REPORT);
      state.userPermissions.canPrintReport = hasPermission(action.payload,PRINT_REPORT);
      state.userPermissions.canAddSetting = hasPermission(action.payload,ADD_SETTING);
      state.userPermissions.canEditSetting = hasPermission(action.payload,EDIT_SETTING);
      state.userPermissions.canArchiveSetting = hasPermission(action.payload,ARCHIVE_SETTING);
      state.userPermissions.canViewBrokerAgent = hasPermission(action.payload,VIEW_BR_AGT);
      state.userPermissions.canAddBrokerAgent = hasPermission(action.payload,ADD_BR_AGT);
      state.userPermissions.canEditBrokerAgent = hasPermission(action.payload,EDIT_BR_AGT);
      state.userPermissions.canDeactivateBrokerAgent = hasPermission(action.payload,DEACTIVATE_BR_AGT);
    },
    setModules: (state, action) => {
      state.modules = action.payload;
    },
    setProfile: (state, action) => {
      state.profile = action.payload;
    },
    setError: (state, action) =>{
      state.error = action.payload;
    },
    setSessionExpired: (state, action) => {
      state.auth.sessionExpired = action.payload
    }
  },
});

export const {
  userLoginFail,
  setIndexDBToken,
  setIndexDBUser_Id,
  setIndexDBPMaker_Id, //PMAKER ID
  setIndexDBMainRole,
  setIndexDBRole,
  setIndexDBFirstName,
  setIndexDBMiddleName,
  setIndexDBLastName,
  setIndexDBSuffix,
  setIndexDBFullName,
  setIndexDBUserName,
  setIndexDBMainModule,
  setIndexDBRBAC,
  setModules,
  setProfile,
  setError,
  setSessionExpired,
  setIndexDBGroupName
} = slice.actions;

export const setUser = (): AppThunk => async (dispatch) => {
  try {
    getIndexDb("user_data", "access_token")
      .then((response) => {
        console.log(response, "token");
        const promise = authService(response.result);
        promise
        .then((response: any) => {
          console.log("auth success",response)
          if(response && response.data){
            if(response.data.access_token){
              dispatch(setIndexDBToken(response.data.access_token));
            }
          }else{
            console.log("auth success but ==> ",response)
            dispatch(setSessionExpired(true))
            // window.location.replace("../index.html#/");
          }
        });
      })
      .catch((error) => {
        dispatch(setIndexDBToken(""));
        console.log(error, "error");
      });
    getIndexDb("user_data", "user_id")
      .then((response) => {
        dispatch(setIndexDBUser_Id(response.result));
        console.log("RBAC USER ID: ", response.result)
        
      })
      .catch((error) => {
        dispatch(setIndexDBMainRole(""));
        console.log(error, "error");
      });
      
    //PM_MAKER ID ***************************
    getIndexDb("user_data", "pmaker_uid")
    .then((response) => {
      dispatch(setIndexDBPMaker_Id(response.result));
      console.log("RBAC PM_MAKER_ID: ", response.result)
    })
    .catch((error) => {
      dispatch(setIndexDBMainRole(""));
      console.log(error, "error");
    });
    //PM_MAKER ID ***************************

    getIndexDb("user_data", "main_role")
      .then((response) => {
        dispatch(setIndexDBMainRole(response.result));
      })
      .catch((error) => {
        dispatch(setIndexDBMainRole(""));
        console.log(error, "error");
      });
    getIndexDb("user_data", "role")
      .then((response) => {
        dispatch(setIndexDBRole(response.result));
      })
      .catch((error) => {
        dispatch(setIndexDBRole([]));
        console.log(error, "error ==> Role");
      });
    getIndexDb("user_data", "rbac")
      .then((response) => {
        dispatch(setIndexDBRBAC(response.result));
        console.log("RBAC USERS: ", response)
      })
      .catch((error) => {
        dispatch(setIndexDBRBAC([]));
        console.log(error, "error ==> RBAC");
      });
    getIndexDb("user_data", "first_name")
      .then((response) => {
        dispatch(setIndexDBFirstName(response.result));
      })
      .catch((error) => {
        dispatch(setIndexDBFirstName(""));
        console.log(error, "error");
      });
    getIndexDb("user_data", "middle_name")
      .then((response) => {
        dispatch(setIndexDBMiddleName(response.result));
      })
      .catch((error) => {
        dispatch(setIndexDBMiddleName(""));
        console.log(error, "error");
      });
    getIndexDb("user_data", "last_name")
      .then((response) => {
        dispatch(setIndexDBLastName(response.result));
      })
      .catch((error) => { 
        dispatch(setIndexDBLastName(""));
        console.log(error, "error");
      });
    getIndexDb("user_data", "suffix")
      .then((response) => {
        dispatch(setIndexDBSuffix(response.result));
      })
      .catch((error) => { 
        dispatch(setIndexDBSuffix(""));
        console.log(error, "error");
      });
    getIndexDb("user_data", "full_name")
      .then((response) => {
        dispatch(setIndexDBFullName(response.result));
      })
      .catch((error) => {
        dispatch(setIndexDBFullName(""));
        console.log(error, "error");
      });
    getIndexDb("user_data", "username")
      .then((response) => {
        dispatch(setIndexDBUserName(response.result));
      })
      .catch((error) => {
        dispatch(setIndexDBUserName(""));
        console.log(error, "error");
      });
    getIndexDb("user_data", "modules")
      .then((response) => {
        dispatch(setModules(response.result));
      })
      .catch((error) => {
        dispatch(setModules([]));
        console.log(error, "error");
      });
    getIndexDb("user_data", "main_module")
      .then((response) => {
        dispatch(setIndexDBMainModule(response.result));
      })
      .catch((error) => {
        dispatch(setIndexDBMainModule(""));
        console.log(error, "error");
      });
    getIndexDb("user_data", "group_name")
      .then((response) => {
        dispatch(setIndexDBGroupName(response.result));
      })
      .catch((error) => {
        dispatch(setIndexDBGroupName(""));
        console.log(error, "error");
      });
  } catch (err) {
    console.log(err, "error");
  }
};

export const logout = (): AppThunk => async (dispatch) => {
  try {
    const response = logoutService();
    console.log("logoutService Respnse ==> ", response);
    window.location.replace("../index.html#/");
  } catch (err) {
    console.log(err, "error");
  }
};

/**
 * Handle API Call for Get : Fetch for profile page
 * <AUTHOR>
 * @param token
 * @url `api/profile
 */
// @ts-ignore
export const getUserProfile = (token: any): AppThunk => async (
  dispatch: AppDispatch, 
  getState: () => RootState
) => {
  dispatch(setIsRequesting(true))
  const response = getUserData(token);
  await response
    .then((a: any) => {
      dispatch(setIsRequesting(false))
      if (a === undefined) {
        console.log("error");
        dispatch(setError(true));
      } else {

      
          fieldsToEncrypt.forEach((field: string) => {
            if (a.data.body !== null && field in a.data.body) {
              a.data.body[field] = decryptCredentials(a.data.body[
                field
              ] as string);
            }
          });
        

        dispatch(setProfile(a.data))
        console.log("USER PROFILE: ", a.data)
      }
    })
    .catch(function (e) {
      switch(e.response.status){
        case 500: 
          console.log("catch", e);
          dispatch(setIsRequesting(false))
          dispatch(setError(true));
          console.log("Oops, something went wrong.");
        break;
        case 401: 
          dispatch(setSessionExpired(true))
        break; 

      }
    });
};

/**
 * Handle API Call for Put : Update user password
 * <AUTHOR>
 * @param token
 * @url `api/user/password
 */
// @ts-ignore
export const updatePassword = (passwordParams: any): AppThunk => async (
  dispatch: AppDispatch, 
  getState: () => RootState
) => {
  dispatch(setIsRequesting(true))
  let token = getState().login.token;
  const response = updateUserPassword(passwordParams, token);
  return await response
};

/**
 * Handle API Call for Patch : Update profile picture
 * <AUTHOR>
 * @param token
 * @url `api/users/profile_pic
 */
// @ts-ignore
export const updateProfilePic = (data: any): AppThunk => async (
  dispatch: AppDispatch, 
  getState: () => RootState
) => {
  dispatch(setIsRequesting(true))
  let token = getState().login.token;
  const response = updateProfAvatar(data, token);
  return await response
};

export default slice.reducer;