import { createSlice } from "@reduxjs/toolkit"

export const dashboardSlice = createSlice({
	name: "dashboard",
	initialState: {
		activeTile: 0,
		activeSalesTile: 0,
		activeSpecialistTile: 0,
		activeSupervisorTile: 0,
		activeManagerTile: 0,
		tileName: "Total Request",
		role: "",
	},
	reducers: {
		changeTileSales: (state, action) => {
			state.activeSalesTile = action.payload.tileIndexSales
			state.tileName = action.payload.name
			state.role = action.payload.role
		},
		changeTileSpecialist: (state, action) => {
			state.activeSpecialistTile = action.payload.tileIndexSpecialist
			state.tileName = action.payload.name
			state.role = action.payload.role
		},
		changeTileSupervisor: (state, action) => {
			state.activeSupervisorTile = action.payload.tileIndexSupervisor
			state.tileName = action.payload.name
		},
		changeTileManager: (state, action) => {
			state.activeManagerTile = action.payload.tileIndexManager
			state.tileName = action.payload.name
		},
		changeTile: (state, action) => {
			state.activeTile = action.payload.tileIndex
			state.tileName = action.payload.name
		},
	},
})

export const {
	changeTileSales,
	changeTileSpecialist,
	changeTileSupervisor,
	changeTileManager,
	changeTile,
} = dashboardSlice.actions

export default dashboardSlice.reducer
