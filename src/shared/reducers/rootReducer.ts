import { combineReducers } from "@reduxjs/toolkit";

// add your slices here.
import loginSlice from "./LoginSlice";
import dashboardSlice from "./DashboardSlice";
import franchiseSlice from "./FranchiseSlice";
import reportSlice from './ReportSlice';
import maintenanceSlice from './MaintenanceSlice';
import contactSlice from './ContactSlice';

const rootReducer = combineReducers({
  login: loginSlice,
  franchise: franchiseSlice,
  dashboard: dashboardSlice,
  report: reportSlice,
  maintenance: maintenanceSlice,
  contact: contactSlice,
});

export type RootState = ReturnType<typeof rootReducer>;

export default rootReducer;
