import { createSlice, PayloadAction } from "@reduxjs/toolkit"
import { Franchise } from "../models/Franchise"
import { AppThunk, AppDispatch } from "../../store"
import { RootState } from "./rootReducer"
import axios from "axios"
import DownloadFile from "js-file-download"

import {
	getFranchises,
	createFranchiseService,
	saveFranchiseService,
	getFranchise,
	uploadFranchiseFileService,
	deleteFranchiseFileService,
	updateFranchiseStatus,
	updateFranchise,
	downloadFranchiseFileService,
	submitFranchise,
	validateFranchiseInput,
	validateFranchiseInpuTIN,
	updateDueDate,
	updateExpiryDate,
	updateRequestResubmitted,
	getIndustryList,
	getRegionList,
	getProvinceList,
	getCitiesList,
	// getBrgyList,
	getBdoUserList,
	getMotherCompanyList,
	submitRefranchise,
	getBarangayList,
	// checkToken
} from "../../services/FranchiseService"
import { setSessionExpired } from "./LoginSlice"

interface IUploading {
	file_name: string
	file_type: string
	file_size: number
	percentCompleted: number
	bytes: number
	source?: any
}
interface IUploaded {
	_id?: string
}

interface IAppointmentLetterDetails {
	agent: string,
	valid_from: string,
	valid_until: string,
}
export interface IErrorUploading {
	name: string
	lastModified: number
	type: string
	message: string
}

export interface BusinessLocation {
	city: any[]
	region: any[]
	brgy: any[]
}

export const franchiseSlice = createSlice({
	name: "franchise",
	initialState: {
		tab: 0,
		activeFilterIndex: 0,
		activeFilterName: "All",
		isView: false,
		isViewLoading: false,
		details: {} as Franchise,
		isNewInputs: false,
		isCreate: false,
		isEdit: false,
		isEditLoading: false,
		isSearching: false,
		new: {} as Franchise,
		newRemarks: {} as Franchise,
		isSaving: false,
		isSavingLoading: false,
		validationModalContent: {
			title: "",
			p: "",
			error: false,
		},
		isUpdating: false,
		franchiseList: [],
		industryListData: [],
		bdoUserListData: [],
		locationListData: {
			regions: [],
			provinces: [],
			cities: [],
			brgy: [],
		},
		appointmentLetter: [] as any[],
		appointmentLetterDetails: {} as IAppointmentLetterDetails,
		appointmentLetterToUpload: false,
		uploadedAppoinmentLetter: {} as IUploaded,
		uploadingAppointmentLetter: {} as IUploading,
		uploadingFile: {} as IUploading,
		uploadedFile: {} as IUploaded,
		rejectedFile: {} as IErrorUploading,
		deletingFile: {},
		deletedFile: {} as IUploaded,
		deleteErrorFile: {} as IErrorUploading,
		success: false,
		successDetails: {},
		error: false,
		errorDetails: {},
		isRequesting: false,
		isFetchingList: false,
		isValidating: false,
		validationInputContent: {
			label: "",
			valid: true,
			id: ""
		},
		bulkSubmitData: {
			title: "Submit Batch Request",
			description:
				"Are you sure you want to submit selected Franchise Requests? This will be subject for review by the Franchise Specialist.",
			success: false,
			error: false,
		},
		isVFRUploadedFile: false,
		isUpdateDueExpiry: false,
		motherCompanyListData: [],
		franchiseViewUploadModal: {
			toggle: true,
			message: ''
		},
		currentRecordId: null
	},
	reducers: {
		resetState: (state) => {
			state.tab = 0
			state.new = {} as Franchise
			state.details = {} as Franchise
			state.isSearching = false
			state.isNewInputs = false
			state.isCreate = false
			state.isEdit = false
			state.isEditLoading = false
			state.isSaving = false
			state.isSavingLoading = false
			state.isView = false
			state.isViewLoading = false
			state.success = false
			state.successDetails = {}
			state.error = false
			state.errorDetails = {}
			state.validationModalContent = {
				title: "",
				p: "",
				error: false,
			}
			state.isUpdating = false
			state.uploadedFile = {}
			state.rejectedFile = {} as IErrorUploading
			state.bulkSubmitData = {
				title: "Submit Batch Request",
				description:
					"Are you sure you want to submit selected Franchise Requests? This will be subject for review by the Franchise Specialist.",
				success: false,
				error: false,
			}
			state.validationInputContent = { valid: true, label: "", id: "" }
			state.isVFRUploadedFile = false
			state.isUpdateDueExpiry = false
			state.appointmentLetter = []
			state.appointmentLetterDetails = {} as IAppointmentLetterDetails
			state.appointmentLetterToUpload = false
			state.franchiseViewUploadModal = {
				toggle: false,
				message: ''
			}
			state.currentRecordId = null
		},
		clearAppointmentLetter: (state) => {
			console.log('fireeddd')
			state.appointmentLetter = []
			state.uploadedAppoinmentLetter = {}
		},
		changeFilter: (state, action) => {
			state.activeFilterIndex = action.payload.filterIndex
			state.activeFilterName = action.payload.name
		},
		createProgress: (state) => {
			state.tab += 1
			state.uploadedFile = {}
			state.rejectedFile = {} as IErrorUploading
		},

		createBackProgress: (state) => {
			state.tab -= 1
			state.uploadedFile = {}
			state.rejectedFile = {} as IErrorUploading
		},

		clearValidationModalContent: (state) => {
			state.validationModalContent = {
				title: "",
				p: "",
				error: false,
			}
		},
		goBackAndCreateNew: (state) => {
			state.tab = 0
			state.new = {} as Franchise
			state.error = false
			state.errorDetails = {}
		},
		setFranchiseData: (state, { payload }: PayloadAction<Franchise>) => {
			state.new = payload
			state.error = false
			state.errorDetails = {}
			state.isSavingLoading = false
			state.isSaving = false
		},
		setEncoderRemarks: (state, { payload }: PayloadAction<Franchise>) => {
			state.newRemarks = payload
		},
		setUserInputValues: (state, { payload }: any) => {
			state.isNewInputs = payload
		},
		setValidatorUploaded: (state, { payload }: any) => {
			state.isVFRUploadedFile = payload
		},
		setViewLoading: (state, { payload }: any) => {
			state.isViewLoading = payload
		},
		getFranchiseData: (state, { payload }: PayloadAction<Franchise>) => {
			switch (payload.status) {
				case "SAVED":
					payload.isSaved = true
					break
				case "SUBMITTED":
					payload.isSubmitted = true
					break
				case "RESUBMITTED":
					payload.isReSubmitted = true
					break
				case "CANCELLED":
					payload.isCancelled = true
					break
				case "RECEIVED":
					payload.isReceived = true
					break
				case "RETURNED":
					payload.isReturned = true
					break
				case "VALIDATED":
					payload.isValidated = true
					break
				case "FOR_APPROVAL":
					payload.isEndorsed = true
					break
				case "RESUBMITTED_TO_SUPERVISOR":
					payload.isReEndorsed = true
					break
				case "APPROVAL_IN_PROCESS":
					payload.isApprovalInProcess = true
					break
				case "RETURNED_TO_VALIDATOR":
					payload.isReturnedToValidator = true
					break
				case "FINAL_APPROVAL":
					payload.isForFinalApproval = true
					break
				case "FINAL_APPROVAL_IN_PROCESS":
					payload.isFinalApprovalInProcess = true
					break
				case "APPROVED":
					payload.isApproved = true
					break
				case "DISAPPROVED":
					payload.isDisApproved = true
					break
				case "EXPIRED":
					payload.isExpired = true
					break
				default:
					break
			}
			state.details = payload
			state.isViewLoading = false
			state.isView = true
			state.error = false
			state.errorDetails = {}
		},
		setEditLoading: (state, { payload }: any) => {
			state.isEditLoading = payload
		},
		editFranchiseData: (state, { payload }: PayloadAction<Franchise>) => {
			state.new = payload
			state.isEditLoading = false
			state.isEdit = true
		},
		setIsCreate: (state, action) => {
			state.isCreate = action.payload
		},
		setSaveLoading: (state, { payload }: any) => {
			state.isSavingLoading = payload
		},

		setUpdating: (state, action) => {
			state.isUpdating = action.payload
		},

		setModalContent: (state, action) => {
			state.validationModalContent.title = action.payload.title
			state.validationModalContent.p = action.payload.p
			state.validationModalContent.error = action.payload.error
		},

		getFranchiseListData: (state, action) => {
			state.franchiseList = action.payload
			state.error = false
			state.errorDetails = {}
		},
		getIndustryListData: (state, action) => {
			state.industryListData = action.payload
			state.error = false
			state.errorDetails = {}
		},
		getBdoUserListData: (state, action) => {
			state.bdoUserListData = action.payload
			state.error = false
			state.errorDetails = {}
		},
		getLocationRegions: (state, action) => {
			state.locationListData.regions = action.payload
			state.error = false
			state.errorDetails = {}
		},
		getLocationProvinces: (state, action) => {
			state.locationListData.provinces = action.payload
			state.error = false
			state.errorDetails = {}
		},
		getLocationCities: (state, action) => {
			state.locationListData.cities = action.payload
			state.error = false
			state.errorDetails = {}
		},
		getLocationBrgys: (state, action) => {
			state.locationListData.brgy = action.payload
			state.error = false
			state.errorDetails = {}
		},
		setAppointmentLetterDetails: (
			state, payload
		) => {
			console.log(payload)
			const details = payload.payload
			state.appointmentLetterDetails = details 
		},
		setAppointmentLetterUploadProgress: ( 
			state, 
			{ payload } : PayloadAction<IUploading>,
			) => {
			console.log("uploaddingg", payload)
			state.uploadingAppointmentLetter = payload		
		},
		setAppointmentLetterToUpload: (state) => {
			state.appointmentLetterToUpload = false
		},
		setAppointmentLetter: ( state, { payload }) => {
			state.appointmentLetter = [...state.appointmentLetter, payload]
			state.appointmentLetterToUpload = true
		},
		setAppointmentLetterUploaded : (
			state,
			{ payload } : PayloadAction<{}>,
		) => {
			state.uploadedAppoinmentLetter = payload
			state.uploadingAppointmentLetter = {} as IUploading
			state.isNewInputs = true
		},
		setIsSearching: (state, action) => {
			state.isSearching = action.payload
		},
		setFranchiseError: (state, action) => {
			state.error = true
			state.errorDetails = action.payload
			state.success = false
			state.successDetails = {}
		},
		setOnUploadFileProgress: (
			state,
			{ payload }: PayloadAction<IUploading>,
		) => {
			state.uploadingFile = payload
		},
		setOnUploadFileCompleted: (state, { payload }: PayloadAction<{}>) => {
			state.uploadedFile = payload
			state.uploadingFile = {} as IUploading
			state.isNewInputs = true
		},
		setOnUploadFileRejected: (
			state,
			{ payload }: PayloadAction<IErrorUploading>,
		) => {
			state.rejectedFile = payload
			state.uploadingFile = {} as IUploading
		},
		setOnDeleteFileProgress: (state, action) => {
			state.deletingFile = action.payload
		},
		setOnDeleteFileCompleted: (state, { payload }: PayloadAction<{}>) => {
			state.deletedFile = payload
			state.deletingFile = {}
		},
		setOnDeleteFileError: (
			state,
			{ payload }: PayloadAction<IErrorUploading>,
		) => {
			state.deleteErrorFile = payload
			state.deletingFile = {}
		},
		setIsRequesting: (state, action) => {
			state.isRequesting = action.payload
		},
		setIsFetchingList: (state, action) => {
			state.isFetchingList = action.payload
		},
		setIsValidating: (state, action) => {
			state.isValidating = action.payload
		},
		setBulkSubmitData: (state, action) => {
			state.bulkSubmitData = action.payload
		},
		resetUnsavedChanges: (state) => {
			state.isNewInputs = false
			state.uploadedFile = {}
		},
		setInputValidation: (state, action) => {
			state.validationInputContent = action.payload
		},
		setIsUpdateDueExpiry: (state, action) => {
			state.isUpdateDueExpiry = action.payload
		},
		getMotherCompanyListData: (state, action) => {
			state.motherCompanyListData = action.payload
			state.error = false
			state.errorDetails = {}
		},
		setFranchiseViewUploadModal: (state, action) => {
			const { status, message } = action.payload

			switch (status) {
				case 200:
					state.franchiseViewUploadModal.toggle = true
					state.franchiseViewUploadModal.message = message
					break;
				case 'invalid':
					state.franchiseViewUploadModal.toggle = true
					state.franchiseViewUploadModal.message = message
					break;
				case 'exceed':
					state.franchiseViewUploadModal.toggle = true
					state.franchiseViewUploadModal.message = message
					break;
				case 413: 
					state.franchiseViewUploadModal.toggle = true
					state.franchiseViewUploadModal.message = message
					break;
				
				default:
					break;
			}	

		},
		closeFranchiseViewUploadModal: (state) => {
			state.franchiseViewUploadModal.toggle = false
		},
		setCurrentRecordId: (state, action) => {
			state.currentRecordId = action.payload
		}
	},
})

export const {
	resetState,
	changeFilter,
	clearAppointmentLetter,
	createProgress,
	createBackProgress,
	clearValidationModalContent,
	goBackAndCreateNew,
	setFranchiseData,
	setSaveLoading,
	setEditLoading,
	editFranchiseData,
	setIsCreate,
	setUserInputValues,
	setValidatorUploaded,
	setViewLoading,
	getFranchiseData,
	getFranchiseListData,
	getIndustryListData,
	getBdoUserListData,
	getLocationRegions,
	getLocationProvinces,
	getLocationCities,
	getLocationBrgys,
	setAppointmentLetter,
	setAppointmentLetterToUpload,
	setAppointmentLetterDetails,
	setAppointmentLetterUploadProgress,
	setAppointmentLetterUploaded,
	setIsSearching,
	setFranchiseError,
	setModalContent,
	setUpdating,
	setOnUploadFileProgress,
	setOnUploadFileCompleted,
	setOnUploadFileRejected,
	setOnDeleteFileProgress,
	setOnDeleteFileCompleted,
	setOnDeleteFileError,
	setIsRequesting,
	setIsFetchingList,
	setIsValidating,
	setBulkSubmitData,
	resetUnsavedChanges,
	setInputValidation,
	setIsUpdateDueExpiry,
	setEncoderRemarks,
	getMotherCompanyListData,
	setFranchiseViewUploadModal,
	closeFranchiseViewUploadModal,
	setCurrentRecordId
} = franchiseSlice.actions

// Submit Franchise Details
export const addFranchise =
	(newFranchise: Franchise, _role?: string): AppThunk =>
	//@ts-ignore
	(dispatch: AppDispatch, getState: () => RootState) => {
		// @ts-ignore
		dispatch(setSaveLoading(true))
		let token = getState().login.token;
		let role = getState().login.roleAPI;
		let currentId = getState().franchise.currentRecordId;
		let data = newFranchise;
		dispatch(setIsRequesting(true))
		if (currentId) {
			console.log('saving current record with ID: ', currentId);

			console.log("ADD BODY Values: ", data)
			const response = updateFranchiseStatus(currentId, role, "submit", data, token)
			response
				.then((a: any) => {
					dispatch(setIsRequesting(false))
					// var data = a.data
					// data.supporting_docs_obj = data.supporting_docs
					// data.supporting_docs = data.supporting_docs.map((file: any) => {
					// 	return file._id
					// })
					// dispatch(editFranchiseData(data))
					// @ts-ignore
					dispatch(setSaveLoading(false))
					dispatch(resetUnsavedChanges())
					dispatch(
						setModalContent({
							title: "Request Submitted",
							p: "Franchise Request is submitted. This will be subject for review by the Franchising Specialist.",
							error: false,
						}),
					)
					dispatch(setIsCreate(false))
					// @ts-ignore
					dispatch(fetchFranchises())
				})
				.catch(function (e) {
					console.log("Error")
					dispatch(setIsRequesting(false))
					if (e.response) {
						switch (e.response.status) {
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
							case 401:
								console.log("unauthorized user")
							dispatch(setSessionExpired(true))
								break
							default:
								dispatch(
									setModalContent({
										title: "Error",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
						dispatch(setFranchiseError(e.response.data.errors))
						// @ts-ignore
						dispatch(setEditLoading(false))
					} else {
						dispatch(
							setModalContent({
								title: "Error",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					}
				})
		} else {
			const response = createFranchiseService(newFranchise, token)
			response
				.then((a: any) => {
					console.log(a)
					// @ts-ignore
					dispatch(setSaveLoading(false))
					dispatch(setIsRequesting(false))
					dispatch(resetUnsavedChanges())
					dispatch(
						setModalContent({
							title: "Request Submitted",
							p: "Franchise Request is submitted. This will be subject for review by the Franchising Specialist.",
							error: false,
						}),
					)
					dispatch(setIsCreate(false))
					// @ts-ignore
					dispatch(fetchFranchises())
				})
				.catch(function (e) {
					console.log(e.response)
					// @ts-ignore
					dispatch(setSaveLoading(false))
					dispatch(setIsRequesting(false))
					if (e.response) {
						switch (e.response.status) {
							case 400:
								if (e.response.data.err_type === "frf_save_duplicate") {
									dispatch(
										setModalContent({
											title: "Request already exist",
											p: "There is already an active franchise for the same company.",
											error: true
										})
									)
								} else if (e.response.data.err_type === "frf_already_in_process" || e.response.data.err_type ===  "processmaker" || e.response.data.errors) {
									dispatch(
										setModalContent({
											title: "Request already in process",
											p: "Sorry, there is already a franchise request for the same Company.",
											error: true
										})
									)
								}
								else {
									dispatch(
										setModalContent({
											title: "Error",
											p: "Oops, something went wrong.",
											error: true,
										}),
									)
								}
								break
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "Error",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
							case 401:
								console.log("unauthorized user")
								dispatch(setSessionExpired(true))
								break
							default:
								dispatch(
									setModalContent({
										title: "Error",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
						dispatch(setFranchiseError(e.response.data.errors))
					} else {
						dispatch(
							setModalContent({
								title: "Error",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					}
				})
		}
	}

// Save For Now Franchise Details
export const saveFranchise =
	(newFranchise: Franchise): AppThunk =>
	//@ts-ignore
	(dispatch: AppDispatch, getState: () => RootState) => {
		try {
			console.log('trying to save for now franchise')
			// @ts-ignore
			dispatch(setSaveLoading(true))
			let token = getState().login.token
			let isEdit = getState().franchise.isEdit
			let currentId;
			console.log('save new franchise values', newFranchise)
			dispatch(setIsRequesting(true))
			const response = saveFranchiseService(newFranchise, token)
			response
				.then((a: any) => {
					console.log("axios then", a)
					console.log('current id', a.data?._id || a.data?.data?._id);

					// let objectIdCreated
					// if (!currentId) {
					// 	objectIdCreated = a.data?._id || a.data?.id || null;
					// } else {
					// 	objectIdCreated = currentId
					// }
					currentId = a.data?._id || a.data?.data?._id;
					// @ts-ignore
					dispatch(setSaveLoading(false))
					dispatch(setIsRequesting(false))
					dispatch(resetUnsavedChanges())
					dispatch(
						setModalContent({
							title: "Saving Successful",
							p: "Your FRF has been saved! You can check the saved FRF on the Saved list. Do you want to create a new franchise request again?",
							error: false,
						}),
					)
					dispatch(setIsCreate(false))
					dispatch(setCurrentRecordId(currentId));
				})
				.catch(function (e) {
					console.log("e.response", e)
					dispatch(setIsRequesting(false))

					if (e.response) {
						switch (e.response.status) {
							case 200:
								dispatch(setCurrentRecordId(currentId));
								dispatch(
									setModalContent({
										title: "Saving Successful",
										p: "Your FRF has been saved! You can check the saved FRF on the Saved list. Do you want to create a new franchise request again?",
										error: false,
									}),
								)
								break;
							case 400:
								if (!isEdit) {
									console.log('FRF ERROR', e.response.data.err_type);
									if (e.response.data.err_type === "frf_save_duplicate") {
										dispatch(
											setModalContent({
												title: "Request already exist",
												p: "There is already an active franchise for the same company.",
												error: true
											})
										)
									} else if (e.response.data.err_type === "frf_already_in_process" || e.response.data.err_type ===  "processmaker" || e.response.data.errors) {
										dispatch(
											setModalContent({
												title: "Request already in process",
												p: "Sorry, there is already a franchise request for the same Company.",
												error: true
											})
										)
									}
									else {
										dispatch(
											setModalContent({
												title: "Error",
												p: "Oops, something went wrong.",
												error: true,
											}),
										)
									}
								} else {
									if (e.response.data.err_type === "frf_save_duplicate") {
										dispatch(
											setModalContent({
												title: "Request already exist",
												p: "There is already an active franchise for the same company.",
												error: true
											})
										)
									} else if (e.response.data.err_type === "frf_already_in_process" || e.response.data.err_type ===  "processmaker" || e.response.data.errors) {
										dispatch(
											setModalContent({
												title: "Request already in process",
												p: "Sorry, there is already a franchise request for the same Company.",
												error: true
											})
										)
									}
									else {
										dispatch(
											setModalContent({
												title: "Error",
												p: "Oops, something went wrong.",
												error: true,
											}),
										)
									}
								}
								break
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "Error Saving",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
							case 401:
								console.log("unauthorized user")
							dispatch(setSessionExpired(true))
								break
							default:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "Error",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
						dispatch(setFranchiseError(e.response.data.errors))
						// @ts-ignore
						dispatch(setSaveLoading(false))
					} else {
						dispatch(
							setModalContent({
								title: "Error Saving Response",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					}
				})
		} catch (err) {
			dispatch(setIsRequesting(false))
			// dispatch(setsaveSubmitInProgress(false))
			dispatch(
				setModalContent({
					title: "Error Saving",
					p: "Oops, something went wrong.",
					error: true,
				}),
			)
		}
	}



export const uploadAppointmentLetter = 
	(file: File, details: IAppointmentLetterDetails ): AppThunk => 
	//@ts-ignore
	(dispatch: AppDispatch, getState: () => RootState) => {
		console.log('uploooaddd app letttteeerrrrrr')
		console.log(file, details)
		let token = getState().login.token
		// Cancel all 
		let CancelToken = axios.CancelToken
		let source = CancelToken.source();
		setTimeout(() => {
			source.cancel()
		},600000)
		let formData = new FormData()
		formData.append("appointment_letter", file);
		formData.append("purpose", "appointment_letter");
		formData.append("agent", details.agent);
		formData.append("valid_from", details.valid_from);
		formData.append("valid_until", details.valid_until);
		const config = {
			onUploadProgress: (progressEvent: any) => {
				let percentCompleted = Math.floor(
					(progressEvent.loaded * 100) / progressEvent.total
				);
				let bytes = file.size * (percentCompleted / 100);
				let data = {
					file_name: file.name,
					file_type: file.type,
					file_size: file.size,
					percentCompleted,
					bytes,
					source,
				}
				dispatch(setAppointmentLetterUploadProgress(data));
			},
			onDownloadProgress: (e: any) => {
			},
			headers: {
				Authorization: `Bearer ${token}`,
				"Content-type": "multipart/form-data",
				cancelToken: source.token,
				timeout: 600000,
			}
		}

		const result = uploadFranchiseFileService(formData, config, '');
		
		result.then(res => {
			console.log('responnseeeeee', res.data)
			dispatch(setAppointmentLetterToUpload())
			dispatch(setAppointmentLetterUploaded(res.data));
		}).catch(err => console.log(err));
	}
// Upload Franchise Documents
export const uploadDocument =
	(file: File, from: String): AppThunk =>
	//@ts-ignore
	(dispatch: AppDispatch, getState: () => RootState) => {
		let token = getState().login.token
		var CancelToken = axios.CancelToken
		var source = CancelToken.source()
		setTimeout(() => {
			source.cancel()
			// Timeout Logic
		}, 600000)
		let formData = new FormData()
		formData.append("supporting_docs", file)
		const config = {
			onUploadProgress: (progressEvent: any) => {
				let percentCompleted = Math.floor(
					(progressEvent.loaded * 100) / progressEvent.total,
				)
				let bytes = file.size * (percentCompleted / 100)
				let data = {
					file_name: file.name,
					file_type: file.type,
					file_size: file.size,
					percentCompleted,
					bytes,
					source,
				}
				dispatch(setOnUploadFileProgress(data))
			},
			onDownloadProgress: function (event: any) {
				console.log("download", event)
			},
			headers: {
				Authorization: `Bearer ${token}`,
				"Content-type": "multipart/form-data",
				cancelToken: source.token,
				timeout: 600000,
			},
		}
		const result = uploadFranchiseFileService(formData, config, from)
		result
			.then((response) => {
				console.log("resss", response)
				dispatch(setOnUploadFileCompleted(response.data))
			})
			.catch((err) => {
				console.log("UPLOAD ERROR RESPONSE ===> ", err)
				console.log(err.response)

				if (axios.isCancel(err)) {
					// @ts-ignore
					if (err && err.response) {
						// @ts-ignore
						console.log("Request canceled", err.response)
					}
					dispatch(
						setOnUploadFileRejected({
							name: file.name,
							lastModified: file.lastModified,
							type: file.type,
							message: "Upload canceled",
						}),
					)
				} else {
					if (err && err.response) {
						console.log("Request Error", err.response)
					}
					dispatch(
						setOnUploadFileRejected({
							name: file.name,
							lastModified: file.lastModified,
							type: file.type,
							message: "Upload Failed",
						}),
					)
				}
			})
	}

// Upload Franchise Documents
export const uploadDocuments =
	(file: File, from: String): AppThunk =>
	//@ts-ignore
	(dispatch: AppDispatch, getState: () => RootState) => {
		let token = getState().login.token
		var CancelToken = axios.CancelToken
		var source = CancelToken.source()
		setTimeout(() => {
			source.cancel()
			// Timeout Logic
		}, 600000)
		let formData = new FormData()
		formData.append("supporting_docs", file)
		const config = {
			onUploadProgress: (progressEvent: any) => {
				let percentCompleted = Math.floor(
					(progressEvent.loaded * 100) / progressEvent.total,
				)
				let bytes = file.size * (percentCompleted / 100)
				let data = {
					file_name: file.name,
					file_type: file.type,
					file_size: file.size,
					percentCompleted,
					bytes,
					// source,
				}
				dispatch(setOnUploadFileProgress(data))
			},
			onDownloadProgress: function (event: any) {
				console.log("download", event)
			},
			headers: {
				Authorization: `Bearer ${token}`,
				"Content-type": "multipart/form-data",
				cancelToken: source.token,
				timeout: 600000,
			},
		}
		dispatch(setIsRequesting(true))
		return uploadFranchiseFileService(formData, config, from)
		//   .then((response:any) =>{
		//   if(response){
		//     if(response.status === 400){
		//       dispatch(
		//         setOnUploadFileRejected({
		//           name: file.name,
		//           lastModified: file.lastModified,
		//           type: file.type,
		//           message:response.data,
		//         })
		//       );
		//     }
		//   }
		// }).catch((e:any) =>
		//   {
		//     console.log(e,typeof e)
		//     if(e){
		//       let errMsg = "Opps something wrong!!!";
		//       if(e.response){
		//         errMsg = e.response.data;
		//       }
		//       dispatch(
		//         setOnUploadFileRejected({
		//           name: file.name,
		//           lastModified: file.lastModified,
		//           type: file.type,
		//           message:errMsg,
		//         })
		//       );
		//     }
		//   }
		// );
	}

// Edit Franchise Details
export const editFranchise =
	(id: any, _role?: string): AppThunk =>
	//@ts-ignore
	async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			// @ts-ignore
			dispatch(setEditLoading(true))
			let token = getState().login.token
			let role = getState().login.roleAPI
			dispatch(setIsRequesting(true))
			const response = getFranchise(id, role === "abt_maintainer" ? 'encoder' : _role ? _role : role, token)
			response
				.then((a: any) => {
					dispatch(setIsRequesting(false))
					var data = a.data
					data.supporting_docs_obj = data.supporting_docs
					data.supporting_docs = data.supporting_docs.map((file: any) => {
						return file._id
					})
					dispatch(editFranchiseData(data))
				})
				.catch(function (e) {
					console.log("Error")
					dispatch(setIsRequesting(false))
					if (e.response) {
						switch (e.response.status) {
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
							case 401:
								console.log("unauthorized user")
							dispatch(setSessionExpired(true))
								break
							default:
								dispatch(
									setModalContent({
										title: "Error",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
						dispatch(setFranchiseError(e.response.data.errors))
						// @ts-ignore
						dispatch(setEditLoading(false))
					} else {
						dispatch(
							setModalContent({
								title: "Error",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					}
				})
			dispatch(setIsFetchingList(false))
		} catch (err) {
			dispatch(setIsRequesting(false))
			dispatch(setFranchiseError)
			dispatch(setIsFetchingList(false))
			console.log(err)
		}
	}

// Get Franchise Details
export const fetchFranchise =
	(id: string, _role?: string): AppThunk =>
	//@ts-ignore
	async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			dispatch(setIsRequesting(true))
			// @ts-ignore
			dispatch(setViewLoading(true))
			let token = getState().login.token
			let role = getState().login.roleAPI === "abt_maintainer" ? "encoder" : _role ? _role : getState().login.roleAPI
			console.log('FIRED 1')
			const response = getFranchise(id, role, token)
			response
				.then((a: any) => {
			console.log('FIRED 2')
					dispatch(setIsRequesting(false))
					// @ts-ignore
					dispatch(setViewLoading(false))
					console.log("view details result", a)
					if (a !== undefined) {
						console.log("view details result DATA ==> ", a.data)
						var data = a.data
						data.supporting_docs_obj = data.supporting_docs
						data.supporting_docs = data.supporting_docs.map((file: any) => {
							return file._id
						})
						dispatch(getFranchiseData(data))
						if (
							(role === "validator" && data.status === "RECEIVED") ||
							(role === "supervisor" &&
								data.status === "APPROVAL_IN_PROCESS") ||
							(role === "manager" &&
								data.status === "FINAL_APPROVAL_IN_PROCESS")
						) {
							// @ts-ignore
							dispatch(fetchFranchises(role))
						}
					}
				})
				.catch(function (e) {
			console.log('FIRED 3')
					console.log("Error", e)
					dispatch(setIsRequesting(false))
					// @ts-ignore
					dispatch(setViewLoading(false))
					console.log("Error")
					if (e.response) {
						switch (e.response.status) {
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
							case 401: 
								dispatch(setSessionExpired(true))
						}
						dispatch(setFranchiseError(e.response.data.errors))
					}
				})
		} catch (err) {
			dispatch(setIsRequesting(false))
			dispatch(setFranchiseError)
			console.log(err)
		}
	}

// Get Franchise List
export const fetchFranchises =
//@ts-ignore
	(_role?: string): AppThunk => async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let token = getState().login.token
			let role = getState().login.roleAPI
			dispatch(setIsRequesting(true))
			const response = getFranchises(role === 'abt_maintainer' ? 'encoder' : _role ? _role : role, token)
			response
				.then((a: any) => {
					dispatch(setIsRequesting(false))
					if (a === undefined) {
						console.log("error")
						dispatch(setFranchiseError)
					} else {
						dispatch(getFranchiseListData(a.data))
					}
				})
				.catch(function (e) {
					console.log("catch", e)
					dispatch(setIsRequesting(false))
					if (e.response && e.response.status) {
						switch (e.response.status) {
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
							case 401: 
								dispatch(setSessionExpired(true))
						}
						dispatch(setFranchiseError(e.response.data.errors))
					}
				})
		} catch (err) {
			dispatch(setIsRequesting(false))
			console.log(err)
		}
	}

// Update Franchise Request {status : cancel,return,validate}
export const updateFranchiseRequest =
	(id: string, action: string, userRole: string, values?: any, requestor?: string): AppThunk =>
	//@ts-ignore
	(dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let token = getState().login.token
			let role = userRole ? userRole : getState().login.roleAPI
			let data = values ? values : {}
			/*** Uncomment this for testing ***/
			/* dispatch(setIsRequesting(true))
    dispatch(setUpdating(true));
    switch (action) {
      case "approve":
        dispatch(
          setModalContent({
            title: "Request Approved",
            p: requestor,
            error: false,
          })
        );
        break;
      case "disapprove":
        dispatch(
          setModalContent({
            title: "Request Disapproved",
            p: "Request has been disapproved.",
            error: false,
          })
        );
        break;
    }
    dispatch(setUpdating(false));
    dispatch(setIsRequesting(false))
    return; */
			/* -------------------------------  */
			dispatch(setUpdating(true))
			dispatch(setIsRequesting(true))
			console.log("BODY Values: ", data)
			const response = updateFranchiseStatus(id, role, action, data, token)
			response
				.then((a: any) => {
					dispatch(setIsRequesting(false))
					if (a === undefined) {
						console.log("error")
						dispatch(
							setModalContent({
								title: "Error",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					} else {
						dispatch(resetUnsavedChanges())
						switch (action) {
							case "return":
								dispatch(
									setModalContent({
										title: "Request Returned",
										p:
											role === "supervisor"
												? "Franchise Request has been returned to Franchising Specialist."
												: "Franchise Request has been returned to sales user.",
										error: false,
									}),
								)
								break
							case "validate":
								dispatch(
									setModalContent({
										title: "Validated Franchise Request",
										p: a.data.validation_message,
										error: false,
									}),
								)
								var ndata = a.data
								ndata.supporting_docs_obj = ndata.supporting_docs
								ndata.supporting_docs = ndata.supporting_docs.map(
									(file: any) => {
										return file._id
									},
								)
								dispatch(getFranchiseData(ndata))
								break
							case "endorse":
								dispatch(
									setModalContent({
										title: "Request Endorsed",
										p: "Franchise Request has been endorsed.",
										error: false,
									}),
								)
								break
							case "reendorse":
								dispatch(
									setModalContent({
										title: "Request Endorsed",
										p: "Franchise Request has been endorsed.",
										error: false,
									}),
								)
								break
							case "approve":
								if (requestor) {
									dispatch(
										setModalContent({
											title: "Request Approved",
											p: requestor,
											error: false,
										}),
									)
								} else {
									dispatch(
										setModalContent({
											title: "Request Approved",
											p:
												role === "supervisor"
													? "Request has been approved. This will be subject for final approval."
													: "Request has been approved. It has been successfully sent to Underwriting.",
											error: false,
										}),
									)
								}
								break
							case "disapprove":
								dispatch(
									setModalContent({
										title: "Request Disapproved",
										p: "Request has been disapproved.",
										error: false,
									}),
								)
								break
							case "cancel":
								dispatch(
									setModalContent({
										title: "Request Cancelled",
										p: "Franchise Request has been cancelled.",
										error: false,
									}),
								)
								break
							case "resubmit":
								dispatch(
									setModalContent({
										title:
											data.status === "RETURNED"
												? "Request Submitted"
												: "Request Updated",
										p: "Franchise Request is submitted. This will be subject for review by the Franchising Specialist.",
										error: false,
									}),
								)
								break
						}
						// @ts-ignore
						dispatch(fetchFranchises(role))
					}
					dispatch(setUpdating(false))
				})
				.catch(function (e) {
					dispatch(setIsRequesting(false))
					console.log("catch", e)
					if (e.response) {
						switch (e.response.status) {
							case 500:
								console.log("500 Oops something went wrong!")
								dispatch(
									setModalContent({
										title:
											action === "approve" ? "Approve Request Error" : "Error",
										p:
											action === "approve"
												? "An error occured in approving the request. The franchise has not been sent to Underwriting"
												: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
							case 504:
								console.log("504 Oops something went wrong!")
								dispatch(
									setModalContent({
										title:
											action === "approve" ? "Approve Request Error" : "Error",
										p:
											action === "approve"
												? "An error occured in approving the request. The franchise has not been sent to Underwriting"
												: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
							case 422:
								dispatch(
									setModalContent({
										title: "Error",
										p:
											action === "approve"
												? "Oops, something went wrong. The franchise request was not approved."
												: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
							case 427:
								dispatch(
									setModalContent({
										title: "Approve Request Error",
										p: "You have already approved a franchise request for the same company.",
										error: true,
									}),
								)
								break
							case 400:
								console.log("400 Oops something went wrong!")
								if (action === "resubmit") {
									if (
										e.response.data &&
										e.response.data.errors &&
										typeof e.response.data.errors == "string"
									) {
										dispatch(
											setModalContent({
												title: "Request already in process ",
												p: "Sorry, there is already a franchise request for the same Company.",
												error: true,
											}),
										)
									} else {
										if (
											e.response.data.errors.length > 0 &&
											e.response.data.errors[0].msg !== "Server Error"
										) {
											dispatch(
												setModalContent({
													title: "Request already exist",
													p: "There is already an active franchise for the same company.",
													error: true,
												}),
											)
										} else {
											dispatch(
												setModalContent({
													title: "Error Saving",
													p: "Oops, something went wrong.",
													error: true,
												}),
											)
										}
									}
								} else {
									const { err_type = '', message = 'Server Error' } = e.response.data
									if(err_type === "processmaker") {
										dispatch(
											setModalContent({
												title:
													action === "approve"
														? "Approve Request Error"
														: "Error",
												p: message,
												error: true,
											}),
										)
									} else {
										dispatch(
											setModalContent({
												title:
													action === "approve"
														? "Approve Request Error"
														: "Error",
												p:
													action === "approve"
														? "An error occured in approving the request. The franchise has not been sent to Underwriting"
														: "Oops, something went wrong.",
												error: true,
											}),
										)
									}

								}
								break
							case 401:
								console.log("unauthorized user")
							dispatch(setSessionExpired(true))
								break
							default:
								dispatch(
									setModalContent({
										title: "Error",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
					} else {
						dispatch(
							setModalContent({
								title: "Error",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					}
					dispatch(setUpdating(false))
				})
		} catch (err) {
			dispatch(setIsRequesting(false))
			console.log(err)
		}
	}

// Delete Franchise File Uploaded
export const deleteFranchiseFileUpload =
	(_id: string): AppThunk =>
	//@ts-ignore
	(dispatch: AppDispatch, getState: () => RootState) => {
		try {
			// dispatch(setOnDeleteFileProgress());
			let token = getState().login.token
			const response = deleteFranchiseFileService(_id, token)
			response
				.then((a: any) => {
					console.log("delete upload result", a)
					if (a && a.status === 200 && a.data) {
						dispatch(setOnDeleteFileCompleted({ _id, message: a.data }))
					}
				})
				.catch(function (e) {
					console.log("delete upload error ==> ", e)
					if (e && e.data) {
						dispatch(setOnDeleteFileError(e.data))
					}
				})
		} catch (err) {
			console.log(err)
		}
	}

// Add Franchise Request on Success Upload of Specialist in View Modal
export const updateFranchiseOnUpload =
	(franchise: Franchise, supporting_docs: any[]): AppThunk =>
	//@ts-ignore
	(dispatch: AppDispatch, getState: () => RootState) => {
		let token = getState().login.token
		let id = franchise._id
		const response = updateFranchise(id, { supporting_docs }, token)
		response
			.then((a: any) => {
				if (a !== undefined) {
					var data = a.data
					data.supporting_docs_obj = data.supporting_docs
					data.supporting_docs = data.supporting_docs.map((file: any) => {
						return file._id
					})
					dispatch(getFranchiseData(data))
				}
			})
			.catch(function (e) {
				// dispatch(deleteFranchiseFileUpload(supporting_docs[0]));
			})
		// dispatch(setOnUploadFileCompleted({}));
	}

// Download Franchise File Uploaded
export const downloadFranchiseFileUpload =
	(_id: string, filename: string): AppThunk =>
	//@ts-ignore
	async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let token = getState().login.token
			const response = await downloadFranchiseFileService(_id, token)
			if (response) {
				// console.log(response)
				// console.log(filename)
				DownloadFile(response.data, filename)
			}
		} catch (error) {}
	}

/**
 * Handle API Call for Bulk Submit
 * @param _ids @type array of object
 * <AUTHOR>
 * @action PUT
 * @url `/franchise/bulk-submit`
 * @body [{"_id":"5eddcf2ef28309171191dbc4","app_uid":"4057769565eddcf2e1d3317040366814"}]
 */
export const bulkSubmit =
	(_ids: any[]): AppThunk =>
	//@ts-ignore
	async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let token = getState().login.token
			dispatch(setIsRequesting(true))
			/* setTimeout(() => {
      dispatch(setIsRequesting(false))
      dispatch(setBulkSubmitData({
        title: "Batch Request Submitted",
        description: "Batch Franchise Request has been submitted and will be subjected for review by the Franchise Specialist.",
        success:true,
        error:false,
      }))
    }, 10000);
    return; */
			const promise = submitFranchise(_ids, token)
			promise
				.then((a: any) => {
					dispatch(setIsRequesting(false))
					// @ts-ignore
					dispatch(fetchFranchises())
					dispatch(
						setBulkSubmitData({
							title: "Batch Request Submitted",
							description:
								"Batch Franchise Request has been submitted and will be subjected for review by the Franchise Specialist.",
							success: true,
							error: false,
						}),
					)
				})
				.catch((e: any) => {
					console.log(e.response)
					dispatch(setIsRequesting(false))
					dispatch(
						setBulkSubmitData({
							title: "Error",
							description: "Oops, something went wrong.",
							success: false,
							error: true,
						}),
					)
				})
		} catch (error) {}
	}

/**
 * Handle API Call for Input Validation
 * @param values @type object
 * <AUTHOR>
 * @action POST
 * @url `/franchise/validate`
 * @body { "client_id":"test123" }
 */
export const validateInput =
	(values: any): AppThunk =>
	//@ts-ignore
	async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let token = getState().login.token
			dispatch(setIsValidating(true))
			const promise = validateFranchiseInput(values, token)
			promise
				.then((a: any) => {
					dispatch(setIsValidating(false))
					dispatch(setInputValidation({ valid: true, label: "" }))
				})
				.catch((e: any) => {
					console.log("e.response", e.response)
					if (e.response) {
						switch (e.response.status) {
							case 400:
								console.log("e.response")
								dispatch(
									setInputValidation({ valid: false, label: e.response.data }),
								)
								break
							case 500:
								dispatch(
									setInputValidation({ valid: false, label: e.response.data }),
								)
								break
							case 404:
								dispatch(setInputValidation({ valid: false, label: 'Client ID/TIN does not match. Please check it again'}));
								break
							default:
								dispatch(
									setInputValidation({
										valid: false,
										label: "Invalid Client Id",
									}),
								)
								break
						}
					} else {
						dispatch(
							setModalContent({
								title: "Error",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					}
					dispatch(setIsValidating(false))
				})
		} catch (error) {}
	}

	/**
 * Handle API Call for Input Validation
 * @param values @type object
 * <AUTHOR>
 * @action POST
 * @url `/franchise/validate`
 * @body { "tin_id":"test123" }
 */
export const validateInputTINId =
	(values: any): AppThunk =>
	//@ts-ignore
	async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let token = getState().login.token
			// let isTaxNumberValid ={ 
			// 	valid: true,
			// 	inputClass: "",
			// 	spanClass: "",
			// 	text: "",
			// };
			dispatch(setIsValidating(true))
			const promise = validateFranchiseInpuTIN(values, token)
			promise
				.then((a: any) => {
					console.log('AAAAA', a);
					dispatch(setIsValidating(false))
					dispatch(setInputValidation({ valid: true, label: "", id: "tin_id" }))
				})
				.catch((e: any) => {
					console.log("e.response", e.response)
					dispatch(setIsValidating(false))
					if (e.response && e.response.status) {
						switch (e.response.status) {
							case 200:
								dispatch(setInputValidation({ valid: true, label: "", id: "tin_id"}))
								break;
							case 400:
								console.log('responsebhie', e.response);
								dispatch(setInputValidation({ valid: false, label: "Client already exists. Please enter a different TIN or Client ID.", id: "tin_id"}))
								// dispatch(
								// 	setModalContent({
								// 		title: "Request already in process ",
								// 		p: "Sorry, this franchise request is already being processed.",
								// 		error: true,
								// 	}),
								// )
								break;
							case 404:
								dispatch(setInputValidation({ valid: false, label: 'Client ID/TIN does not match. Please check it again', id: "tin_id"}));
								break;
						}
					} else {
						dispatch(
							setModalContent({
								title: "Error",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					}
				})
		} catch (error) {}
	}

/**
 * Handle API Call for Update Franchise Request Due Date - Return Status
 * @param id
 * @param dueDate
 * <AUTHOR>
 * @action PATCH
 * @url `/franchise/validator/{id}/duedate`
 * @body { "return_due_date":"date" }
 */

export const updateFranchiseDueDate =
	(id: string, dueDate: Date): AppThunk =>
	//@ts-ignore
	async (dispatch: AppDispatch, getState: () => RootState) => {
		let token = getState().login.token
		let param = { return_due_date: dueDate }
		dispatch(setIsRequesting(true))
		dispatch(setIsUpdateDueExpiry(true))
		console.log("Parameters: ", id, param)
		const response = updateDueDate(id, param, token)
		return await response
			.then((a: any) => {
				console.log("then", a)
				dispatch(setIsRequesting(false))
				dispatch(setIsUpdateDueExpiry(false))
				if (a) {
					// @ts-ignore
					dispatch(fetchFranchise(id))
					// @ts-ignore
					dispatch(fetchFranchises())
					dispatch(
						setModalContent({
							title: "",
							p: "",
							error: false,
						}),
					)
				}
			})
			.catch(function (e) {
				console.log("catch", e)
				dispatch(setIsRequesting(false))
				if (e.response) {
					switch (e.response.status) {
						case 500:
							dispatch(
								setModalContent({
									title: "Error",
									p: "Oops, something went wrong.",
									error: true,
								}),
							)
							break
						case 401:
							console.log("unauthorized user")
							dispatch(setSessionExpired(true))
							break

						default:
							dispatch(
								setModalContent({
									title: "Error",
									p: "Oops, something went wrong.",
									error: true,
								}),
							)
							break
					}
				} else {
					dispatch(
						setModalContent({
							title: "Error",
							p: "Oops, something went wrong.",
							error: true,
						}),
					)
				}
			})
	}

/**
 * Handle API Call for Update Franchise Request Expiry Date - Approved status, Non Active Account
 * @param id
 * @param expiryDate
 * <AUTHOR>
 * @action PATCH
 * @url ` /franchise/manager/{id}/expirydate`
 * @body { "expiry_date":"date" }
 */
export const updateFranchiseExpiryDate =
	(id: string, expiryDate: Date): AppThunk =>
	//@ts-ignore
	async (dispatch: AppDispatch, getState: () => RootState) => {
		let token = getState().login.token
		let param = { expiry_date: expiryDate }
		dispatch(setIsRequesting(true))
		dispatch(setIsUpdateDueExpiry(true))
		 return await updateExpiryDate(id, param, token).then((a: any) => {
				console.log("then", a)
				dispatch(setIsRequesting(false))
				dispatch(setIsUpdateDueExpiry(false))
				if (a) {
					// @ts-ignore
					dispatch(fetchFranchise(id))
					// @ts-ignore
					dispatch(fetchFranchises())
					dispatch(
						setModalContent({
							title: "",
							p: "",
							error: false,
						}),
					)
				}
			})
			.catch(function (e) {
				console.log("catch", e)
				dispatch(setIsRequesting(false))
				if (e.response) {
					switch (e.response.status) {
						case 500:
							dispatch(
								setModalContent({
									title: "Error",
									p: "Oops, something went wrong.",
									error: true,
								}),
							)
							break
						case 401:
							console.log("unauthorized user")
							dispatch(setSessionExpired(true))
							break

						default:
							dispatch(
								setModalContent({
									title: "Error",
									p: "Oops, something went wrong.",
									error: true,
								}),
							)
							break
					}
				} else {
					dispatch(
						setModalContent({
							title: "Error",
							p: "Oops, something went wrong.",
							error: true,
						}),
					)
				}
			})
	}

/**
 * Handle API Call for UPDATE Franchise Details
 * @param values franchise object
 * <AUTHOR>
 * @action PATCH
 * @url `/franchise/${id}`
 * @body franchise object
 *  */
export const patchFranchiseRequest =
	(values: any): AppThunk =>
	//@ts-ignore
	(dispatch: AppDispatch, getState: () => RootState) => {
		let token = getState().login.token
		let action = values.status
		let id = values._id
		dispatch(setIsRequesting(true))
		const response = updateRequestResubmitted(id, values, token)
		response
			.then((a: any) => {
				dispatch(setIsRequesting(false))
				if (a === undefined) {
					dispatch(
						setModalContent({
							title: "Error",
							p: "Oops, something went wrong.",
							error: true,
						}),
					)
				} else {
					dispatch(resetUnsavedChanges())
					dispatch(
						setModalContent({
							title: "Request Updated",
							p: "Franchise Request is updated.",
							error: false,
						}),
					)
				}
			})
			.catch(function (e) {
				dispatch(setIsRequesting(false))
				if (e.response) {
					switch (e.response.status) {
						case 500:
							dispatch(
								setModalContent({
									title: "Error",
									p: "Oops, something went wrong.",
									error: true,
								}),
							)
							break
						case 422:
							dispatch(
								setModalContent({
									title: "Error",
									p: "Oops, something went wrong.",
									error: true,
								}),
							)
							break
						case 400:
							if (action === "RESUBMITTED") {
								if (
									e.response.data &&
									e.response.data.errors &&
									typeof e.response.data.errors == "string"
								) {
									dispatch(
										setModalContent({
											title: "Request already in process ",
											p: "Sorry, there is already a franchise request for the same Company.",
											error: true,
										}),
									)
								} else {
									if (
										e.response.data.errors.length > 0 &&
										e.response.data.errors[0].msg !== "Server Error"
									) {
										dispatch(
											setModalContent({
												title: "Request already exist",
												p: "There is already an active franchise for the same company.",
												error: true,
											}),
										)
									} else {
										dispatch(
											setModalContent({
												title: "Error Saving",
												p: "Oops, something went wrong.",
												error: true,
											}),
										)
									}
								}
							} else {
								if (
									e.response.data &&
									e.response.data.errors &&
									typeof e.response.data.errors == "string"
								) {
									dispatch(
										setModalContent({
											title: "Request already in process ",
											p: "Sorry, there is already a franchise request for the same Company.",
											error: true,
										}),
									)
								} else {
									if (
										e.response.data.errors.length > 0 &&
										e.response.data.errors[0].msg !== "Server Error"
									) {
										dispatch(
											setModalContent({
												title: "Request already exist",
												p: "There is already an active franchise for the same company.",
												error: true,
											}),
										)
									} else {
										dispatch(
											setModalContent({
												title: "Error Saving",
												p: "Oops, something went wrong.",
												error: true,
											}),
										)
									}
								}
							}
							break
						case 401:
							console.log("unauthorized user")
							dispatch(setSessionExpired(true))
							break
						default:
							dispatch(
								setModalContent({
									title: "Error",
									p: "Oops, something went wrong.",
									error: true,
								}),
							)
							break
					}
				} else {
					dispatch(
						setModalContent({
							title: "Error",
							p: "Oops, something went wrong.",
							error: true,
						}),
					)
				}
			})
	}

// Get Industry List
export const fetchIndustryList =
//@ts-ignore
	(): AppThunk => async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let roles = getState().login.userRoles
			let token = getState().login.token
			let role = roles.isSalesUser ? 'encoder' : getState().login.roleAPI
			// console.log("RESULT SUCCESS: ", role, token)
			const response = getIndustryList(role, token)
			// console.log("RESULT SUCCESS2: ", response)
			response
				.then((a: any) => {
					dispatch(getIndustryListData(a.data))
					// console.log("RESULT SUCCESS: ", a.data)
				})
				.catch(function (e) {
					console.log("catch", e)
					console.log("RESULT ERROR: ", e)
					if (e.response && e.response.status) {
						switch (e.response.status) {
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
						// dispatch(setIndustryListError(e.response.data.errors));
					}
				})
		} catch (err) {
			console.log(err)
		}
	}

// Get Location Regions
export const fetchLocationRegions =
//@ts-ignore
	(): AppThunk => async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let token = getState().login.token
			let role = getState().login.roleAPI
			// console.log("RESULT SUCCESS: ", role, token)
			const response = getRegionList(role, token)
			// console.log("RESULT SUCCESS2: ", response)
			response
				.then((a: any) => {
					dispatch(getLocationRegions(a.data.body))
					// console.log("RESULT SUCCESS: ", a.data)
				})
				.catch(function (e) {
					console.log("catch", e)
					console.log("RESULT ERROR: ", e)
					if (e.response && e.response.status) {
						switch (e.response.status) {
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
					}
				})
		} catch (err) {
			console.log(err)
		}
	}

// Get Location Regions
export const fetchLocationProvince =
//@ts-ignore
	(): AppThunk => async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let token = getState().login.token
			let role = getState().login.roleAPI
			// console.log("RESULT SUCCESS: ", role, token)
			const response = getProvinceList(role, token)
			// console.log("RESULT SUCCESS2: ", response)
			response
				.then((a: any) => {
					dispatch(getLocationProvinces(a.data.body))
					// console.log("RESULT SUCCESS: ", a.data)
				})
				.catch(function (e) {
					console.log("catch", e)
					console.log("RESULT ERROR: ", e)
					if (e.response && e.response.status) {
						switch (e.response.status) {
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
					} else {
						dispatch(
							setModalContent({
								title: "Error",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					}
				})
		} catch (err) {
			console.log(err)
		}
	}

// Get Cities List
export const fetchLocationCities =
//@ts-ignore
	(): AppThunk => async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let token = getState().login.token
			let role = getState().login.roleAPI
			// console.log("RESULT SUCCESS: ", role, token)
			const response = getCitiesList(role, token)
			// console.log("RESULT SUCCESS2: ", response)
			response
				.then((a: any) => {
					dispatch(getLocationCities(a.data.body))
					// console.log("RESULT SUCCESS: ", a.data)
				})
				.catch(function (e) {
					console.log("catch", e)
					console.log("RESULT ERROR: ", e)
					if (e.response && e.response.status) {
						switch (e.response.status) {
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
					} else {
						dispatch(
							setModalContent({
								title: "Error",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					}
				})
		} catch (err) {
			console.log(err)
		}
	}

// Get Barangay List
export const fetchLocationBrgys =
	(city: any, region: any, province: any): AppThunk =>
	//@ts-ignore
	async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let token = getState().login.token
			let role = getState().login.roleAPI
			// console.log("RESULT SUCCESS: ", role, token)
			const response = getBarangayList(role, token, city, region, province)
			response
				.then((a: any) => {
					// const filterBrgys = a.data.body.filter(
					// 	(item: any) => item.status === "active" && item.city === city,
					// )
					dispatch(getLocationBrgys(a.data.body));
					// dispatch(getLocationBrgys(filterBrgys))
					// console.log("RESULT SUCCESS: ", a.data)
					dispatch(setIsFetchingList(true))
				})
				.catch(function (e) {
					console.log("catch", e)
					console.log("RESULT ERROR: ", e)
					if (e.response && e.response.status) {
						switch (e.response.status) {
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
					} else {
						dispatch(
							setModalContent({
								title: "Error",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					}
					dispatch(setIsFetchingList(true))
				})
		} catch (err) {
			console.log(err)
		}
	}

// Get Mother Company List
export const fetchMotherCompanyList =
//@ts-ignore
	(): AppThunk => async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let roles = getState().login.userRoles
			let token = getState().login.token
			let role = roles.isSalesUser ? 'encoder' : getState().login.roleAPI
			const response = getMotherCompanyList(role, token)
			response
				.then((a: any) => {
					dispatch(getMotherCompanyListData(a.data))
				})
				.catch(function (e) {
					console.log("catch", e)
					console.log("RESULT ERROR: ", e)
					if (e.response && e.response.status) {
						switch (e.response.status) {
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
					}
				})
		} catch (err) {
			console.log(err)
		}
	}

// // Get Cities List
// export const fetchLocationBrgys = (city:any): AppThunk => async (
//   dispatch: AppDispatch,
//   getState: () => RootState
// ) => {
//   try {
//     let token = getState().login.token;
//     let role = getState().login.roleAPI;
//     dispatch(setIsRequesting(true))
//     console.log("RESULT SUCCESS: ", role, token)
//     const response = getBrgyList(role, token);
//     console.log("RESULT SUCCESS2: ", response)

//     response
//       .then((a: any) => {
//           let filterBrgys = a.data.body.filter((item:any)=> item.city === city && item.status === "active")
//           // dispatch(getLocationBrgys(a.data.body));
//           dispatch(getLocationBrgys(filterBrgys));
//           console.log("RESULT SUCCESS: ", a.data)
//           dispatch(setIsRequesting(false))
//           dispatch(setIsFetchingList(true))
//       })
//       .catch(function (e) {
//         console.log("catch", e);
//         console.log("RESULT ERROR: ", e)
//         if (e.response && e.response.status) {
//           switch (e.response.status) {
//             case 500:
//               console.log("Oops, something went wrong.");
//               dispatch(
//                 setModalContent({
//                   title: "",
//                   p: "Oops, something went wrong.",
//                   error: true,
//                 })
//               );
//               break;
//           }
//         }
//         dispatch(setIsRequesting(false))
//         dispatch(setIsFetchingList(true))
//       });
//   } catch (err) {

//     console.log(err);
//   }
// };

// Get BDO List
export const fetchBDOUsers =
//@ts-ignore
	(_role?: string): AppThunk => async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let token = getState().login.token
			let role = getState().login.roleAPI;

			const response = getBdoUserList(role === 'abt_maintainer' ? 'encoder' : _role ? _role : role, token)
			response
				.then((a: any) => {
					const filterUsers = a.data.map((user: any) => (
						{
							...user,
							user_id: user._id,
							full_name: user.full_name,
						}))
					dispatch(getBdoUserListData(filterUsers))
					// console.log("FILTERED BDO RESULT SUCCESS: ", filterUsers)
					dispatch(setIsFetchingList(true))
				})
				.catch(function (e) {
					if (e.response && e.response.status) {
						switch (e.response.status) {
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
					} else {
						dispatch(
							setModalContent({
								title: "Error",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					}
					dispatch(setIsFetchingList(true))
				})
		} catch (err) {
			console.log(err)
		}
	}

// Get BDO List by Sales head
export const fetchBDOUsersRaw =
//@ts-ignore
	(): AppThunk => async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let token = getState().login.token
			let role = getState().login.roleAPI;
			
			const response = getBdoUserList(role === 'abt_maintainer' ? 'encoder' : role, token)
			
			 response.then((a: any) => {
					dispatch(getBdoUserListData(a.data))
					dispatch(setIsFetchingList(true))
				})
				.catch(function (e) {
					if (e.response && e.response.status) {
						switch (e.response.status) {
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
					} else {
						dispatch(
							setModalContent({
								title: "Error",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					}
					dispatch(setIsFetchingList(true))
				})
		} catch (err) { console.log(err) }
	}

//@ts-ignore
export const submitRefranchiseRequest = (id: any, body: any): AppThunk => async (dispatch: AppDispatch, getState: () => RootState) => {
	try {
		let token = getState().login.token
		// let role = getState().login.roleAPI
		let userId = getState().login.user_id

		const response = submitRefranchise(id, { requested_by: userId, ...body}, token)
		response.then(res => { 
			dispatch(setIsFetchingList(true))
			if(res.data.ok === false){
				dispatch(
					setModalContent({
						title: "Request Failed",
						p: `${res.data.error}`,
						error: true,
				}))
			}

			if(res.data.ok === true){
				dispatch(
					setModalContent({
						title: "Success",
						p: "Request for reassignment of Franchise will be sent to Underwriting for approval.",
						error: false,
				}))
			}
		})
	} catch(e) {
		console.log(e)
	}
}

// export const validateToken = (body: any): AppThunk => async (dispatch: AppDispatch, getState: () => RootState) => {
// 	try {
// 		let token = getState().login.token

// 		const response = checkToken({ access_token: token}, token)
// 		response.then(res => {
// 			console.log(`resp token ${token}`, res);
// 		})
// 	} catch (e) {
// 		console.log(e)
// 	}
// }

export default franchiseSlice.reducer
