import { createSlice, } from "@reduxjs/toolkit";
import { AppThunk, AppDispatch } from "../../store";
import { RootState } from "./rootReducer";
import { ReasonObj } from "../models/Maintenance";

import {
  getReturnReason, getDisapproveReason, saveReason, deleteReason, updateReason
} from "../../services/MaintenanceService";

import { setIsRequesting } from "./FranchiseSlice"
import { setSessionExpired } from "./LoginSlice";

export const maintenanceSlice = createSlice({
  name: "maintenance",
  initialState: {
    returnReasonList: [],
    disapproveReasonList: [],
    error: false,
    isSavingLoading: false,
    isAddingNewItem: false,
    reasonDesc: "",
    selectedID: "",
    success: false,
    errorDetails: {},
    modalContent: {
      title: "",
      desc: "",
      error: false,
    },
    successDetails: {},
    isAddSuccess: false,
    isReturnEdit: false,
    returnEditID: "",
    isDisapproveEdit: false,
    disapproveEditID: "",
    isEditSuccess: false,
    isDeleteSuccess: false,
    isReasonExist: false,
    returnInputs:"",
    disapproveInputs:""
  },
  reducers: {
    getReturnReasonListData: (state, action) => {
      state.returnReasonList = action.payload;
      state.error = false;
    },
    getDisapproveReasonListData: (state, action) => {
      state.disapproveReasonList = action.payload;
      state.error = false;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    setIsDeleteSuccess: (state, action) => {
      state.isDeleteSuccess = action.payload;
    },
    setIsReasonExist: (state, action) => {
      state.isReasonExist = action.payload;
    },
    setIsEditSuccess: (state, action) => {
      state.isEditSuccess = action.payload;
    },
    setIsAddSuccess: (state, action) => {
      state.isAddSuccess = action.payload;
    },
    setReasonError: (state, action) => {
      state.error = true;
      state.errorDetails = action.payload;
      state.success = false;
      state.successDetails = {};
    },
    setModalContent: (state, action) => {
      state.modalContent.title = action.payload.title;
      state.modalContent.desc = action.payload.desc;
      state.modalContent.error = action.payload.error;
    },
    setSaveLoading: (state, {payload}: any) => {
      state.isSavingLoading = payload;
    },
    setSelectedID: (state, action) => {
      state.selectedID = action.payload;
    },
    setAddingNewItem: (state, action) => {
      state.isAddingNewItem = action.payload;
    },
    setReasonDesc: (state, action) => {
      state.reasonDesc = action.payload;
    },
    setIsReturnEdit: (state, action) => {
      state.isReturnEdit = action.payload;
    },
    setReturnEditID: (state, action) => {
      state.returnEditID = action.payload;
    },
    setIsDisapproveEdit: (state, action) => {
      state.isDisapproveEdit = action.payload;
    },
    setDisapproveEditID: (state, action) => {
      state.disapproveEditID = action.payload;
    },
    setReturnInputs: (state, action) => {
      state.returnInputs = action.payload;
    },
    setDisapproveInputs: (state, action) => {
      state.disapproveInputs = action.payload;
    },
    clearModalContent: (state) => {
      state.modalContent = {
        title: "",
        desc: "",
        error: false,
      };
    }
  }
});

export const {
  getReturnReasonListData,
  getDisapproveReasonListData,
  setError,
  setReasonError,
  setModalContent,
  setSaveLoading,
  clearModalContent,
  setSelectedID,
  setAddingNewItem,
  setReasonDesc,
  setIsAddSuccess,
  setIsReturnEdit,
  setReturnEditID,
  setIsDisapproveEdit,
  setDisapproveEditID,
  setIsEditSuccess,
  setIsDeleteSuccess,
  setIsReasonExist,
  setReturnInputs,
  setDisapproveInputs
} = maintenanceSlice.actions;


/** Save new Reason for Return **/
// @ts-ignore
export const saveNewReason = (newReason: ReasonObj): AppThunk => (
  dispatch: AppDispatch,
  getState: () => RootState
) => {
  try {
    dispatch(setIsRequesting(true))
    let token = getState().login.token;
    let reason = getState().maintenance.reasonDesc;
    const response = saveReason(newReason, token, reason);
    response
      .then((a: any) => {
        dispatch(setIsRequesting(false))
        dispatch(setIsAddSuccess(true))
        dispatch(
          setModalContent({
            title: "Reason Added",
            desc: "New reason has been successfully added to the list.",
            error: false,
          })
        );
        if (reason === "return") {
          //@ts-ignore
          dispatch(fetchReturnReasonList());
              dispatch(setReturnInputs(""));
        } else {
          //@ts-ignore
          dispatch(fetchDisapproveReasonList());
              dispatch(setDisapproveInputs(""));
        }
      })
      .catch(function (e) {
        dispatch(setIsRequesting(false))
        if (e.response) {
          switch (e.response.status) {
            case 400:
              dispatch(
                setModalContent({
                  title: "Reason Already Exists",
                  desc:
                    "The reason that you want to add already exists in the list.",
                  error: true,
                })
              );
              break;
            case 500:
              dispatch(
                setModalContent({
                  title: "Error Saving",
                  desc: "Oops, something went wrong.",
                  error: true,
                })
              );
              break;
            case 401:
							dispatch(setSessionExpired(true))
              break;
            default:
              break;
          }
          dispatch(setReasonError(e.response.data.errors));
          if (reason === "return") {
            //@ts-ignore
            dispatch(fetchReturnReasonList());
          } else {
            //@ts-ignore
            dispatch(fetchDisapproveReasonList());
          }
        } else {
          dispatch(
            setModalContent({
              title: "Error",
              desc: "Oops, something went wrong.",
              error: true,
            })
          );
        }
      });
  } catch (err) {
  }
};


// Get Reason List
// @ts-ignore
export const fetchReturnReasonList = (): AppThunk => async (
  dispatch: AppDispatch,
  getState: () => RootState
) => {
  try {
    dispatch(setIsRequesting(true))
    let token = getState().login.token;
    let role = getState().login.roleAPI;
    const response = getReturnReason(role, token);
    response
      .then((a: any) => {
        dispatch(getReturnReasonListData(a.data));
        dispatch(setIsRequesting(false))
      })
      .catch(function (e: any) {
        dispatch(setIsRequesting(false))
        if (e.response) {
          switch (e.response.status) {
            case 401:
							dispatch(setSessionExpired(true))
              break;
            default:
              break;
          }
        } 
      });
  } catch (err) {
  }
};
// @ts-ignore
export const fetchDisapproveReasonList = (): AppThunk => async (
  dispatch: AppDispatch,
  getState: () => RootState
) => {
  try {
    dispatch(setIsRequesting(true))
    let token = getState().login.token;
    let role = getState().login.roleAPI;
    const response = getDisapproveReason(role, token);
    response
      .then((a: any) => {
        dispatch(getDisapproveReasonListData(a.data));
        dispatch(setIsRequesting(false))
      })
      .catch(function (e: any) {
        dispatch(setIsRequesting(false))
        if (e.response) {
          switch (e.response.status) {
            case 401:
							dispatch(setSessionExpired(true))
              break;
            default:
              break;
          }
        }
      });
  } catch (err) {}
};

// Delete Return Reason
// @ts-ignore
export const deleteReasonItem = (_id: string): AppThunk => (
  dispatch: AppDispatch,
  getState: () => RootState
) => {
  try {
    dispatch(setIsRequesting(true))
    let token = getState().login.token;
    let reason = getState().maintenance.reasonDesc;
    const response = deleteReason(_id, token, reason);
    response
      .then((a: any) => {
        dispatch(setIsRequesting(false))
        dispatch(setSelectedID("")) 
        dispatch(
          setModalContent({
            title: "Reason Removed",
            desc: "You have successfully removed the reason from the list.",
            error: false,
          })
        );
        dispatch(setIsDeleteSuccess(true))
        if (reason === "return") {
          //@ts-ignore
          dispatch(fetchReturnReasonList());
        } else {
          //@ts-ignore
          dispatch(fetchDisapproveReasonList());
        }
      })
      .catch(function (e: any) {
        dispatch(setIsRequesting(false))
        dispatch(setSelectedID("")) 
        dispatch(
          setModalContent({
            title: "Error",
            desc: "Oops, something went wrong.",
            error: true,
          })
        );
      });
  } catch (err) {}
};


/** Update Reason **/
// @ts-ignore
export const updateReasonItem = (updatedReason: ReasonObj): AppThunk => (
  dispatch: AppDispatch,
  getState: () => RootState
) => {
  try {
    dispatch(setIsRequesting(true))
    let token = getState().login.token;
    let reason = getState().maintenance.reasonDesc;
    const response = updateReason(updatedReason, token, reason);
    response
      .then((a: any) => {
        dispatch(setIsRequesting(false))
        dispatch(
          setModalContent({
            title: "Reason Updated",
            desc: "You have successfully updated the reason.",
            error: false,
          })
        );
        dispatch(setReturnEditID(""))
        dispatch(setIsEditSuccess(true))
        if (reason === "return") {
          //@ts-ignore
          dispatch(fetchReturnReasonList());
        } else {
          //@ts-ignore
          dispatch(fetchDisapproveReasonList());
        }
      })
      .catch(function (e) {
        dispatch(setIsRequesting(false))
        dispatch(setIsEditSuccess(false))
        if (e.response) {
          switch (e.response.status) {
            case 400:
              dispatch(
                setModalContent({
                  title: "Reason Already Exists",
                  desc:
                    "The reason that you want to add already exists in the list.",
                  error: true,
                })
              );
              break;
            case 500:
              dispatch(
                setModalContent({
                  title: "Error Saving",
                  desc: "Oops, something went wrong.",
                  error: true,
                })
              );
              break;
            case 401:
              dispatch(setSessionExpired(true))
              break;
            default:
              break;
          }
          dispatch(setReasonError(e.response.data.errors));
          if (reason === "return") {
            //@ts-ignore
            dispatch(fetchReturnReasonList());
          } else {
            //@ts-ignore
            dispatch(fetchDisapproveReasonList());
          }
        } else {
          dispatch(
            setModalContent({
              title: "Error",
              desc: "Oops, something went wrong.",
              error: true,
            })
          );
        }
      });
  } catch (err) {}
};

export default maintenanceSlice.reducer;
