import { createSlice, PayloadAction } from "@reduxjs/toolkit"
import { Contact } from "../models/Contact"
import { AppThunk, AppDispatch } from "../../store"
import { RootState } from "./rootReducer"

import {
	getContacts,
	createContactService,
	getContact,
	updateContactStatus,
	updateContact,
	getProvinceList,
	getCitiesList,
	getBrgyList,
	getOfficeLocationList,
} from "../../services/ContactService"
import { setSessionExpired } from "./LoginSlice"

export const contactSlice = createSlice({
	name: "contact",
	initialState: {
		activeFilterIndex: 0,
		activeFilterName: "All",
		activeStatusFilterIndex: 0,
		activeStatusFilterName: "All",
		isView: false,
		isViewLoading: false,
		details: {} as Contact,
		isNewInputs: false,
		isCreate: false,
		isEdit: false,
		isEditLoading: false,
		isSearching: false,
		new: {} as Contact,
		validationModalContent: {
			title: "",
			p: "",
			error: false,
		},
		contactList: [],
		locationListData: {
			regions: [],
			provinces: [],
			cities: [],
			brgy: [],
		},
		officeLocationList: [],
		isRequesting: false,
		isFetchingList: false,
	},
	reducers: {
		resetState: (state) => {
			state.new = {} as Contact
			state.isView = false
			state.isViewLoading = false
			state.details = {} as Contact
			state.isSearching = false
			state.isNewInputs = false
			state.isCreate = false
			state.isEdit = false
			state.isEditLoading = false
			state.validationModalContent = {
				title: "",
				p: "",
				error: false,
			}
		},
		changeFilter: (state, action) => {
			state.activeFilterIndex = action.payload.filterIndex
			state.activeFilterName = action.payload.name
		},
		changeStatusFilter: (state, action) => {
			state.activeStatusFilterIndex = action.payload.filterStatusIndex
			state.activeStatusFilterName = action.payload.statusName
		},
		clearValidationModalContent: (state) => {
			state.validationModalContent = {
				title: "",
				p: "",
				error: false,
			}
		},
		goBackAndCreateNew: (state) => {
			state.new = {} as Contact
		},
		setContactData: (state, { payload }: PayloadAction<Contact>) => {
			state.new = payload
		},
		setUserInputValues: (state, { payload }: any) => {
			state.isNewInputs = payload
		},
		setViewLoading: (state, { payload }: any) => {
			state.isViewLoading = payload
		},
		getContactData: (state, { payload }: PayloadAction<Contact>) => {			
			state.details = payload
			state.isViewLoading = false
			state.isView = true
		},
		setEditLoading: (state, { payload }: any) => {
			state.isEditLoading = payload
		},
		editContactData: (state, { payload }: PayloadAction<Contact>) => {
			state.new = payload
			state.isEditLoading = false
			state.isEdit = true
		},
		setIsCreate: (state, action) => {
			state.isCreate = action.payload
		},
		setModalContent: (state, action) => {
			state.validationModalContent.title = action.payload.title
			state.validationModalContent.p = action.payload.p
			state.validationModalContent.error = action.payload.error
		},
		getContactListData: (state, action) => {
			state.contactList = action.payload
		},
		getLocationRegions: (state, action) => {
			state.locationListData.regions = action.payload
		},
		getLocationProvinces: (state, action) => {
			state.locationListData.provinces = action.payload
		},
		getLocationCities: (state, action) => {
			state.locationListData.cities = action.payload
		},
		getLocationBrgys: (state, action) => {
			state.locationListData.brgy = action.payload
		},
		getOfficeLocations: (state, action) => {
			state.officeLocationList = action.payload
		},
		setIsSearching: (state, action) => {
			state.isSearching = action.payload
		},
		setIsRequesting: (state, action) => {
			state.isRequesting = action.payload
		},
		setIsFetchingList: (state, action) => {
			state.isFetchingList = action.payload
		},
		resetUnsavedChanges: (state) => {
			state.isNewInputs = false
		},
	},
})

export const {
	resetState,
	changeFilter,
	changeStatusFilter,
	clearValidationModalContent,
	goBackAndCreateNew,
	setContactData,
	setEditLoading,
	editContactData,
	setIsCreate,
	setUserInputValues,
	setViewLoading,
	getContactData,
	getContactListData,
	getLocationRegions,
	getLocationProvinces,
	getLocationCities,
	getLocationBrgys,
	getOfficeLocations,
	setIsSearching,
	setModalContent,
	setIsRequesting,
	setIsFetchingList,
	resetUnsavedChanges,
} = contactSlice.actions

// Submit Contact Details
export const addContact =
	(newContact: Contact): AppThunk =>
	//@ts-ignore
	(dispatch: AppDispatch, getState: () => RootState) => {
		let token = getState().login.token
		dispatch(setIsRequesting(true))
		const response = createContactService(newContact, token)
		response
			.then((a: any) => {
				dispatch(setIsRequesting(false))
				dispatch(resetUnsavedChanges())
				dispatch(
					setModalContent({
						title: "Contact Created",
						p: `${a.data.data.type} contact has been successfully added`,
						error: false,
					}),
				)
				dispatch(setIsCreate(false))
				//@ts-ignore
				dispatch(fetchContacts())
			})
			.catch(function (e) {
				console.log(e.response)
				dispatch(setIsRequesting(false))
				if (e.response) {
					switch (e.response.status) {
						case 400:
							dispatch(
								setModalContent({
									title: "Error",
									p: "Oops, something went wrong.",
									error: true,
								}),
							)
							break
						case 500:
							console.log("Oops, something went wrong.")
							dispatch(
								setModalContent({
									title: "Error",
									p: "Oops, something went wrong.",
									error: true,
								}),
							)
							break
						case 401:
							console.log("unauthorized user")
							dispatch(setSessionExpired(true))
							break
						default:
							dispatch(
								setModalContent({
									title: "Error",
									p: "Oops, something went wrong.",
									error: true,
								}),
							)
							break
					}
				} else {
					dispatch(
						setModalContent({
							title: "Error",
							p: "Oops, something went wrong.",
							error: true,
						}),
					)
				}
			})
	}

// Get Contact Details to Edit
export const editContact =
	(id: any): AppThunk =>
	// @ts-ignore
	async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			//@ts-ignore
			dispatch(setEditLoading(true))
			let token = getState().login.token
			let role = getState().login.roleAPI
			dispatch(setIsRequesting(true))
			const response = getContact(id, role, token)
			response
				.then((a: any) => {
					dispatch(setIsRequesting(false))
					var data = a.data
					dispatch(editContactData(data))
				})
				.catch(function (e) {
					console.log("Error")
					dispatch(setIsRequesting(false))
					if (e.response) {
						switch (e.response.status) {
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
							case 401:
								console.log("unauthorized user")
								
							dispatch(setSessionExpired(true))
								break
							default:
								dispatch(
									setModalContent({
										title: "Error",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
						//@ts-ignore
						dispatch(setEditLoading(false))
					} else {
						dispatch(
							setModalContent({
								title: "Error",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					}
				})
			dispatch(setIsFetchingList(false))
		} catch (err) {
			dispatch(setIsRequesting(false))
			dispatch(setIsFetchingList(false))
			console.log(err)
		}
	}

// Get Contact Details
export const fetchContact =
	(id: string): AppThunk =>
	// @ts-ignore
	async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			dispatch(setIsRequesting(true))
			//@ts-ignore
			dispatch(setViewLoading(true))
			let token = getState().login.token
			let role = getState().login.roleAPI

			const response = getContact(id, role, token)
			response
				.then((a: any) => {
					dispatch(setIsRequesting(false))
					//@ts-ignore
					dispatch(setViewLoading(false))
					console.log("view details result", a)
					if (a !== undefined) {
						var data = a.data						
						dispatch(getContactData(data))
					}
				})
				.catch(function (e) {
					console.log("Error", e)
					dispatch(setIsRequesting(false))
					//@ts-ignore
					dispatch(setViewLoading(false))
					console.log("Error")
					if (e.response) {
						switch (e.response.status) {
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
					}
				})
		} catch (err) {
			dispatch(setIsRequesting(false))
			console.log(err)
		}
	}

// Get Contact List

export const fetchContacts =
	// @ts-ignore
	(): AppThunk => async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let token = getState().login.token
			let role = getState().login.roleAPI
			dispatch(setIsRequesting(true))
			const response = getContacts(role, token)
			response
				.then((a: any) => {
					dispatch(setIsRequesting(false))
					if (a === undefined) {
						console.log("error")
					} else {
						dispatch(getContactListData(a.data))
					}
				})
				.catch(function (e) {
					console.log("catch", e)
					dispatch(setIsRequesting(false))
					if (e.response && e.response.status) {
						switch (e.response.status) {
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
					}
				})
		} catch (err) {
			dispatch(setIsRequesting(false))
			console.log(err)
		}
	}

// Update Contact Status Request
export const updateContactRequestStatus =
	(id: string, action: string, values?: any): AppThunk =>
	// @ts-ignore
	(dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let token = getState().login.token
			let data = values ? values : {}

			dispatch(setIsRequesting(true))
			const response = updateContactStatus(id, action, data, token)
			response
				.then((a: any) => {
					dispatch(setIsRequesting(false))
					if (a === undefined) {
						console.log("error")
						dispatch(
							setModalContent({
								title: "Error",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					} else {
						dispatch(resetUnsavedChanges())
						switch (action) {
							case "deactivate":
								dispatch(
									setModalContent({
										title: "Contact Deactivated",
										p:`${values.type} contact has been successfully deactivated.`,
										error: false,
									}),
								)
								break
							case "activate":
								dispatch(
									setModalContent({
										title: "Contact Activated",
										p:`${values.type} contact has been successfully activated.`,
										error: false,
									}),
								)								
								break
						}
						//@ts-ignore
						dispatch(fetchContacts())
					}
				})
				.catch(function (e: any) {
					dispatch(setIsRequesting(false))
					console.log("catch", e)
					if (e.response) {
						switch (e.response.status) {				
							case 401:
								console.log("unauthorized user")
							dispatch(setSessionExpired(true))
								break
							case 400:
								dispatch(
									setModalContent({
										title: "Request already processed",
										p: `Sorry, this contact was already ${action}d.`,
										error: true,
									}),
								)
								break
							default:
								dispatch(
									setModalContent({
										title: "Error",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
					} else {
						dispatch(
							setModalContent({
								title: "Error",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					}
				})
		} catch (err) {
			dispatch(setIsRequesting(false))
			console.log(err)
		}
	}

// UPDATE Contact Details Request
export const patchContactRequest =
	(values: any): AppThunk =>
	// @ts-ignore
	(dispatch: AppDispatch, getState: () => RootState) => {
		let token = getState().login.token
		// let action = values.status
		let id = values._id
		dispatch(setIsRequesting(true))
		const response = updateContact(id, values, token)
		response
			.then((a: any) => {
				dispatch(setIsRequesting(false))
				if (a === undefined) {
					dispatch(
						setModalContent({
							title: "Error",
							p: "Oops, something went wrong.",
							error: true,
						}),
					)
				} else {
					dispatch(resetUnsavedChanges())
					dispatch(
						setModalContent({
							title: "Contact Updated",
							p: `${a.data.type} contact has been successfully saved`,
							error: false,
						}),
					)
				}
			})
			.catch(function (e) {
				dispatch(setIsRequesting(false))
				if (e.response) {
					switch (e.response.status) {
						case 500:
							dispatch(
								setModalContent({
									title: "Error",
									p: "Oops, something went wrong.",
									error: true,
								}),
							)
							break
						case 401:
							console.log("unauthorized user")
							dispatch(setSessionExpired(true))
							break
						default:
							dispatch(
								setModalContent({
									title: "Error",
									p: "Oops, something went wrong.",
									error: true,
								}),
							)
							break
					}
				} else {
					dispatch(
						setModalContent({
							title: "Error",
							p: "Oops, something went wrong.",
							error: true,
						}),
					)
				}
			})
	}

// Get Location Province
export const fetchLocationProvince =
	// @ts-ignore
	(): AppThunk => async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let token = getState().login.token
			console.log("RESULT SUCCESS: ", token)
			const response = getProvinceList(token)
			console.log("RESULT SUCCESS2: ", response)
			response
				.then((a: any) => {
					dispatch(getLocationProvinces(a.data.body))
					console.log("RESULT SUCCESS: ", a.data)
				})
				.catch(function (e) {
					console.log("catch", e)
					console.log("RESULT ERROR: ", e)
					if (e.response && e.response.status) {
						switch (e.response.status) {
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
					} else {
						dispatch(
							setModalContent({
								title: "Error",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					}
				})
		} catch (err) {
			console.log(err)
		}
	}

// Get Cities List
export const fetchLocationCities =
	// @ts-ignore
	(): AppThunk => async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let token = getState().login.token
			console.log("RESULT SUCCESS: ", token)
			const response = getCitiesList(token)
			console.log("RESULT SUCCESS2: ", response)
			response
				.then((a: any) => {
					dispatch(getLocationCities(a.data.body))
					console.log("RESULT SUCCESS: ", a.data)
				})
				.catch(function (e) {
					console.log("catch", e)
					console.log("RESULT ERROR: ", e)
					if (e.response && e.response.status) {
						switch (e.response.status) {
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
					} else {
						dispatch(
							setModalContent({
								title: "Error",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					}
				})
		} catch (err) {
			console.log(err)
		}
	}

// Get Barangay List
export const fetchLocationBrgys =
	(city: any): AppThunk =>
	// @ts-ignore
	async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let token = getState().login.token
			console.log("RESULT SUCCESS: ", token)
			const response = getBrgyList(token)
			response
				.then((a: any) => {
					const filterBrgys = a.data.body.filter(
						(item: any) => item.status === "active" && item.city === city,
					)
					dispatch(getLocationBrgys(filterBrgys))
					console.log("RESULT SUCCESS: ", a.data)
					dispatch(setIsFetchingList(true))
				})
				.catch(function (e) {
					console.log("catch", e)
					console.log("RESULT ERROR: ", e)
					if (e.response && e.response.status) {
						switch (e.response.status) {
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
					} else {
						dispatch(
							setModalContent({
								title: "Error",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					}
					dispatch(setIsFetchingList(true))
				})
		} catch (err) {
			console.log(err)
		}
	}

// Get Office Locations
export const fetchOfficeLocations =
	// @ts-ignore
	(): AppThunk => async (dispatch: AppDispatch, getState: () => RootState) => {
		try {
			let token = getState().login.token
			const response = getOfficeLocationList(token)
			response
				.then((a: any) => {
					dispatch(getOfficeLocations(a.data))
				})
				.catch(function (e) {
					console.log("catch", e)
					console.log("RESULT ERROR: ", e)
					if (e.response && e.response.status) {
						switch (e.response.status) {
							case 500:
								console.log("Oops, something went wrong.")
								dispatch(
									setModalContent({
										title: "",
										p: "Oops, something went wrong.",
										error: true,
									}),
								)
								break
						}
					} else {
						dispatch(
							setModalContent({
								title: "Error",
								p: "Oops, something went wrong.",
								error: true,
							}),
						)
					}
				})
		} catch (err) {
			console.log(err)
		}
	}

export default contactSlice.reducer
