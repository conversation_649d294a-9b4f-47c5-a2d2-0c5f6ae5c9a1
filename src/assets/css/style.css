/* Default VNI Stylesheet */

/* @import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,500;1,400;1,500&display=swap'); */
@import url("https://use.typekit.net/wvp0eso.css");

body{
    font-family: usual, sans-serif !important;
}

/* Icons */

.icon-search::before {
    position: absolute;
    content: url(../images/icon-search.png);
    display: inline-block;
    transform: scale(.70);
    opacity: .4;
    padding: 5px;
}

/* Custom Typhography */

.header1 {
    font-weight: 600;
    font-size: 18px;
}

.NormalParagraph {
    font-weight: 400;
    font-size: 13px;
    margin-bottom: 15px;
}

.CustomLabel {
    font-weight: 400;
    font-size: 13px;
    margin: 15px 0px;
}

.bold {
    font-weight: 800;
}

.medium {
    font-weight: 600;
}

/* Custom Color */

.grey {
    color: #6A6A6A;
}

.main-60 {
    color: #797F92;
}

/* Custom for display(Grid, flex, align, justify), margins, paddings - Might be replaced in Material UI */

.w-100 {
    width: 100%;
}

.d-flex {
    display: flex;
}

.align-items-center {
    align-items: center;
}

.align-items-end {
    align-items: flex-end;
}

.m-0 {
    margin: 0;
}

.mt-15 {
    margin-top: 15px;
}

.row {
    display: flex;
    margin: 0 -18px;
}

.col-3 {
    width: 25%;
    padding: 0px 18px;
}

.col-6 {
    width: 50%;
    padding: 0px 18px;
}

body {
    background-color: #F5F7FB;
    margin: 0px;
    font-family: usual, sans-serif;
    font-weight: 400;
    font-style: normal;
    /* Main Color */
    color: #272E4C;
}

/* For primary button 
   ex. <Button variant="contained" color="primary" disableElevation>
*/

.CustomPrimaryButton {
    font-family: usual, sans-serif;
    font-weight: 600;
    padding: 12px 43px;
    text-align: center;
    font-size: 14px;
    background-color: #3AB77D;
    border: none;
    border-radius: 4px;
    color: white;
    min-width: 150px;
    /* border: 1.5px solid #3AB77D; */
    outline: none;
    cursor: pointer;
    text-decoration: none;
}

.CustomPrimaryButton.buttonDisabled {
    border: 1.5px solid #7a7a7a !important;
    background-color: #b1b1b1 !important;
    color: #7a7a7a !important;
    pointer-events: none;
}

/* For primary outline button
   ex. <Button variant="outlined" color="primary">
*/

.CustomPrimaryOulineButton {
    font-family: usual, sans-serif !important;
    font-weight: 600 !important;
    padding: 12px 43px !important;
    text-align: center !important;
    font-size: 14px !important;
    background-color: white !important;
    border: 1.5px solid #3AB77D !important;
    border-radius: 4px !important;
    color: #3AB77D !important;
    min-width: 150px !important;
    outline: none !important;
    cursor: pointer !important;
    text-decoration: none !important;
}

/* For Red outlined button */

.CustomPrimaryOulineButton.scarlet {
    border: 1.5px solid #eb6260 !important;
    background-color: #f3baba !important;
    color: #eb6260 !important;
    cursor: pointer !important;
}

.CustomPrimaryOulineButton.scarlet:disabled{
    border: 1.5px solid #7a7a7a !important;
    background-color: #b1b1b1 !important;
    color: #7a7a7a !important;

}

.CustomPrimaryOulineButton.scarlet.no-bg {
    background: transparent;
}

/* For Green outlined button (Ex. Save For Now button) */

.CustomPrimaryOulineButton.green {
    border: 1.5px solid #3AB77D !important;
    background-color: #63927d !important;
    color: white !important;
}

/* For disabled button
   ex. <Button variant="contained" disabled>
*/

.CustomDisabledButton {
    font-family: usual, sans-serif;
    font-weight: 600;
    padding: 12px 43px;
    text-align: center;
    font-size: 14px;
    background-color: #CBCBCB;
    border: 1.5px solid #CBCBCB;
    outline: none;
    border-radius: 4px;
    color: #fff;
    min-width: 150px;
    cursor: not-allowed;
}

/* For Tables */

/* Add this to a div that have a tale inside
   Note: For scrollable table
*/

.TableHolder {
    height: 530px;
    width: 100%;
    overflow: auto;
    box-shadow: 0px 3px 6px #272E4C19;
}

.TableHolder::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

.TableHolder::-webkit-scrollbar-track {
    background: #DDDFE3;
}

.TableHolder::-webkit-scrollbar-thumb {
    background: rgba(54, 54, 54, .60);
    border: 3px solid #DDDFE3;
    border-radius: 20px;
}

/* Custom Table */

.CustomTable {
    font-size: 12px;
    min-width: 100%;
    max-width: 100%;
    border-collapse: collapse;
}

.CustomTable .MuiTableCell-head {
    z-index: 2;
    position: sticky;
    top: 0;
    min-width: 50px;
    background-color: #DDDFE3;
    color: #272E4C;
    padding: 14px;
    text-align: left;
    font-family: usual, sans-serif;
    font-weight: 600;
    white-space: nowrap;
    cursor: pointer;
}

.CustomTable .MuiTableCell-body {
    background-color: inherit;
    font-size: 13px;
    max-width: 200px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    color: #272E4C;
    padding: 14px;
    text-align: left;
    font-family: usual, sans-serif;
    font-weight: 400;
}

.CustomTable tr {
    background-color: white;
}

.CustomTable tr:nth-child(odd) {
    background-color: #f6f7fa;
}

.MuiTableCell-head {
    cursor: pointer;
    position: relative;
    white-space: nowrap;
}

.MuiTableCell-head.sortable::after {
    content: "";
    display: inline-block;
    transform: translateY(3.5px);
    opacity: .5;
    /* padding-left: 12px; */
    width: 16px;
    height: 16px;
    background: url(../images/icon-sort.png) center / 60% no-repeat;
    /* position: absolute; */
    top: 13px;
    margin-left: 10px;
}

.MuiTableCell-head.sortable:hover::after {
    opacity: .7;
}

/* Action in table cell like Edit etc. */

.CustomTable .MuiTableCell-body .action {
    color: #3AB77D;
    font-size: 11px;
    text-decoration: underline;
    cursor: pointer;
}

/* For table with sticky table data */

.sticky-td-2 td:nth-child(1) {
    position: sticky;
    left: 0px;
}

.sticky-td-2 td:nth-child(2) {
    position: sticky;
    left: 145px;
}

.sticky-td-2 th:nth-child(1) {
    position: sticky;
    left: 0px;
    z-index: 4;
}

.sticky-td-2 th:nth-child(2) {
    position: sticky;
    left: 145px;
    z-index: 4;
}

/* For Table label and controller */

.table-label {
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
}

.table-label p {
    font-size: 14px;
    color: #272E4C;
    color: #B8BBC7;
    margin: 0;
}

.table-controller {
    display: flex;
    align-items: baseline;
}

.table-controller p {
    font-size: 13px;
    font-family: usual, sans-serif;
    font-weight: 600;
    font-weight: bold;
    color: #272E4C;
    margin-left: 14px;
}

.table-controller span {
    font-size: 12px;
    /* color: #272E4C; */
    color: #B8BBC7;
    text-decoration: underline;
    cursor: pointer;
    margin-left: 14px;
}

.table-controller .veridata-select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    /* background: url(../images/icons/select-arrow.png);
    background-repeat: no-repeat;
    background-position-x: 80%;
    background-position-y: 3px;
    background-size: 10px; */
    background: url(../images/select-arrow.png) right 5px top 5px / 10px no-repeat;
    color: #272E4C;
    border: 0.5px solid #272E4C8A;
    padding: 3px 20px 3px 10px;
    font-size: 12px;
    border-radius: 8px;
    outline: none;
    margin-left: 14px;
}

/* Override for checkbox in Material UI */

.MuiCheckbox-colorPrimary.Mui-checked {
    color: #1E2071;
}

/* Custom Checkbox - Might not be needed in Material UI */

.custom-checkbox {
    position: absolute;
    opacity: 0;
}

.custom-checkbox+label {
    position: relative;
    cursor: pointer;
    padding: 0;
}

.custom-checkbox+label:before {
    z-index: -1;
    content: '';
    margin-right: 10px;
    display: inline-block;
    vertical-align: text-top;
    width: 17px;
    height: 17px;
    background: white;
    border-radius: 3px;
    border: 1px solid #DDDFE3;
}

.custom-checkbox:hover+label:before {
    background: #f6f7fa;
}

.custom-checkbox:checked+label:before {
    background: #1E2071;
}

.custom-checkbox:disabled+label {
    color: #b8b8b8;
    cursor: not-allowed;
}

.custom-checkbox:disabled+label:before {
    box-shadow: none;
    background: #B4B6D1;
}

.custom-checkbox:checked+label:after {
    content: '';
    position: absolute;
    left: 3px;
    top: 3px;
    width: 2px;
    height: 2px;
    content: url(../images/icon-check.svg);
    transform: scale(.025);
    filter: invert(100%) sepia(100%) saturate(0%) hue-rotate(290deg) brightness(103%) contrast(102%);
}

/* Checkbox label style for Mother Company */

.customCheckboxLabel {
    position: relative;
    left: 60px;
}

/* Custom Breadcrumb with header */

.breadcrumb p {
    margin: 0px;
    margin-bottom: 10px;
    text-decoration: underline;
    color: #272E4C;
    font-size: 12px;
    display: inline-block;
    font-weight: 100;
}

.breadcrumb p.active {
    text-decoration: none;
    font-weight: 600;
    font-size: 12px;
}

.breadcrumb h1 {
    margin: 0px;
    color: #272E4C;
    font-size: 18px;
    font-weight: 600;
}

/* Custom Input 
   ex. <TextField id="outlined-basic" label="Outlined" variant="outlined" />
*/

.CustomInput {
    box-sizing: border-box;
    /* To re-check - mark */
    padding: 9.5px;
    font-size: 15px;
    border-radius: 8px;
    border: 1px solid #272E4C4D;
    outline: none;
    color: #272E4C;
    box-shadow: 0 0 0px 1000px #ffffff inset;
}

/* Error State of Input */

.CustomInput.disabled {
    pointer-events: none;
    background-color: #cccccc;
    opacity: .60;
    box-shadow: 0 0 0px 1000px #cccccc inset;
}

/* Error State of Input */

.CustomInput.error {
    background-color: #ffebee;
    border: 1px solid #ef5350;
    box-shadow: 0 0 0px 1000px #ffebee inset;
}

textarea.CustomInput {
    resize: none;
}

/* Custom input with icon on the left */

.input-add-icon {
    box-sizing: border-box;
    padding: 11px 11px 11px 50px;
    font-size: 15px;
    border-radius: 8px;
    border: 1px solid #272E4C4D;
    outline: none;
    color: #272E4C;
}

/* Custom Date Picker */

.vni-datepicker {
    display: block;
    width: 100%;
    position: relative;
    margin-top: 0.25rem;
}

.vni-datepicker:hover {
    cursor: pointer;
}

.vni-datepicker label {
    display: block;
    width: 100%;
}

.vni-datepicker .CustomInput {
    width: 100%;
}

.vni-datepicker i.fa-calendar {
    position: absolute;
    right: 15px;
    top: 13px;
    color: #a6a7ac;
    font-size: 125%;
}

/* Custom Autocomplete for material-ui autocomplete */

.CustomAutocomplete {
    width: -webkit-fill-available;
    width: -moz-available;
    margin-top: 0.25rem;
    border: 1px solid #272E4C4D;
    border-radius: 8px;
    outline: none;
    appearance: none !important;
    -webkit-appearance: none !important;
    text-overflow: ellipsis;
    background-color: white;
    color: #272E4C !important;
}

.CustomAutocomplete.disabled {
    pointer-events: none;
    background-color: #cccccc !important;
    opacity: .60;
}

.CustomAutocomplete.error {
    background-color: #ffebee !important;
    border: 1px solid #ef5350;
}

/* Custom Select - Might wanna use this, Material UI select is complicated to customize */

.CustomSelect {
    width: -webkit-fill-available;
    width: -moz-available;
    border: 1px solid #bec0c9;
    border-radius: 8px;
    padding: 11px;
    padding-right: 20px;
    outline: none;
    appearance: none !important;
    -webkit-appearance: none !important;
    text-overflow: ellipsis;
    background: white url(../images/select-arrow.png) right 10px center / 10px no-repeat;
    color: #272E4C !important;
    /* background-size: 10px;
    background-repeat: no-repeat;
    background-position-y: 10px;
    background-position-x: calc(100% - 8px); */
}

/* Select Override */
.CustomSelect .MuiSelect-icon{
    display: none;
}

.CustomSelect .MuiSelect-select{
    padding: 0px !important;
    background-color: white;
    border: none;
}

.CustomSelect .MuiSelect-select:focus{
    background-color: white;
}

.CustomSelect.MuiInput-underline:hover:not(.Mui-disabled):before{
    border:none;
}

.CustomSelect::before{
    border-bottom: 0px !important;
    display: none;
    opacity: 0;
}

.CustomSelect::after{
    border-bottom: 0px !important;
    display: none;
    opacity: 0;
}
/* Error State of Input */

.CustomSelect.error {
    background-color: #ffebee;
    border: 1px solid #ef5350;
}

.CustomSelect.error .MuiSelect-root{
    background-color: #ffebee;
}

.CustomSelect.error .MuiSelect-select:focus{
    background-color: #ffebee;
}

.CustomSelect.disabled{
    background-color: #cccccc;
    opacity: .60;
    border: 1px solid #272E4C4D;
}

.CustomSelect.Mui-disabled .MuiSelect-select.Mui-disabled {
    background-color: #cccccc;
  }


/* Custom Logo Header with account and notification */

.logo-head {
    position: sticky;
    top: 0px;
    z-index: 10;
    background-color: white;
    padding: 10px 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0px 3px 6px #********;
}

.logo-head.no-shadow {
    box-shadow: none;
}

.user-details {
    position: relative;
}

.user-details .drop {
    display: none;
    width: auto;
    position: absolute;
    z-index: 30;
    right: 0;
    padding: 8px 16px;
    background: white;
    border-radius: 8px;
    min-width: 120px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -1px rgba(0, 0, 0, .06);
}

.user-details .drop a {
    text-decoration: none;
    color: inherit;
    display: block;
    text-align: left;
}

.user-details:hover .drop {
    display: block;
}

.account-avatar {
    cursor: pointer;
    width: 24px;
    height: 24px;
    overflow: hidden;
    border-radius: 50%;
    margin-left: 30px;
    display: inline-block;
}

.triangle-down {
    margin-left: 8px;
    position: relative;
    top: 3px;
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid black;
}

.account-name {
    cursor: pointer;
    color: #272E4C;
    font-size: 16px;
    padding-left: 15px;
    font-weight: 600;
}

.account-name::after {
    content: url(../images/icon-dropdown.png);
    transform: translateY(5px) scale(.7);
    display: inline-block;
    filter: invert(15%) sepia(23%) saturate(300%) hue-rotate(145deg) brightness(94%) contrast(94%);
    opacity: 70%;
}

/* .notification{
    display: inline-block;
    transform: translateY(3px);
    cursor: pointer;
}


.notification-count{
    background-color: #FF6969;
    border-radius: 50%;
    padding: 1px 4px;
    color: white;
    font-size: 8px;
    text-align: center;
    display: inline-block;
    transform: translate(-13px, -13px);
} */

.notification {
    display: inline-block;
    cursor: pointer;
    position: relative;
}

.notification input[type="checkbox"] {
    appearance: none;
    display: none;
}

.notifs-dropdown {
    display: none;
    position: absolute;
    z-index: 20;
    width: 360px;
    border-radius: 8px;
    padding: 1rem;
    right: 0;
    background: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -1px rgba(0, 0, 0, .06);
}

.notification input[type="checkbox"]:checked+.notifs-dropdown {
    display: block;
}

.notification .notif-list {
    display: flex;
    flex-flow: column;
    margin: 10px 0;
}

.notification .notif-list>span {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
}

.notification .notif-list>span p {
    width: calc(100% - 100px);
}

.notification .notif-list>span .timestamp {
    width: 100px;
    margin-left: 16px;
    text-align: right;
}

.notification-count {
    background-color: #FF6969;
    border-radius: 50%;
    color: white;
    font-size: 8px;
    text-align: center;
    display: inline-block;
    position: absolute;
    padding: 2px 4px;
    right: -5px;
    top: -5px;
}

/* Custom Menu */

.menu {
    position: sticky;
    top: 63px;
    z-index: 10;
    background-color: #1E2071;
    font-size: 12px;
    padding: 15px 158px 0px 158px;
}

.menu-items {
    padding: 0px;
    display: flex;
    justify-content: space-between;
    margin: 0px;
    list-style: none;
}

.menu-items li {
    cursor: pointer;
    color: white;
    padding-bottom: 11px;
    border-bottom: 4px solid #1E2071;
}

.menu-items li.active {
    color: #62FFB7;
    padding-bottom: 11px;
    border-bottom: 4px solid #62FFB7;
    font-weight: 800;
}

/* Custom page Header */

.PageHeaderContainer {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

/* Custom Alert or notification */

.alert {
    width: 330px;
    height: 73px;
    position: absolute;
    bottom: 10px;
    right: 0px;
    opacity: .89;
    box-shadow: 0px 3px 6px #272E4C52;
    color: white;
    font-weight: 600;
    border-radius: 8px 0px 0px 8px;
    font-size: 14px;
}

.alert.success {
    background-color: #3AB77D;
}

.alert .content {
    padding: 20px 50px 20px 15px;
    font-size: 14px;
}

.alert .close {
    position: absolute;
    top: 10px;
    right: 25px;
    opacity: .5;
    cursor: pointer;
}