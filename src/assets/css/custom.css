/* Custom Stylesheet */

body {
    font-size: 13px;
}

a {
    color: #3AB77D;
    text-decoration: underline;
    display: flex;
}

a svg {
    margin-right: 0.25rem;
}

a svg svg path {
    fill: #3AB77D;
}

a.disabled {
    color: #c5c4c9;
    pointer-events: none;
}

a.disabled svg path {
    fill: #c5c4c9;
}

a.more {
    font-weight: 700;
    color: #7b7c7e;
}

button.bluewood, a.bluewood {
    background-color: #272E4C;
    border-style: none;
}

button.scarlet, a.scarlet {
    background-color: #f56565;
    border-style: none;
}

aside#sidebar {
    position: -webkit-sticky;
    position: sticky;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    color: #FFFFFF;
    background-color: #1E2071;
    text-align: center;
    font-size: 9px;
}

aside#sidebar .sidebar-content {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
    position: fixed;
    top: 0;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    left: 0;
    max-width: 74px;
    width: 100%;
}

aside a {
    display: block;
    color: #FFFFFF;
    text-decoration: none;
}

aside a>span {
    display: block;
    width: 2rem;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 0.25rem;
    padding: 0.5rem;
    background-color: #35377B;
    border-radius: 9999px;
}

aside a svg {
    width: 100%;
    max-height: 16px;
}

aside a svg path {
    fill: #fff;
}

aside a.active svg path {
    fill: #3AB77D;
}

.main {
    position: relative;
    display: block;
}

.bg-login {
    background: url(../images/background.jpg) center / cover no-repeat;
}

.vni-tab-btn-container {
    display: flex;
    flex-wrap: wrap;
    margin-top: 0.75rem;
    margin-bottom: 0.75rem;
}

.vni-tab-btn-container.no-wrap {
    flex-wrap: nowrap;
}

.vni-tab-btn-container span {
    margin-right: 0.75rem;
    margin-bottom: 0.75rem;
}

.vni-tab-btn-container span:last-of-type {
    margin-left: 0;
}

.vni-tab-btn-container span.vni-mb-0 {
    margin-bottom: 0;
}

.notification-list li {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
}

.notification-list li p {
    border-bottom: #707070 1px solid;
    flex-grow: 1;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    width: calc(100% - 6rem);
}

.notification-list li span.timestamp {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    text-align: right;
    width: 6rem;
}

.batch-submit svg {
    fill: #3AB77D
}

.batch-submit.disabled svg {
    fill: #c5c4c9;
}

/* .animate { transition: all .1s ease-in-out; } */

/* File Upload Button */

.file-upload-btn {
    display: flex;
    align-items: center;
}

.upload-box {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    margin-top: 1.25rem;
    margin-bottom: 1.25rem;
    padding: 1.25rem;
    border-radius: 0.5rem;
    text-align: center;
    background-color: #e7e8ed;
    border: #b0b3c9 2px dashed;
    min-height: 40vh;
    color: #707070;
}

.upload-box .file-item {
    width: 33.333333%;
    padding: 0.5rem;
}

.upload-box .file-item>div {
    position: relative;
    display: block;
    background-color: #d2d3d8;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

.upload-box .file-item .file-info {
    display: block;
    padding: 1.5rem;
}

.upload-box .file-item i {
    font-size: 80px;
    margin-bottom: 15px;
}

/* Cut Long file names */

.upload-box .file-item h5 {
    overflow: hidden;
    height: 38px;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.upload-box .file-item .loading {
    background-color: #B9BBC6;
    height: 5px;
    width: 100%;
}

.upload-box .file-item .loading .progress {
    background-color: #3AB77D;
    height: 5px;
}

.upload-box .file-item .custom-checkbox+label {
    position: absolute;
    left: 0;
    margin: 0.75rem;
}

.upload-box .file-item.error>div {
    background-color: #e9ccd0;
    border: #dc7b7c 2px solid;
    border-radius: 5px;
}

.upload-box .file-item .error-desc {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    color: #dc7b7c;
}

.supporting-documents-list * {
    text-align: right;
}

.supporting-documents-list ul li {
    display: block;
    color: #3AB77D;
    text-decoration: underline;
    margin-top: 0.25rem;
    margin-bottom: 0.25rem;
}

.preview-request-container {
    width: 91.666667%;
    margin-bottom: 1.25rem;
}

.preview-request-container .info-row {
    display: flex;
    padding: 0;
    margin-bottom: 2rem;
}

.preview-request-container .info-row>span {
    padding-right: 1rem;
}

.preview-request-container small {
    color: #7a7e93;
    font-size: 12px;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
}

.preview-request-container h4 {
    font-size: 1rem;
}

.vni-tab-btn {
    background-color: #FAFCFE;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    border-radius: 12px;
    min-width: 170px;
}

/* remove if necessary */

.vni-tab-btn strong {
    font-size: 1.25rem;
    margin-right: 0.5rem;
    color: #3AB77D;
}

.vni-tab-btn.active, .vni-tab-btn:hover {
    background-color: #3AB77D;
    color: #FFFFFF;
}

.vni-tab-btn.active strong, .vni-tab-btn:hover strong {
    color: #FFFFFF;
}

.vni-table-container {
    background-color: #FFFFFF;
    width: 100%;
    max-width: 100%;
    border-radius: 0.375rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.vni-table-container a {
    font-weight: 700;
    text-decoration: none;
}

.vni-table {
    display: flex;
}

.vni-table .scrollable {
    flex-grow: 1;
}

.vni-table .scrollable>div {
    overflow-x: auto;
}

.vni-table .scrollable table {
    table-layout: auto;
    width: 100%;
}

.vni-table .sticky-cell {
    position: -webkit-sticky;
    position: sticky;
    right: 0;
    background-color: #FFFFFF;
    text-align: center;
    min-width: 100px;
}

.vni-table .sticky-cell a {
    display: inline-block;
    margin-left: 1rem;
}

.vni-table .sticky-cell a:first-of-type {
    margin-left: 0;
}

.vni-table .vni-chk-cell {
    min-width: 0px;
}

.vni-table .custom-checkbox+label:before {
    border: #707070 2px solid;
}

.vni-table .custom-checkbox:checked+label:after {
    transform: scale(.020);
}

/* border-bottom: #F4F4F4 1px solid; */

.vni-table tr:nth-of-type(even) {
    background-color: #f6f7fa;
}

.vni-table tr.vni-row-chk {
    background-color: #ddf1eb;
}

.vni-table tr.vni-row-chk .sticky-cell {
    background-color: #ddf1eb;
}

.vni-table th, .vni-table td, .vni-table th:first-child {
    padding: 0.75rem;
    min-width: 120px;
    width: auto;
}

.vni-table th {
    text-align: left;
    background-color: #DDDFE3;
}

.vni-table th.sticky-cell {
    background-color: #DDDFE3;
}

.vni-table td:first-of-type {
    vertical-align: middle;
}

.vni-table a {
    text-decoration: underline;
    font-weight: 400;
    display: inherit;
}

.vni-table a.compare {
    text-decoration: none;
    font-weight: 700;
    display: block;
    text-align: center;
}

.CustomSelect.minimal {
    text-align: center;
    background-color: transparent;
    border-radius: 0;
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    border-top: 0;
    border-left: 0;
    border-right: 0;
    -moz-text-align-last: center;
    text-align-last: center;
}

.CustomSelect.minimal .MuiSelect-root{
    display:flex;
}

.vni-search {
    margin-top: 1.25rem;
    margin-bottom: 1.25rem;
    display: flex;
    align-items: center;
    width: 100%;
}

.vni-search.vni-mTop {
    margin-top: 0;
}

.vni-search .input-add-icon {
    padding-left: 1rem;
}

.vni-search .CustomPrimaryButton {
    margin-left: 1.5rem;
    margin-right: 3rem;
    border-radius: 0.5rem;
}

div[class*="buttonGroup"] .MuiButton-text {
    padding: 6px 20px;
}

.custom-checkbox+label:before {
    border: #707070 2px solid;
}

.custom-checkbox:checked+label:after {
    transform: scale(.020);
}

.custom-rdo {
    position: relative;
    display: flex;
    align-items: center;
}

.custom-rdo input[type="radio"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.custom-rdo input[type="radio"]+label:before {
    content: '';
    top: 3px;
    border-width: 1px;
    background-color: #FFFFFF;
    border-color: #272E4C;
    width: 1rem;
    height: 1rem;
    display: inline-block;
    position: relative;
    margin-right: 0.25rem;
    border-radius: 9999px;
}

.custom-rdo input[type="radio"]+label:hover {
    cursor: pointer;
}

.custom-rdo input[type="radio"]:checked+label:before {
    background-color: #3AB77D;
    box-shadow: inset 0 0 0 2px white;
}

.custom-rdo:hover {
    cursor: pointer;
}

.custom-rdo+.custom-rdo {
    margin-left: 2rem;
}

label+.CustomInput, label+.CustomSelect {
    margin-top: 0.25rem;
}

.create-franchise-request-head, .notifications-head, .maintenance-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #FFFFFF;
    padding: 3rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.create-franchise-request-head .breadcrumbs, .notifications-head .breadcrumbs, .maintenance-head .breadcrumbs {
    text-transform: uppercase;
    margin-bottom: 0.75rem;
    display: block;
}

.create-franchise-request-head .breadcrumbs a, .notifications-head .breadcrumbs a, .maintenance-head .breadcrumbs a {
    display: inline-block;
    color: #272E4C;
}

.create-franchise-request-head .progress-bar, .notifications-head .progress-bar, .maintenance-head .progress-bar {
    z-index: 1;
}

.create-franchise-request-head .progress-bar ul, .notifications-head .progress-bar ul, .maintenance-head .progress-bar ul {
    display: flex;
    justify-content: space-between;
    color: #898F9D;
}

.create-franchise-request-head .progress-bar ul li, .notifications-head .progress-bar ul li, .maintenance-head .progress-bar ul li {
    position: relative;
    min-width: 150px;
}

.create-franchise-request-head .progress-bar ul li:before, .notifications-head .progress-bar ul li:before, .maintenance-head .progress-bar ul li:before {
    content: '';
    width: 24px;
    height: 24px;
    background-color: #e8e9ee;
    position: relative;
    border-radius: 9999px;
    border-width: 2px;
    border-color: #FFFFFF;
    border-color: #FFFFFF;
    display: block;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 0.5rem;
    z-index: 10;
}

.create-franchise-request-head .progress-bar ul li:first-of-type:before, .notifications-head .progress-bar ul li:first-of-type:before, .maintenance-head .progress-bar ul li:first-of-type:before {
    margin-left: 0;
    margin-right: 0;
}

.create-franchise-request-head .progress-bar ul li:last-of-type:before, .notifications-head .progress-bar ul li:last-of-type:before, .maintenance-head .progress-bar ul li:last-of-type:before {
    margin-left: 0;
    margin-right: 0;
    margin-left: auto;
}

.create-franchise-request-head .progress-bar ul li:after, .notifications-head .progress-bar ul li:after, .maintenance-head .progress-bar ul li:after {
    content: '';
    height: 2px;
    top: 11px;
    background-color: #e8e9ee;
    width: calc(100% + 75px);
    position: absolute;
    left: 50%;
}

.create-franchise-request-head .progress-bar ul li:first-of-type:after, .notifications-head .progress-bar ul li:first-of-type:after, .maintenance-head .progress-bar ul li:first-of-type:after {
    left: 0;
}

.create-franchise-request-head .progress-bar ul li:last-of-type:after, .notifications-head .progress-bar ul li:last-of-type:after, .maintenance-head .progress-bar ul li:last-of-type:after {
    content: none;
}

.create-franchise-request-head .progress-bar ul li.current, .create-franchise-request-head .progress-bar ul li.done, .notifications-head .progress-bar ul li.current, .notifications-head .progress-bar ul li.done, .maintenance-head .progress-bar ul li.current, .maintenance-head .progress-bar ul li.done {
    font-weight: 700;
    color: #272E4C;
}

.create-franchise-request-head .progress-bar ul li.current:before, .create-franchise-request-head .progress-bar ul li.done:before, .notifications-head .progress-bar ul li.current:before, .notifications-head .progress-bar ul li.done:before, .maintenance-head .progress-bar ul li.current:before, .maintenance-head .progress-bar ul li.done:before {
    background-color: #3AB77D;
}

.create-franchise-request-head .progress-bar ul li.done:after, .notifications-head .progress-bar ul li.done:after, .maintenance-head .progress-bar ul li.done:after {
    background-color: #3AB77D;
}

.kyc-footer-btns {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #7b7c7e;
    padding: 3rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
}

.kyc-footer-btns a {
    text-decoration: none;
    outline: none !important;
}

.kyc-footer-btns button {
    color: #FFFFFF;
    margin-right: 1rem;
    /* &.CustomPrimaryOulineButton { @apply bg-transparent; }
        &.save-for-now { background-color: #63927d; } */
}

/* Request Info status
    <span class="request-status (gold, green, red)">Status Name</span>
*/

.request-status {
    position: relative;
}

.request-status:before {
    content: '';
    width: 8px;
    height: 8px;
    background-color: #7b7c7e;
    display: inline-block;
    border-radius: 9999px;
    margin-left: 0.5rem;
    margin-right: 0.5rem;
}

.request-status.yellow {
    color: #FFBB1B;
}

.request-status.yellow:before {
    background-color: #FFBB1B;
}

.request-status.green {
    color: #3AB77D;
}

.request-status.green:before {
    background-color: #3AB77D;
}

.request-status.scarlet {
    color: #FF6969;
}

.request-status.scarlet:before {
    background-color: #FF6969;
}

/* .tab-content {
  display: none;
}

.tab-content.current {
  display: inherit;
} */

.tab-content {
    display: none;
    width: 75%;
}

.tab-content.current {
    display: flex;
}

.chips-container {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    margin-top: 1.25rem;
    margin-bottom: 1.25rem;
}

.chips-container .chip {
    background-color: #E4E4E4;
    color: #1a202c;
    display: flex;
    align-items: center;
    border-radius: 0.5rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    margin-right: 0.75rem;
    margin-bottom: 0.75rem;
}

.chips-container .chip p {
    padding: 0.5rem;
}

.chips-container .chip button {
    border-width: 1px;
    padding: 0.5rem;
}

.chips-container .chip button:hover {
    opacity: 0.75;
}

.modal .tabs {
    margin-top: 0.75rem;
}

.modal .tabs li {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    padding-right: 0.75rem;
}

.modal .tabs li.current {
    font-weight: 700;
    color: #1E2071;
    text-decoration: underline;
}

.modal .tabs li:hover {
    cursor: pointer;
}

.modal .request-details {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    padding-left: 2.5rem;
    margin-left: 2.5rem;
    border-left-width: 1px;
    border-color: #E4E4E4;
    /* span { @apply border; } */
}

.modal .request-details small {
    color: #898F9D;
    text-transform: uppercase;
    font-size: 12px;
}

.modal .request-details h4 {
    font-weight: 700;
    font-size: 1rem;
}

.modal .vni-table td {
    min-width: 80px;
}

.modal .compare-requests-container {
    width: 100%;
}

.modal .compare-requests-container .compare-requests {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.modal .compare-requests-container .request-details {
    border-style: none;
    padding-left: 0;
    margin-left: 0;
    flex-grow: 0;
    width: calc(50% - 2rem);
}

.modal .compare-requests-container .request-details small {
    color: #898F9D;
    text-transform: uppercase;
    font-size: 11px;
}

.modal .compare-requests-container .request-details h4 {
    font-weight: 700;
    font-size: 0.875rem;
}

.modal .compare-requests-container .request-details .vni-w-1\/3 {
    width: calc(33.333% - 1rem);
}

.modal .compare-requests-container .request-details .vni-w-1\/2 {
    width: calc(50% - 1rem);
}

.modal .CustomSelect.minimal {
    padding-left: 0;
    padding-right: 0;
    font-weight: 700;
    width: 250px;
    max-width: 100%;
    -moz-text-align-last: left;
    text-align-last: left;
}

@media (min-width: 1024px) {
    .upload-box .file-item {
        width: 20%;
    }
}

@media (min-width: 1280px) {
    .upload-box .file-item {
        width: 16.666667%;
    }
}



/* Modal Stylesheet */

.blocker {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    z-index: 30;
    padding: 20px;
    box-sizing: border-box;
    background-color: #000;
    background-color: rgba(0, 0, 0, 0.50);
    text-align: center
}

.blocker:before {
    content: "";
    display: inline-block;
    height: 100%;
    vertical-align: middle;
    margin-right: -0.05em
}

.blocker.behind {
    background-color: transparent
}

.modal {
    display: none;
    vertical-align: middle;
    position: relative;
    z-index: 30;
    max-width: 500px;
    box-sizing: border-box;
    width: 90%;
    background: #fff;
    padding: 40px;
    -webkit-box-shadow: 0 6px 10px rgba(0,0,0,0.15);
    -moz-box-shadow: 0 6px 10px rgba(0,0,0,0.15);
    -o-box-shadow: 0 6px 10px rgba(0,0,0,0.15);
    -ms-box-shadow: 0 6px 10px rgba(0,0,0,0.15);
    box-shadow: 0 6px 10px rgba(0,0,0,0.15);
    text-align: left;
}

.modal.large {
    max-width: 1024px;
}

.modal a.close-modal {
    position: absolute;
    top: 20px;
    right: 20px;
    display: block;
    width: 24px;
    height: 24px;
    text-indent: -9999px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAYAAABV7bNHAAAD0klEQVR4Xu3bP2gTURwH8N+7q6jt0EEaJ3ERCyp00k2EirZI20FI6eDi0skO3dokBwftJWOHbh0KDg42IFKLtIJ1FJ1EUAhuTpJQsENPJL385EoCpXh3737v3nsHeVlzee/d577v9+5fGJhPrAAzPvECBighIQbIAIkVEZMgkyCTIDEBkyAxP1ODTIJMgsQETILE/IRrkOM4N23bPnBd95fYULL9teM4c4h4KQiCrVqt1qK2TgYqlUqXLcuqA8DdsHNE3PR9/9na2tof6mCy+N3i4uLFoaGh1wDwsDuuA8uypldWVj5S2icDVSqVzwBw+0yn+0dHR1O6kFzXvdBut3cYY/fPjOt3EATXKUkiATmOM4aIXyKOiBakGJyTYSLiE8/zXqRNEQmoXC5fYYz9jOls//Dw8NH6+vrftAOibJ+EE7bZ6XTGqtXq17Ttk4DCTiqVyiYAPNWN1K05OwAwHjUWRKx7njebFifcngxULBbt0dHRVwAwowtpYWHh/PDw8Ns4HADYbjQaj+v1eqAUKOxMJ5IKHKEE9Y6GDiRVOJkAqU6SSpzMgE4hhcVyUlZNUo2TKVDY2Pz8/LlCobAtA0kHTuZAspB04UgBSoPUbDYnNzY22nHLLyfObqPRmKIu5XH9k8+Dks4pOKfbbrPZnIlC4sWJayNpnEnfSwNKkaT/InWBdxNOAmOBk3ae53upQFSkLNLHs/M820gHSosUbs+xEkpPTg9PCRAvEiLunawcjE3EHF1lONJWsaid45w6cclXiqMciDdJEULKcbQAEZG04GgD6iGNjIy8Sag34a3SvVarNZ10QsmzIlG2UVakzw4urEcGKOKQEYp1/0wxAk6PWQuS0ikmgKMNSRkQD07fnijy4ADAyRTqu0uNNDi9pTwvV/LSz4MoOL1ik4d7QVKBRHDyhCSlSHPi7PflLVdenDQvN3BON6FHzFGXIZkmqPuUVcqzMV1ImQGpeAStAykTIBU4KQt3ZtNNGEgljg4kISAdOKqRyEA6cVQikYHy8goeT+FGxJee580pu6O4tLR0bWBg4EdMh0pf4uRB6nQ649Vq9UNaJFKClpeXb9i2/S2iM6U4vNMNEWc9zwtffE/1IQGFPZTL5U+MsTune0PE977vT+t6kTwqSYjY8n3/KmVcZCDXdQvHx8dbAHAvRMrTXxEGBwefM8aKpw7exOrq6rtU0eluTAbqdZbXP7OUSqUHlmXdCoJgr1arfafgSL3dQR1Q3n4nnKC87VDW4zFACaIGyACJTTqTIJMgkyAxAZMgMT9Tg0yCTILEBEyCxPz+AUgx8meFPvoDAAAAAElFTkSuQmCC');    
}

.modal-spinner {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translateY(-50%) translateX(-50%);
    padding: 12px 16px;
    border-radius: 5px;
    background-color: #111;
    height: 20px
}

.modal-spinner>div {
    border-radius: 100px;
    background-color: #fff;
    height: 20px;
    width: 2px;
    margin: 0 1px;
    display: inline-block;
    -webkit-animation: sk-stretchdelay 1.2s infinite ease-in-out;
    animation: sk-stretchdelay 1.2s infinite ease-in-out
}

.modal-spinner .rect2 {
    -webkit-animation-delay: -1.1s;
    animation-delay: -1.1s
}

.modal-spinner .rect3 {
    -webkit-animation-delay: -1.0s;
    animation-delay: -1.0s
}

.modal-spinner .rect4 {
    -webkit-animation-delay: -0.9s;
    animation-delay: -0.9s
}

@-webkit-keyframes sk-stretchdelay {
    0%, 40%, 100% {
        -webkit-transform: scaleY(0.5)
    }
    20% {
        -webkit-transform: scaleY(1.0)
    }
}

@keyframes sk-stretchdelay {
    0%, 40%, 100% {
        transform: scaleY(0.5);
        -webkit-transform: scaleY(0.5)
    }
    20% {
        transform: scaleY(1.0);
        -webkit-transform: scaleY(1.0)
    }
}

.CustomPrimaryOulineButton,
.CustomPrimaryButton { min-width: unset!important; }

.modal .btn-group {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}

.modal .btn-group a, .modal .btn-group button { margin: 0 10px; }

.modal .btn-group .CustomPrimaryOulineButton,
.modal .btn-group .CustomPrimaryButton { padding: 12px 24px; min-width: 120px!important; text-align: center; display: block }

.modal .title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
}

.modal .title.vni-mb-0 { margin-bottom: 0; }

.vni-flex.vni-flex-wrap.new-max-width {
    max-width: 620px;
}

/* Report Module extra cell*/
.vni-last-cell-index *[class*="TableStubCell-cell"] {
    display: none;
}

/* Manager Maintenance*/
.vni-mt {
    margin-top: 40px;
}

.vni-mb {
    margin-bottom: 20px;
}
/* Remarks */
li.MuiListItem-root.vni-reason-list {
    display: inline-block;
    background: gainsboro;
    width: auto;
    border-radius: 5px;
    padding: 5px 10px 5px 15px;
    margin-right: 15px;
    margin-bottom: 15px;
}

li.MuiListItem-root.vni-reason-list > * {
    display: inline-block;
    vertical-align: middle;
}

li.MuiListItem-root.vni-reason-list span {
    font-family: usual, sans-serif !important;
    font-size: 0.875rem;
}

.MuiListItem-root.vni-reason-list div[class*="icon"] {
    max-width: 20px;
    min-width: 20px;
    margin: 0 2px;
    cursor: pointer;
}

.MuiListItem-root.vni-reason-list .vni-reason-text {
    padding-right: 15px;
}

.MuiListItem-root.vni-reason-list.selected-item .vni-reason-text {
    color: gray;
}

.vni-search .vni-maintenance-btn {
    margin-right: 0;
}

.vni-list-container svg {
    font-size: 20px;
}

.MuiListItem-root.vni-reason-list div[class*="icon"] svg {
    font-size: 18px;
}

.MuiListItem-root.vni-reason-list.selected-item .vni-reason-edit-icon svg {
    color: #3AB77D;
}

.vni-main-bg {
    background: white;
}

p.MuiTypography-root.vni-requestedBy-section > * {
    display: inline-block;
    vertical-align: top;
    padding: 0 0 0 5px;
}

p.MuiTypography-root.vni-requestedBy-section li {
    padding: 0;
    float: left;
    width: auto;
}

p.MuiTypography-root.vni-requestedBy-section li p {
    margin-right: 5px;
}

.off li + li {
    display: none;
}

.pvw-sheet .MuiTableCell-body {
    padding: 16px 10px;
    word-wrap: break-word;
}

.pvw-sheet th.MuiTableCell-root {
    width: 100%;
}

.vni-flex.vni-flex-direction-col {
    flex-direction: column;
}

.vni-remove-btn1{
    display:none !important;
}

.vni-pos-relative{
    position: relative;
}

.vni-add-btn, .vni-date-picker button {
    outline: none !important;
}

div[class*="vni-remove-btn"] {
    width: 20px;
    min-width: 25px;
    position: absolute;
    margin-top: 8px;
    top: 0;
    right: -28px;
    cursor: pointer;
}

/* Date picker */
.vni-date-picker {
	position: relative;
}

.vni-date-picker .MuiFormHelperText-root.Mui-error {
    position: absolute;
    margin: 0 5px;
    bottom: -13px;
    padding: 0px 5px;
    background: white;
    font-size: 11px;
    left: 0;
    border-radius: 10px;
}

.vni-search-date-picker .MuiFormHelperText-root.Mui-error {
    position: absolute;
    margin: 0;
    top: -15px;
    padding: 0px 3px;
    font-size: 11px;
    left: 0;
}

.vni-date-picker .MuiFormHelperText-root {
    color:#f44336;
}

.vni-expirydate{
    position: relative;
}

.pickerTwo {
    position: absolute;
    width: 20px;
    right: 0;
    margin-right: -25px;
}

.vni-expirydate .pickerTwo .MuiInputAdornment-positionEnd {
    margin-left: 0;
}

.pickerOne, .pickerTwo, .frExpirationDate {
    display: inline-block !important;
    vertical-align: middle;
}

.pickerOne .MuiInputAdornment-root,
.pickerTwo input,
.pickerOne .MuiFormHelperText-root,
.pickerTwo .MuiFormHelperText-root,
.pickerTwo .MuiInput-underline.Mui-error:after,
.vni-expirydate .hidePicker {
	display: none!important;
}

.pickerOne .MuiFormControl-root {
    max-width: inherit !important;
    width: auto !important;
    text-align: left;
    margin-top: 1px;
    margin-right: 3px;
}

.frExpirationDate {
    margin-left: 5px;
}

.pickerTwo button {
    margin-top: 20px;
    margin-left: -14px;
}

.vni-expirydate .pickerOne .MuiInputBase-input {
    font-weight: bold;
    font-size: 14px;
    font-family: "Roboto", "Helvetica", "Arial", sans-serif;
    padding: 2px 0;
    text-transform: uppercase;
    text-align: left;
    box-sizing: border-box;
    min-width: 90px;
    color: rgb(39, 46, 76);
    max-width: 140px;
}

.vni-expirydate svg {
    color: rgb(39, 46, 76);
}

.vni-expirydate .vni-text-gold input {
    color: #FFBB1B !important;
}

.vni-expirydate .vni-text-ocean_green input {
    color: #3AB77D !important;
}

.vni-expirydate #hide {
    text-transform: uppercase;
    position: absolute;
    height: 0;
    overflow: hidden;
    white-space: pre;
}

#entry_request .selectInput:focus-within,
#entry_request input:focus, 
#entry_request textarea:focus {
    box-shadow: 0px 1px 3px rgba(0,0,0,0.3);
}

#entry_request .custom-rdo:focus-within label:before {
    box-shadow: 0px 1px 3px rgba(0,0,0,0.5);
}

#entry_request .custom-rdo {
    border-radius: 5px;
    padding: 2px 4px;
}

.viewFR.rightCaption {
    /* position: absolute; */
    top: 40px;
    right: 50px;
    text-align: right;
}

.viewFR.rightCaption small {
    width: 100%;
    display: block;
    margin: 5px 0;
}

.vni-color-light-grey {
    color: #9A9A9A;
}

.viewFR.rightCaption small .reassign {
    margin-top: 10px !important;
    display: inline-block;
    text-decoration: underline;
    font-size: 12px;
    cursor: pointer;
}

.compare-requests-container .date-sub {
    position: absolute;
    margin-right: 0 !important;
    right: 0;
    top: -10px;
    text-align: right;
}

.compare-requests-container .vni-uppercase {
    text-transform: uppercase;
    font-size: 10px;
}

.compare-requests-container .vni-date-time {
    display: block;
    text-transform: none;
    font-size: 12px;
}

.viewFR.rightCaption .vni-font-normal {
    margin-left: 5px;
    text-transform: none; 
    font-size: 12px;
}