.green-link{
    color: #3AB77D;
    text-decoration: underline;
    font-weight: 600;
    font-size: 14px;
}

.icon-plus::before {
  content: url(../../assets/icons/plus-solid.svg);
  display: inline-block;
  margin: 0 5px 0 0;
  width: 11px;
  height: 11px;
  filter: invert(18%) sepia(75%) saturate(1819%) hue-rotate(139deg) brightness(96%) contrast(91%);
}

.icon-plus.green:before {
  filter: invert(62%) sepia(13%) saturate(1897%) hue-rotate(100deg) brightness(96%) contrast(87%);
}


.green{
    color: #3AB77D;
}

.red{
    color: #FD5451;
}

.d-block{
    display: block !important;
}

.text-14{
    font-size: 14px;
}

.text-16{
    font-size: 16px;
}

.mark-px-0{
    padding-left: 0px !important;
    padding-right: 0px !important;
}

.mark-py-0{
    padding-top: 0px !important;
    padding-bottom: 0px !important;
}

.mark.pb-5{
    padding-bottom: 5px !important;
}

.mark-pt-0{
    padding-top: 0px !important;
}

.MuiTableCell-head{
    white-space: nowrap;
}

.large-modal{
    max-width: 60% !important;
}

.mark-w-154{
    width: 154px !important;
}

.line-height-06{
    line-height: 0.6;
}

.text-24{
    font-size: 24px;
}

.mt-n-125{
    margin-top: -1.25rem;
}

.d-flex-center{
    display: flex !important;
    justify-content: center !important;
}

.d-flex-right{
    display: flex !important;
    justify-content: right !important;
}

.MuiDialog-paper {
    min-width: 500px !important;
    /* max-width: 500px !important; */
    padding: 30px 10px 30px 10px !important;
}

.title{
    font-size: 20px;
    font-weight: bold;
    font-style: normal;
    color: #272E4C;
}

.close-modal {
    cursor: pointer;
    position: absolute;
    top: 20px;
    right: 20px;
    display: block;
    width: 24px;
    height: 24px;
    text-indent: -9999px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center center;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAYAAABV7bNHAAAD0klEQVR4Xu3bP2gTURwH8N+7q6jt0EEaJ3ERCyp00k2EirZI20FI6eDi0skO3dokBwftJWOHbh0KDg42IFKLtIJ1FJ1EUAhuTpJQsENPJL385EoCpXh3737v3nsHeVlzee/d577v9+5fGJhPrAAzPvECBighIQbIAIkVEZMgkyCTIDEBkyAxP1ODTIJMgsQETILE/IRrkOM4N23bPnBd95fYULL9teM4c4h4KQiCrVqt1qK2TgYqlUqXLcuqA8DdsHNE3PR9/9na2tof6mCy+N3i4uLFoaGh1wDwsDuuA8uypldWVj5S2icDVSqVzwBw+0yn+0dHR1O6kFzXvdBut3cYY/fPjOt3EATXKUkiATmOM4aIXyKOiBakGJyTYSLiE8/zXqRNEQmoXC5fYYz9jOls//Dw8NH6+vrftAOibJ+EE7bZ6XTGqtXq17Ttk4DCTiqVyiYAPNWN1K05OwAwHjUWRKx7njebFifcngxULBbt0dHRVwAwowtpYWHh/PDw8Ns4HADYbjQaj+v1eqAUKOxMJ5IKHKEE9Y6GDiRVOJkAqU6SSpzMgE4hhcVyUlZNUo2TKVDY2Pz8/LlCobAtA0kHTuZAspB04UgBSoPUbDYnNzY22nHLLyfObqPRmKIu5XH9k8+Dks4pOKfbbrPZnIlC4sWJayNpnEnfSwNKkaT/InWBdxNOAmOBk3ae53upQFSkLNLHs/M820gHSosUbs+xEkpPTg9PCRAvEiLunawcjE3EHF1lONJWsaid45w6cclXiqMciDdJEULKcbQAEZG04GgD6iGNjIy8Sag34a3SvVarNZ10QsmzIlG2UVakzw4urEcGKOKQEYp1/0wxAk6PWQuS0ikmgKMNSRkQD07fnijy4ADAyRTqu0uNNDi9pTwvV/LSz4MoOL1ik4d7QVKBRHDyhCSlSHPi7PflLVdenDQvN3BON6FHzFGXIZkmqPuUVcqzMV1ImQGpeAStAykTIBU4KQt3ZtNNGEgljg4kISAdOKqRyEA6cVQikYHy8goeT+FGxJee580pu6O4tLR0bWBg4EdMh0pf4uRB6nQ649Vq9UNaJFKClpeXb9i2/S2iM6U4vNMNEWc9zwtffE/1IQGFPZTL5U+MsTune0PE977vT+t6kTwqSYjY8n3/KmVcZCDXdQvHx8dbAHAvRMrTXxEGBwefM8aKpw7exOrq6rtU0eluTAbqdZbXP7OUSqUHlmXdCoJgr1arfafgSL3dQR1Q3n4nnKC87VDW4zFACaIGyACJTTqTIJMgkyAxAZMgMT9Tg0yCTILEBEyCxPz+AUgx8meFPvoDAAAAAElFTkSuQmCC');    
}

.modal-holder{
    position: absolute;
    top: 0;
    background: rgba(0,0,0,0.5);
    z-index: 100;
    min-width: 100% !important;
    height: 100%;
    display: flex;
    align-items: center;
}

.MuiOutlinedInput-input{
    padding: 11px !important;
}
  
.MuiOutlinedInput-root{
    box-sizing: border-box;
    font-size: 15px;
    border-radius: 8px;
    border-color: #272E4C4D;
    outline: none;
    color: #272E4C;
    box-shadow: 0 0 0px 1000px white inset;
}

.MuiOutlinedInput-root:hover
.MuiOutlinedInput-notchedOutline{
    border-color: #272E4C4D !important;
}

.MuiOutlinedInput-root.Mui-focused
.MuiOutlinedInput-notchedOutline{
    border-color: #272E4C4D !important;
    border-width: .5px !important;
}

.MuiTableHead-root tr:last-child{
    display: none;
}

.pvw-sheet .MuiTableHead-root tr:last-child{
    display: table-row;
}

.MuiListItem-root.vni-pointer-events-none{
    color: gray !important;
}

.bg-black-50{
    background-color: rgba(0,0,0,0.5);
}

/* Date picker fix */
.MuiPickersModal-dialogRoot {
  min-width: 310px !important;
  padding: 0 !important;
}

.d-flex-row{
    flex-direction: row !important;
}

.dashboard table tr td:nth-child(5){
    max-width: 200px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-height: 3.5rem;
}

.dashboard table tr td:nth-child(6){
    max-width: 200px;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
    max-height: 3.5rem;
}

.franchise table tr td:nth-child(6){
    max-width: 200px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-height: 3.5rem;
}

.franchise table tr td:nth-child(7){
    max-width: 200px;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
    max-height: 3.5rem;
}

.otherdash table tr td:nth-child(8){
    max-width: 200px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-height: 3.5rem;
}

.otherdash table tr td:nth-child(9){
    max-width: 200px;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
    max-height: 3.5rem;
}

.otherlist table tr td:nth-child(8){
    max-width: 200px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-height: 3.5rem;
}

.otherlist table tr td:nth-child(9){
    max-width: 200px;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
    max-height: 3.5rem;
}

.contactlist table tr td:nth-child(9){
    min-width: 250px;
    max-width: 300px;
    overflow: hidden;
    white-space: normal;
    max-height: 3.5rem;
}

.MuiInputBase-root.MuiOutlinedInput-root.MuiInputBase-formControl.MuiInputBase-adornedEnd.MuiOutlinedInput-adornedEnd{
    height: 36px;
}

.consistent-date tr:first-child th:first-child{
    min-width: 220px;
    max-width: 220px;
}

.consistent-date tr:first-child th:nth-child(2){
    min-width: 220px;
    max-width: 220px;
}

.SimpleTableHolder {
    width: 794px; 
    min-height: 1123px; 
    padding: 30px 65px;
    position: relative;
}

.SimpleTableHolder p {
    font-weight: bold;
    font-size: 13px;
    font-family: sans-serif !important;
    color: black;
}

.SimpleTableHolder p:last-child {
    position: absolute;
    bottom: 30px;
    font-weight: normal;
}

.SimpleTable td, .SimpleTable th{
    padding: 5px;
    border: 1px solid black;
    font-size: 13px;
    text-align: left;
    font-family: sans-serif !important;
    color: black;
}

.SimpleTable {
    width: 100%;
}

.SimpleTable th{
    cursor: auto;
    background-color: #eee;
    font-weight: bolder;
}

.no-underline-date .MuiInput-underline::before{
    display: none;
}

.no-underline-date .MuiInputBase-inputAdornedEnd{
    color: grey;
    font-weight: 500;
    /* font-size: 14px; */
}

.delete-icon{
    position: absolute;
    top: 0;
    right: calc(-25px - .75rem);
}

.MuiCircularProgress-colorPrimary {
    color: rgb(41, 168, 125) !important;
}

.min-h-192{
    min-height: 192px;
}

.custom-scroll{
    /* min-height: 192px; */
    max-height: 192px;
    overflow: auto;
    overflow-x: hidden;
}

.custom-scroll::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

.custom-scroll::-webkit-scrollbar-track {
    background: #DDDFE3;
}

.custom-scroll::-webkit-scrollbar-thumb {
    background: rgba(54, 54, 54, .60);
    border: 3px solid #DDDFE3;
    border-radius: 20px;
}

.static-name-width{
    min-width: 30%;
    max-width: 30%;
    /* text-overflow: ellipsis; */
    overflow: hidden;
    white-space: normal;
    word-break: break-all;
}

.create-date-fix .MuiInputBase-root.MuiOutlinedInput-root.MuiInputBase-formControl.MuiInputBase-adornedEnd.MuiOutlinedInput-adornedEnd{
    height: 43px;
}

.create-date-fix .MuiInputBase-root.MuiOutlinedInput-root.MuiInputBase-formControl.MuiInputBase-adornedEnd.MuiOutlinedInput-adornedEnd input{
    box-shadow: none !important;
}

.MuiOutlinedInput-root:hover.Mui-error .MuiOutlinedInput-notchedOutline{
    border-color: #f44336 !important;
}

.MuiButtonBase-root.MuiIconButton-root{
    z-index: 1;
}