/* Icons */
.icon-search::before{
    position: absolute;
    content: url(../../assets/icons/icon-search.png);
    display: inline-block;
    transform: scale(.70);
    opacity: .4;
    padding: 5px;
}


/* Custom Typhography */
.header1{
    font-weight: 600;
    font-size: 18px;
}

.NormalParagraph{
    font-weight: 400;
    font-size: 13px;
    margin-bottom: 15px;
}

.CustomLabel{
    font-weight: 400;
    font-size: 13px;
    margin: 15px 0px;
}

.bold{
    font-weight: 800;
}

.medium{
    font-weight: 600;
}

/* Custom Color */
.grey{
    color: #6A6A6A;
}

.main-60{
    color: #797F92;
}

/* Custom for display(Grid, flex, align, justify), margins, paddings - Might be replaced in Material UI */
.w-100{
    width: 100%;
}

.d-flex{
    display: flex;
}
.align-items-end{
    align-items: flex-end;
}

.m-0{
    margin: 0;
}

.mt-15{
    margin-top: 15px;
}

.row{
    display: flex;
    margin: 0 -18px;
}

.col-3{
    width: 25%;
    padding: 0px 18px;
}

.col-6{
    width: 50%;
    padding: 0px 18px;
}