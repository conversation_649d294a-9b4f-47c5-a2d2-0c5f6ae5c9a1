@import url("https://use.typekit.net/wvp0eso.css");
@import './variables.css';

body{
    background-color: #F5F7FB;
    margin: 0px;
    font-family: usual, sans-serif;
    font-weight: 400;
    font-style: normal;
    /* Main Color */
    color: #272E4C;

}

/* For primary button 
   ex. <Button variant="contained" color="primary" disableElevation>
*/
.CustomPrimaryButton{
    font-family: usual, sans-serif;
    font-weight: 600;
    padding: 12px 43px;
    text-align: center;
    font-size: 14px;
    background-color: #3AB77D;
    border: none;
    border-radius: 4px;
    color: white;
    min-width: 150px;
    border: 1.5px solid #3AB77D;
    outline: none;
    cursor: pointer;
}

/* For primary outline button
   ex. <Button variant="outlined" color="primary">
*/
.CustomPrimaryOulineButton{
    font-family: usual, sans-serif;
    font-weight: 600;
    padding: 12px 43px;
    text-align: center;
    font-size: 14px;
    background-color: white;
    border: 1.5px solid #3AB77D;
    border-radius: 4px;
    color: #3AB77D;
    min-width: 150px;
    outline: none;
    cursor: pointer;
}

/* For disabled button
   ex. <Button variant="contained" disabled>
*/
.CustomDisabledButton{
    font-family: usual, sans-serif;
    font-weight: 600;
    padding: 12px 43px;
    text-align: center;
    font-size: 14px;
    background-color: #CBCBCB;
    border: 1.5px solid #CBCBCB;
    outline: none;
    border-radius: 4px;
    color: #fff;
    min-width: 150px;
    cursor: not-allowed;
}

/* For Tables */

/* Add this to a div that have a tale inside
   Note: For scrollable table
*/
.TableHolder{
    height: 530px;
    width: 100%;
    overflow: auto;
    box-shadow: 0px 3px 6px #272E4C19;
}

.TableHolder::-webkit-scrollbar{
    width: 12px;
    height: 12px;
}

.TableHolder::-webkit-scrollbar-track{
    background: #DDDFE3;
}

.TableHolder::-webkit-scrollbar-thumb{
    background: rgba(54, 54, 54, .60);
    border: 3px solid #DDDFE3;
    border-radius: 20px;
}

/* Custom Table */
.CustomTable{
    font-size: 12px;
    min-width: 100%;
    max-width: 100%;
    border-collapse: collapse;
}

.CustomTable .MuiTableCell-head{
    z-index: 2;
    position: sticky;
    top: 0;
    min-width: 50px;
    background-color: #DDDFE3;
    color: #272E4C;
    padding: 14px;
    text-align: left;
    font-family: usual, sans-serif;
    font-weight: 600;
    white-space: nowrap;
    cursor: pointer;
}

.CustomTable .MuiTableCell-body {
    background-color: inherit;
    font-size: 13px;
    max-width: 200px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    color: #272E4C;
    padding: 14px;
    text-align: left;
    font-family: usual, sans-serif;
    font-weight: 400;
}

.CustomTable tr{
    background-color: white;
}

.CustomTable tr:nth-child(odd){
    background-color: #f6f7fa;
}

.MuiTableCell-head.sortable::after{
    content: url(../../assets/icons/icon-sort.png);
    display: inline-block;
    transform: scale(.5) translateY(6px);
    opacity: .5;
    padding-left: 12px;
}

/* Action in table cell like Edit etc. */
.CustomTable .MuiTableCell-body .action{
    color: #3AB77D;
    font-size: 11px;
    text-decoration: underline;
    cursor: pointer;

}

/* For table with sticky table data */
.sticky-td-2 td:nth-child(1){
    position: sticky;
    left: 0px;
}

.sticky-td-2 td:nth-child(2){
    position: sticky;
    left: 145px;
}

.sticky-td-2 th:nth-child(1){
    position: sticky;
    left: 0px;
    z-index: 4;
}

.sticky-td-2 th:nth-child(2){
    position: sticky;
    left: 145px;
    z-index: 4;
}

/* For Table label and controller */
.table-label{
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
}

.table-label p{
    font-size: 14px;
    color: #272E4C;
    opacity: 60%;
    margin: 0;
}

.table-controller{
    display: flex;
    align-items: baseline;
}

.table-controller p{
    font-size: 13px;
    opacity: 100% !important;
    font-family: usual, sans-serif;
    font-weight: 600;
    font-weight: bold;
    color: #272E4C;
    margin-left: 14px;
}

.table-controller span{
    font-size: 12px;
    color: #272E4C;
    opacity: 30%;
    text-decoration: underline;
    cursor: pointer;
    margin-left: 14px;
}

.table-controller .veridata-select{
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: url(../../assets/icons/select-arrow.png);
    background-repeat: no-repeat;
    background-position-x: 80%;
    background-position-y: 3px;
    background-size: 10px;
    color: #272E4C;
    border: 0.5px solid #272E4C8A;
    padding: 3px 20px 3px 10px;
    font-size: 12px;
    border-radius: 8px;
    outline: none;
    margin-left: 14px;
}

/* Override for checkbox in Material UI */
.MuiCheckbox-colorPrimary.Mui-checked {
    color: #1E2071;
}

/* Custom Checkbox - Might not be needed in Material UI */
.custom-checkbox {
    position: absolute;
    opacity: 0;
}

.custom-checkbox + label {
    position: relative;
    cursor: pointer;
    padding: 0;
}

.custom-checkbox + label:before {
    z-index: -1;
    content: '';
    margin-right: 10px;
    display: inline-block;
    vertical-align: text-top;
    width: 17px;
    height: 17px;
    background: white;
    border-radius: 3px;
    border: 1px solid #DDDFE3 ;
}

.custom-checkbox:hover + label:before {
    background: #f6f7fa;
}

.custom-checkbox:checked + label:before {
    background: #1E2071;
}

.custom-checkbox:disabled + label {
    color: #b8b8b8;
    cursor: not-allowed;
}
.custom-checkbox:disabled + label:before {
    box-shadow: none;
    background: #B4B6D1;
}

.custom-checkbox:checked + label:after {
    content: '';
    position: absolute;
    left: 3px;
    top: 3px;
    width: 2px;
    height: 2px;
    content: url(../../assets/icons/icon-check.svg);
    transform: scale(.025);
    filter: invert(100%) sepia(100%) saturate(0%) hue-rotate(290deg) brightness(103%) contrast(102%);
}

/* Custom Breadcrumb with header */
.breadcrumb p{
    margin: 0px;
    margin-bottom: 10px;
    text-decoration: underline;
    color: #272E4C;
    font-size: 12px;
    display: inline-block;
    font-weight: 100;
}

.breadcrumb p.active{
    text-decoration: none;
    font-weight: 600;
    font-size: 12px;
}

.breadcrumb h1{
    margin: 0px;
    color: #272E4C;
    font-size: 18px;
    font-weight: 600;
}

/* Custom Input 
   ex. <TextField id="outlined-basic" label="Outlined" variant="outlined" />
*/
.CustomInput{
    box-sizing: border-box;
    padding: 11px;
    font-size: 15px;
    border-radius: 8px;
    border: 1px solid #272E4C4D;
    outline: none;
    color: #272E4C;
}

/* Custom input with icon on the left */
.input-add-icon{
    box-sizing: border-box;
    padding: 11px 11px 11px 50px;
    font-size: 15px;
    border-radius: 8px;
    border: 1px solid #272E4C4D;
    outline: none;
    color: #272E4C;
}

/* Custom Select - Might wanna use this, Material UI select is complicated to customize */
.CustomSelect{
    width: -webkit-fill-available;
    border: 1px solid #bec0c9;
    border-radius: 8px;
    padding: 11px;
    outline: none;
    appearance: none !important;
    -webkit-appearance: none !important;
    background: url(../../assets/icons/select-arrow.png);
    background-color: white;
    background-size: 10px;
    background-repeat: no-repeat;
    background-position-y: 10px;
    background-position-x: calc(100% - 8px);
}

/* Custom Logo Header with account and notification */
.logo-head{
    position: sticky;
    top: 0px;
    z-index: 10;
    background-color: white;
    padding: 18px 50px;
    display: flex;
    justify-content: space-between;
    box-shadow: 0px 3px 6px #********;
}

.account-name{
    cursor: pointer;
    color: #272E4C;
    font-size: 16px;
    padding-left:15px;
    font-weight: 600;
}

.account-name::after{
    content: url(../../assets/icons/icon-dropdown.png);
    transform: translateY(5px) scale(.7);
    display: inline-block;
    filter: invert(15%) sepia(23%) saturate(300%) hue-rotate(145deg) brightness(94%) contrast(94%);
    opacity: 70%;
}

.notification{
    display: inline-block;
    transform: translateY(3px);
    cursor: pointer;
}


.notification-count{
    background-color: #FF6969;
    border-radius: 50%;
    padding: 1px 4px;
    color: white;
    font-size: 8px;
    text-align: center;
    display: inline-block;
    transform: translate(-13px, -13px);
}

/* Custom Menu */
.menu{
    position: sticky;
    top: 63px;
    z-index: 10;
    background-color: #1E2071;
    font-size: 12px;
    padding: 15px 158px 0px 158px;
}

.menu-items{
    padding: 0px;
    display: flex;
    justify-content: space-between;
    margin: 0px;
    list-style: none;
}

.menu-items li{
    cursor: pointer;
    color: white;
    padding-bottom: 11px;
    border-bottom: 4px solid #1E2071;
}

.menu-items li.active{
    color: #62FFB7;
    padding-bottom: 11px;
    border-bottom: 4px solid #62FFB7;
    font-weight: 800;
}

/* Custom page Header */
.PageHeaderContainer{
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

/* Custom Alert or notification */
 .alert{
    width: 330px;
    height: 73px;
    position: absolute;
    bottom: 10px;
    right: 0px;
    opacity: .89;
    box-shadow: 0px 3px 6px #272E4C52;
    color: white;
    font-weight: 600;
    border-radius: 8px 0px 0px 8px;
    font-size: 14px;
}
.alert.success{
    background-color: #3AB77D;
}

.alert .content{
    padding: 20px 50px 20px 15px;
    font-size: 14px;
}

.alert .close{
    position: absolute;
    top: 10px;
    right: 25px;
    opacity: .5;
    cursor: pointer;
}