import React from "react";
import { HashRouter as Router, Route, Switch } from "react-router-dom";

// Layout Container
import LoginLayout from "./shared/components/Layout/LoginLayout";
import DashboardLayout from "./shared/components/Layout/DashboardLayout";
import { ViewPDFReport } from "./shared/components/ViewPDFReport";
import "./assets/css/main.css";
import "./assets/css/style.css";
import "./assets/css/custom.css";
import "./assets/css/mark.css";


export default function AppContainer()  {  

        return (
            <div>
                <Router >
                    <Switch>
                        <Route exact path="/franchising/login" render={props => (<LoginLayout/>)}/>
                        <Route exact path="/franchising/report/preview" component={ViewPDFReport}/>
                        <Route exact path="/franchising/report/preview/:id" component={ViewPDFReport}/>
                        <Route path="/franchising/" render={props => <DashboardLayout/>} />
                    </Switch>
                </Router>
            </div>
        )
}
