import React from 'react';
import { render } from '@testing-library/react';
import App from './App';


import axios from 'axios';


test('Processmaker Auth', async () => {
  
  const url = "http://54.219.22.153/workflow/oauth2/token"
  const body = {
    "grant_type"   : "password",
    "scope"        : "*",
    "client_id"    : "OCIFMIDAGMPGDXWDPBJZPAAHWJXEZEVG",
    "client_secret": "9680491475e79c33bf22814011735245",
    "username"     : "sales1",
    "password"     : "sales1123"        
  }
  const x = await axios.post(url,body)
  
  expect(x.data).toEqual(expect.objectContaining({
    access_token:expect.any(String),
    expires_in:expect.any(Number),
    token_type:expect.any(String),
    scope:expect.any(String),
    refresh_token:expect.any(String)
  }))
  
});
