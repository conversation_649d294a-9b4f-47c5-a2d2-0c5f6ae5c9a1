import React, { useState, useEffect, useRef } from "react";
import {
  Franchise,
  InputValidStatus,
  CompanyAffiliates,
} from "../../shared/models/Franchise";
import {
  MenuItem,
  Select,
  Grid,
  // Box,
  // Input,
  Typography,
  Button,
  // TextField,
} from "@mui/material";
import { Autocomplete } from "@mui/material";
import {
  // cityList,
  channelOfRequestList,
  sourcesOfFundsList,
  previousHMOProviderList,
} from "../../utils/Environment";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "../../shared/reducers/rootReducer";
import ListItemIcon from "@mui/material/ListItemIcon";
import CancelIcon from "@mui/icons-material/Cancel";
import { PatternFormat } from "react-number-format";

import {
  LocalizationProvider,
  DatePicker,
  DateTimePicker,
} from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { fetchContacts } from "../../shared/reducers/ContactSlice";
import {
  fetchIndustryList,
  fetchLocationRegions,
  fetchLocationCities,
  fetchLocationBrgys,
  fetchLocationProvince,
  fetchBDOUsers,
  clearValidationModalContent,
  fetchMotherCompanyList,
} from "../../shared/reducers/FranchiseSlice";

import { PreviewStyles } from "../../shared/components/ViewPDFReport/style";
import {
  CustomCheckbox,
  AutocompleteInputStyle,
  // AutocompleteWithoutArrow,
  CustomOptionStyle,
} from "../../shared/components/CreateFranchiseForm/styles";
import { Modal } from "../../shared/components/Modal";
// import { decryptCredentials } from "../../utils/LoginService";

interface Props {
  handleFranchiseeName: (input: string, match: any) => void;
  handleFranchiseeContactPersonInput: (input: string) => void;
  handleFranchiseeBusinessAddress: (input: string) => void;
  handleFranchiseeEmail: (input: string) => void;
  handleFranchiseeContactNumber: (input: string) => void;
  handleInputChange: (e: any, input_type?: string, input_id?: string) => any;
  handleInputCursor: (e: any, input_type?: string) => any;
  handleRadioChange: (name: string, value: string, id: string) => any;
  handleInputBusinessAddress: (e: any, obj: {}) => any;
  handleInputRefranchise: (e: any, id: string) => any;
  handleInputChannelOfRequest: (e: any, id: string) => any;
  handleInputAutocomplete: (e: any, id: any) => any;
  handleInputClientId: (e: any, id: string) => any;
  handleInputTIN: (name: string, id: string, value: string) => any;
  handleNumNA: (name: string, id: string, value: string) => any;
  handleInputArray: (
    arr: any[],
    key: string,
    action: string,
    lastIndex?: number
  ) => any;
  handleEffectivityDate: (value: any, id: any, name: any) => any;
  handleInputAffiliates: (
    e: any,
    value: CompanyAffiliates,
    name: string
  ) => any;
  handleInputIsMotherCompany: (value: boolean, name: string) => any;
  handleInputContactPerson: (e: any, value: any, valueObject?: any) => any;
  handleDateAndTimeReceived: (value: any, id: string, name: string) => any;

  validationContent: any;

  authorized_contact_persons: any[];
  stakeholders: any[];
  signatories: any[];
  inputStatus: InputValidStatus;
  franchise: Franchise;
  inputRef: any;
  isEditing: boolean;
  group_name: string
}

const CreateEditFranchiseRequest: React.FC<Props> = (props) => {
  const classes = PreviewStyles();
  const optionStyle = CustomOptionStyle();

  const {
    franchise,
    inputStatus,
    handleInputCursor,
    handleInputChange,
    // handleInputAutocomplete,
    handleRadioChange,
    handleInputBusinessAddress,
    handleInputRefranchise,
    handleInputChannelOfRequest,
    handleInputClientId,
    handleInputTIN,
    handleNumNA,
    handleEffectivityDate,
    handleFranchiseeName,
    inputRef,
    authorized_contact_persons,
    stakeholders,
    signatories,
    handleInputArray,
    isEditing,
    validationContent,
    handleInputAffiliates,
    handleInputIsMotherCompany,
    // handleInputContactPerson,
    handleDateAndTimeReceived,
    handleFranchiseeContactPersonInput,
    handleFranchiseeBusinessAddress,
    handleFranchiseeEmail,
    handleFranchiseeContactNumber,
    group_name
  } = props;

  const {
    tax_number,
    entry_id,
    business_address,
    old_account_name,
    corporate_name,
    is_mother_company,
    brand_name,
    channel_of_request,
    is_refranchise,
    client_id,
    company_affiliates_id,
    company_affiliates,
    company_number,
    industry_class_id,
    // applicant_id,
    applicant_name,
    applicant_contact_person,
    applicant_business_address,
    applicant_contact_number,
    applicant_email_address,
    terms,
    provider_name,
    provider_effectivity_date_to,
    provider_effectivity_date_from,
    no_of_employee,
    is_voluntary_enrollees,
    source_of_funds,
    company_paid_shared,
    no_of_dependents,
    // isReturned,
    date_time_received,
  } = franchise;

  // const isListFitched = useSelector(
  //   (state: RootState) => state.franchise.isFetchingList
  // );

  const [radioEntry, setRadioEntry] = useState(false);
  const [reFranchise, setReFranchise] = useState(false);
  const [errorKey, setErrorKey] = useState("");

  const firstname = useSelector((state: RootState) => state.login.firstname);
  // const [decryptedFirstName, setDecryptedFirstName] = React.useState("");
  const middlename = useSelector((state: RootState) => state.login.middlename);
  // const [decryptedMiddleName, setDecryptedMiddleName] = React.useState("");
  const lastname = useSelector((state: RootState) => state.login.lastname);
  // const [decryptedLastName, setDecrptedLastName] = React.useState("");
  const suffix = useSelector((state: RootState) => state.login.suffix);

  // const [currentUser, setCurrentUser] = React.useState("");


  const industryList = useSelector(
    (state: RootState) => state.franchise.industryListData
  );
  const bdoUserList = useSelector(
    (state: RootState) => state.franchise.bdoUserListData
  );
  const motherCompanyList = useSelector(
    (state: RootState) => state.franchise.motherCompanyListData
  );
  const regionList = useSelector(
    (state: RootState) => state.franchise.locationListData.regions
  );
  const provinceList = useSelector(
    (state: RootState) => state.franchise.locationListData.provinces
  );
  const cityList = useSelector(
    (state: RootState) => state.franchise.locationListData.cities
  );
  const brgyList = useSelector(
    (state: RootState) => state.franchise.locationListData.brgy
  );
  const contactList = useSelector(
    (state: RootState) => state.contact.contactList
  );

  const dispatch = useDispatch();

  const [industryListData, setIndustryListData] = useState<any>([]);
  const [motherCompanyListData, setMotherCompanyListData] = useState<any>([]);
  const [regionListItem, setRegionListItem] = useState<any>([]);
  const [filteredProvinceList, setFilteredProvinceList] = useState([
    { name: "Please select a Region first", value: "", region: "" },
  ]);
  const [filteredCityList, setFilteredCityList] = useState([
    { name: "Please select a Province first", value: "", province: "" },
  ]);
  const [filteredBrgyList, setFilteredBrgyList] = useState([
    { name: "Please select a City first", value: "", city: "" },
  ]);

  const [bdoUserListData, setBDOListData] = useState<any>([]);

  const [openErrorModal, setOpenErrorModal] = React.useState<any>(false);
  const [disableAffiliates, setDisableAffiliates] = useState(true);
  const [customOption, setCustomOption] = useState(true);
  // const [options, setOptions] = useState<any>([]);
  // const [value, setValue] = useState<any>(null);
  // const [applicantContactPerson, setApplicantContactPerson] = useState<any>("");
  const [contactListData, setContactListData] = useState<any>([]);
  // const [franchiseeValue, setFranchiseeValue] = useState<any>("");
  const [applicantBusinessAddress, setApplicantBusinessAddress] =
    useState<string>("");
  // const [applicantContactNumber, setApplicantContactNumber] =
  //   useState<string>("");
  // const [applicantEmailAddress, setApplicantEmailAddress] =
  //   useState<string>("");
  // const [applicantName, setApplicantName] = useState<string>("");

  const contactPerson = useRef(null);

  const handleCloseErrorModal = () => {
    setOpenErrorModal(false);
    dispatch(clearValidationModalContent());
  };

  useEffect(() => {
    if (validationContent && validationContent.error) {
      if (validationContent.title === "Error") {
        setOpenErrorModal(true);
      }
    }
  }, [validationContent]);

  useEffect(() => {
    dispatch(fetchIndustryList());
    dispatch(fetchLocationRegions());
    dispatch(fetchBDOUsers());
    dispatch(fetchMotherCompanyList());
    dispatch(fetchContacts());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (industryList) {
      setIndustryListData(industryList);
    }
  }, [industryList]);

  useEffect(() => {
    if (bdoUserList) {
      setBDOListData(bdoUserList);
    }
  }, [bdoUserList]);

  useEffect(() => {
    if (motherCompanyList) {
      setMotherCompanyListData(motherCompanyList);
    }
  }, [motherCompanyList]);

  useEffect(() => {
    if (is_mother_company && disableAffiliates) {
      handleInputAffiliates(
        "reset",
        { corporate_name: "", _id: "" },
        "company_affiliates"
      );
    } else if (!is_mother_company && !disableAffiliates) {
      handleInputAffiliates(
        "validate",
        { corporate_name: "", _id: "" },
        "company_affiliates"
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [disableAffiliates, is_mother_company]);

  useEffect(() => {
    if (company_affiliates === "" || company_affiliates === undefined) {
      setCustomOption(true);
    } else {
      setCustomOption(false);
    }
  }, [company_affiliates]);

  useEffect(() => {
    switch (channel_of_request) {
      case "Broker":
        const brkrs = contactList
          .filter((c: any) => c.type === "Broker" && c.status === "Active")
          .map((brk: any) => brk.name);
        console.log(brkrs);
        setContactListData(brkrs);
        break;
      case "New Business Agency":
        const agnts = contactList
          .filter((c: any) => c.type === "Agent" && c.status === "Active")
          .map((agnts: any) => agnts.name);
        setContactListData(agnts);
        break;
      case "New Business Associate":
        const asscts = contactList
          .filter((c: any) => c.type === "Agent" && c.status === "Active")
          .map((asscts: any) => asscts.name);
        setContactListData(asscts);
        break;
      case "Called-In Request for Proposal":
        const bdo = bdoUserListData.map((bdo: any) => bdo.full_name);
        setContactListData(bdo);
        break;
      case "Walk-In Request for Proposal":
        const vBdo = bdoUserListData.map((bdo: any) => bdo.full_name);
        setContactListData(vBdo);
        break;
      case "In-House":
        const hBdo = bdoUserListData.map((bdo: any) => bdo.full_name);
        setContactListData(hBdo);
        break;
      case "Business Development Officer":
          if(group_name !== 'BDO') {
            const bBdo = bdoUserListData.map((bdo: any) => bdo.full_name);
            setContactListData(bBdo);
          } else setContactListData([]);
          break;
      default:
        setContactListData([]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [channel_of_request]);

  let currentUser = "";
  // const decryptedFirstName = decryptCredentials(firstname);
  // const decryptedMiddleName = decryptCredentials(middlename);
  // const decryptedLastName = decryptCredentials(lastname);
  if (suffix) {
    currentUser = `${firstname} ${middlename} ${lastname} ${suffix}`;
    // currentUser = `${decryptedFirstName} ${decryptedMiddleName} ${decryptedLastName} ${suffix}}`;
  } else {
    currentUser = `${firstname} ${middlename} ${lastname}`;
    // currentUser = `${decryptedFirstName} ${decryptedMiddleName} ${decryptedLastName}`;
  }

  // useEffect(() => {
  //   let decryptedFName = "";
  //   let decryptedMName = "";
  //   let decryptedLName = "";

  //   if (firstname) {
  //     decryptedFName = decryptCredentials(firstname);
  //     setDecryptedFirstName(decryptedFName);
  //   }

  //   if (middlename) {
  //     decryptedMName = decryptCredentials(middlename);
  //     setDecryptedMiddleName(decryptedMName);
  //   }

  //   if (lastname) {
  //     decryptedLName = decryptCredentials(lastname);
  //     setDecrptedLastName(decryptedLName);
  //   }

  //   let fullName;
  //   if (suffix) {
  //     fullName = `${decryptedFName} ${decryptedMName} ${decryptedLName} ${suffix}`;
  //     setCurrentUser(fullName);
  //   } else {
  //     fullName = `${decryptedFName} ${decryptedMName} ${decryptedLName}`;
  //     setCurrentUser(fullName);
  //   }

  // }, [firstname, middlename, lastname]);

  const pattern = new RegExp(/\D/);
  const initialNameTypeValue = { name: "", type: "" };
  const invalidEmptyObject = {
    valid: false,
    inputClass: "",
    spanClass: "",
    text: "",
  };
  const emptyNameTypeValue = {
    name: invalidEmptyObject,
    type: invalidEmptyObject,
  };

  // const setOptionList = (value: any) => {
  //   let newOptionList = [];
  //   if (value) {
  //     newOptionList = contactListData.filter((option: any) => {
  //       const contact_details = `${option.code} - ${option.contact_person} (${option.name})`;

  //       return (
  //         option.code.toLowerCase().includes(value.toLowerCase()) ||
  //         option.contact_person.toLowerCase().includes(value.toLowerCase()) ||
  //         contact_details.toLowerCase() === value.toLowerCase()
  //       );
  //     });
  //   }
  //   setOptions(
  //     newOptionList.sort(function (a: any, b: any) {
  //       if (a.code < b.code) {
  //         return -1;
  //       }
  //       if (a.code > b.code) {
  //         return 1;
  //       }
  //       return 0;
  //     })
  //   );
  // };

  const handleChangeCheckbox = (e: any) => {
    const { checked } = e.target;

    setDisableAffiliates(checked);
    handleInputIsMotherCompany(checked, "is_mother_company");
  };

  const handleChangeAddress = (keyName: string) => (event: any) => {
    let emptyAddress = {};
    // handleInputCursor(event);

    let ss = event.target.selectionStart;
    let se = event.target.selectionEnd;

    if (
      keyName === "region" ||
      keyName === "province" ||
      keyName === "city" ||
      keyName === "brgy"
    ) {
      // eslint-disable-next-line
      event.target.value = event.target.value;
    } else {
      // eslint-disable-next-line
      event.target.value = event.target.value.toUpperCase();
    }

    event.target.selectionStart = ss;
    event.target.selectionEnd = se;
    const { value } = event.target;

    if (keyName === "bldg_no" || keyName === "zip_code") {
      Object.assign(emptyAddress, { [keyName]: value });
      handleInputBusinessAddress(event, emptyAddress);
    } else {
      const new_value = value;
      switch (keyName) {
        case "region":
          Object.assign(emptyAddress, {
            region: new_value,
            province: "",
            city: "",
            brgy: "",
          });
          handleInputBusinessAddress(event, emptyAddress);
          break;
        case "province":
          Object.assign(emptyAddress, {
            province: new_value,
            city: "",
            brgy: "",
          });
          handleInputBusinessAddress(event, emptyAddress);
          break;
        case "city":
          Object.assign(emptyAddress, { city: new_value, brgy: "" });
          handleInputBusinessAddress(event, emptyAddress);
          break;
        default:
          Object.assign(emptyAddress, { [keyName]: new_value });
          handleInputBusinessAddress(event, emptyAddress);
          break;
      }
    }
  };

  const handleIsRefranchiseChange = (e: any) => {
    const { value } = e.target;
    setReFranchise(value === "Yes");
    handleInputRefranchise(e, "isReFranchiseValid");
  };

  const handleChannelOfRequest = (e: any) => {
    handleInputChannelOfRequest(e, "isChannelOfRequestValid");
  };

  const handleClientId = (e: any) => {
    const { value } = e.target;
    if (!pattern.test(value)) {
      handleInputClientId(e, "isClientIdValid");
    }
  };

  const handleClickRadio = (e: any, str: string) => {
    const { name } = e.target;
    setRadioEntry(!str);
    handleRadioChange(name, str, "isEntryNameValid");
  };

  const handleSourceFundChange = (e: any) => {
    const { name, value } = e.target;
    handleRadioChange(name, value, "isSourceOfFundsValid");
  };

  const handleHMOProviderChange = (e: any) => {
    const { name, value } = e.target;
    handleRadioChange(name, value, "isProviderNameValid");
  };

  const [maxTIN, seTMaxTIN] = useState(16);

  useEffect(() => {
    if (tax_number && tax_number.replace(/[^0-9]/g, "").length === 12) {
      seTMaxTIN(maxTIN - 1);
    } else {
      seTMaxTIN(16);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tax_number]);

  const handleTaxInputChange = (e: any) => {
    const { name, value } = e.target;
    handleInputTIN(name, "isTaxNumberValid", value);
  };

  const handleNAInput = (e: any, id: string) => {
    const { name, value } = e.target;
    handleNumNA(name, id, value);
  };

  const blockSpecialChar = (event: any) => {
    var regex = new RegExp("^[0-9]+$");
    var key = String.fromCharCode(
      !event.charCode ? event.which : event.charCode
    );
    if (!regex.test(key)) {
      event.preventDefault();
      return false;
    }
  };

  // HANDLE ADD AUTHORIZED PERSON
  const handleAuthorizeChange = (e: any, i: number) => {
    handleInputCursor(e);

    const { name, value } = e.target;
    let newAuthPersonList = authorized_contact_persons.map(
      (content: any, index: number) => {
        return i === index ? { ...content, [name]: value } : content;
      }
    );
    let lastIndex = inputStatus.isAuthorizedContactPersons.findIndex(
      (x, index) =>
        index !== i &&
        x.name.valid === false &&
        x.type.valid === false &&
        x.name.text === "" &&
        x.type.text === ""
    );
    handleInputArray(
      newAuthPersonList,
      "authorized_contact_persons",
      "isAuthorizedContactPersons",
      lastIndex
    );
  };

  const handleAddAuth = () => {
    let notAllowAddAuth =
      authorized_contact_persons.filter(
        ({ name, type }) => name === "" || type === ""
      ).length > 0;
    if (!notAllowAddAuth) {
      const newArr = [...authorized_contact_persons, initialNameTypeValue];
      handleInputArray(
        newArr,
        "authorized_contact_persons",
        "isAuthorizedContactPersons",
        newArr.length - 1
      );
    } else {
      handleInputArray(
        authorized_contact_persons,
        "authorized_contact_persons",
        "isAuthorizedContactPersons"
      );
    }
  };

  const handleRemoveAuth = (index: number) => {
    const list = [...authorized_contact_persons];
    list.splice(index, 1);
    let lastIndex = inputStatus.isAuthorizedContactPersons
      .filter((value, i) => i !== index)
      .findIndex(
        (x) =>
          x.name.valid === false &&
          x.type.valid === false &&
          x.name.text === "" &&
          x.type.text === ""
      );
    handleInputArray(
      list,
      "authorized_contact_persons",
      "isAuthorizedContactPersons",
      lastIndex
    );
  };

  // HANDLE ADD STAKEHOLDER
  const handleSHInputChange = (e: any, i: number) => {
    handleInputCursor(e);

    const { name, value } = e.target;
    let newStakeHolderList = stakeholders.map((content: any, index: number) => {
      return i === index ? { ...content, [name]: value } : content;
    });
    let lastIndex = inputStatus.isStakeholdersValid.findIndex(
      (x, index) =>
        index !== i &&
        x.name.valid === false &&
        x.type.valid === false &&
        x.name.text === "" &&
        x.type.text === ""
    );
    handleInputArray(
      newStakeHolderList,
      "stakeholders",
      "isStakeholdersValid",
      lastIndex
    );
  };

  const handleAddSH = () => {
    let notAllowAddStakeHolders =
      stakeholders.filter(({ name, type }) => name === "" || type === "")
        .length > 0;
    if (!notAllowAddStakeHolders) {
      const newArr = [...stakeholders, initialNameTypeValue];
      handleInputArray(
        newArr,
        "stakeholders",
        "isStakeholdersValid",
        newArr.length - 1
      );
    } else {
      handleInputArray(stakeholders, "stakeholders", "isStakeholdersValid");
    }
  };

  const handleRemoveSH = (index: number) => {
    const list = [...stakeholders];
    list.splice(index, 1);
    let lastIndex = inputStatus.isStakeholdersValid
      .filter((value, i) => i !== index)
      .findIndex(
        (x) =>
          x.name.valid === false &&
          x.type.valid === false &&
          x.name.text === "" &&
          x.type.text === ""
      );
    handleInputArray(list, "stakeholders", "isStakeholdersValid", lastIndex);
  };

  // HANDLE ADD SIGNATORIES

  const handleSignatoriesChange = (e: any, i: number) => {
    handleInputCursor(e);
    const { name, value } = e.target;
    let newSignatoriesList = signatories.map((content: any, index: number) => {
      return i === index ? { ...content, [name]: value } : content;
    });
    let lastIndex = inputStatus.isSignatoriesValid.findIndex(
      (x, index) =>
        index !== i &&
        x.name.valid === false &&
        x.type.valid === false &&
        x.name.text === "" &&
        x.type.text === ""
    );
    handleInputArray(
      newSignatoriesList,
      "signatories",
      "isSignatoriesValid",
      lastIndex
    );
  };

  const handleAddSignatories = () => {
    let notAllowAddSignatories =
      signatories.filter(({ name, type }) => name === "" || type === "")
        .length > 0;
    if (!notAllowAddSignatories) {
      const newArr = [...signatories, initialNameTypeValue];
      handleInputArray(
        newArr,
        "signatories",
        "isSignatoriesValid",
        newArr.length - 1
      );
    } else {
      handleInputArray(signatories, "signatories", "isSignatoriesValid");
    }
  };

  const handleRemoveSignatories = (index: number) => {
    const list = [...signatories];
    list.splice(index, 1);
    let lastIndex = inputStatus.isSignatoriesValid
      .filter((value, i) => i !== index)
      .findIndex(
        (x) =>
          x.name.valid === false &&
          x.type.valid === false &&
          x.name.text === "" &&
          x.type.text === ""
      );
    handleInputArray(list, "signatories", "isSignatoriesValid", lastIndex);
  };

  let radioEntryValue = entry_id;
  const radioEntryArray = ["Called-In", "Email", "Online/Website"];
  useEffect(() => {
    if (
      radioEntryValue !== undefined &&
      radioEntryArray.indexOf(radioEntryValue) < 0
    ) {
      setRadioEntry(true);
    } else if (radioEntryValue === undefined) {
      setRadioEntry(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [radioEntryValue]);

  let selectIsRefranchise = is_refranchise;
  useEffect(() => {
    if (selectIsRefranchise !== undefined && selectIsRefranchise === "Yes") {
      setReFranchise(true);
    } else if (
      selectIsRefranchise !== undefined &&
      selectIsRefranchise === "No"
    ) {
      setReFranchise(false);
    } else {
      setReFranchise(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectIsRefranchise]);

  useEffect(() => {
    if (regionList) {
      let regions = regionList.filter((item: any) => item.status === "active");
      setRegionListItem(regions);
    } else {
      setFilteredProvinceList([
        { name: "Please select a Region first", value: "", region: "" },
      ]);
    }
  }, [regionList]);

  useEffect(() => {
    if (business_address && business_address.region) {
      if (business_address && !business_address.city) {
        setFilteredBrgyList([
          { name: "Please select a City first", value: "", city: "" },
        ]);
        setFilteredCityList([
          { name: "Please select a Province first", value: "", province: "" },
        ]);
        setFilteredProvinceList([
          { name: "...list loading", value: "", region: "" },
        ]);
      } else {
        setFilteredProvinceList([
          { name: "...list loading", value: "", region: "" },
        ]);
      }
      dispatch(fetchLocationProvince());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [business_address && business_address.region]);

  useEffect(() => {
    if (provinceList && business_address && business_address.region) {
      let provinces = provinceList.filter(
        (item: any) =>
          item.region === business_address.region && item.status === "active"
      );

      if (provinces.length > 0) {
        setFilteredProvinceList(provinces);
      } else {
        setFilteredProvinceList([
          { name: "list not available", value: "", region: "" },
        ]);
      }
    } else {
      setFilteredCityList([
        { name: "Please select a Province first", value: "", province: "" },
      ]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [provinceList]);

  useEffect(() => {
    if (business_address && business_address.province) {
      dispatch(fetchLocationCities());

      if (business_address && !business_address.city) {
        setFilteredBrgyList([
          { name: "Please select a City first", value: "", city: "" },
        ]);
        setFilteredCityList([
          { name: "...list loading", value: "", province: "" },
        ]);
      } else {
        setFilteredCityList([
          { name: "...list loading", value: "", province: "" },
        ]);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [business_address && business_address.province]);

  useEffect(() => {
    if (cityList && business_address && business_address.province) {
      let cities = cityList.filter(
        (item: any) =>
          item.province === business_address.province && item.status === "active"
      );
      if (cities.length > 0) {
        setFilteredCityList(cities);
      } else {
        setFilteredCityList([
          { name: "list not available", value: "", province: "" },
        ]);
      }
    } else if (cityList && business_address && business_address.region) {
      let cities = cityList.filter(
        (item: any) =>
          item.region === business_address.region && item.status === "active"
      );
      if (cities.length > 0) {
        setFilteredCityList(cities);
      } else {
        setFilteredCityList([
          { name: "list not available", value: "", province: "" },
        ]);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cityList]);

  useEffect(() => {
    if (business_address && business_address.city) {
      console.log('ams barangay', business_address.brgy,isEditing);
      dispatch(fetchLocationBrgys(business_address.city, business_address.region, business_address.province));
      if (business_address && !business_address.brgy) {
        setFilteredBrgyList([{ name: "...list loading", value: "", city: "" }]);
      } 
      // else if (
      //   business_address &&
      //   business_address.brgy !== "" &&
      //   isEditing
      // ) {
      //   if (!isListFitched) {
      //     dispatch(fetchLocationBrgys(business_address.city, business_address.region, business_address.province));
      //     setFilteredBrgyList([
      //       { name: "...list loading", value: "", city: "" },
      //     ]);
      //   }
      // }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [business_address && business_address.city]);

  useEffect(() => {
    if (brgyList && business_address &&business_address.city) {
      let brgys = brgyList.filter(
        (item: any) =>
        item.city === business_address.city && item.status === "active"
      )
      if (brgyList.length > 0) {
        setFilteredBrgyList(brgys);
      } else {
        setFilteredBrgyList([
          { name: "list not available", value: "", city: "" },
        ]);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [brgyList]);

  useEffect(() => {
    const entries = Object.entries(inputStatus);
    for (let index = 0; index < entries.length; index++) {
      const key = entries[index][0];
      const objIError = entries[index][1];

      if (objIError.constructor !== Array) {
        if (!objIError.valid) {
          setErrorKey(key);
          break;
        }
      } else {
        for (let counter = 0; counter < objIError.length; counter++) {
          let otherIErrorName = objIError[counter]["name"];
          let otherIErrorType = objIError[counter]["type"];
          if (!otherIErrorName.valid || !otherIErrorType.valid) {
            setErrorKey(`${key}-${counter}`);
            return;
          }
        }
      }
    }
  }, [inputStatus]);

  const handleApplicantAddress = (applicant: any) => {
    if (applicant?.business_address) {
      const address = applicant.business_address;

      const completeAddress = `${address.floor ?? ""} ${address.unit ?? ""} ${
        address.bldg_name ?? ""
      } ${address.street ?? ""} ${address.brgy ?? ""} ${address.city ?? ""} ${
        address.province ?? ""
      } ${address.country ?? ""}`;
      return setApplicantBusinessAddress(completeAddress.trim().toUpperCase());
    }

    return setApplicantBusinessAddress("");
  };

  // const handleApplicantContactNumber = (applicant: any) => {
  //   setApplicantContactNumber(applicant?.contact_number ?? "");
  // };

  // const handleApplicantEmailAddress = (applicant: any) => {
  //   setApplicantEmailAddress(applicant?.email_address ?? "");
  // };

  // useEffect(() => {
  //   if (applicantContactPerson) {
  //     handleFranchiseeContactPersonInput(applicantContactPerson);
  //   }
  // }, [applicantContactPerson]);

  useEffect(() => {
    if (applicantBusinessAddress) {
      handleFranchiseeBusinessAddress(applicantBusinessAddress);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [applicantBusinessAddress]);

  // useEffect(() => {
  //   if (applicantContactNumber) {
  //     handleFranchiseeContactNumber(applicantContactNumber);
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [applicantContactNumber]);

  // useEffect(() => {
  //   if (applicantEmailAddress) {
  //     handleFranchiseeEmail(applicantEmailAddress);
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [applicantEmailAddress]);

  // useEffect(() => {
  //   if (applicant_name) {
  //     setFranchiseeValue(applicant_name);
  //   }
  // }, [applicant_name]);

  return (
    <>
      <div className="main vni-py-8 vni-px-12" id="entry_request">
        <div className="vni-flex">
          <div
            className="vni-block vni-mr-10"
            ref={errorKey === "isEntryNameValid" ? inputRef : null}
          >
            <h3 className="vni-block vni-text-base">
              Entry of Franchise Request
            </h3>
            <span className="vni-flex vni-my-4">
              <span className="vni-block custom-rdo">
                <input
                  type="radio"
                  name="entry_id"
                  id="1"
                  checked={radioEntryValue === radioEntryArray[0]}
                  onClick={(e) => handleClickRadio(e, radioEntryArray[0])}
                />
                <label data-cy="called_in_radioBtn" htmlFor="1">
                  Called-In
                </label>
              </span>
              <span className="vni-block custom-rdo">
                <input
                  type="radio"
                  name="entry_id"
                  id="2"
                  checked={radioEntryValue === radioEntryArray[1]}
                  onClick={(e) => handleClickRadio(e, radioEntryArray[1])}
                />
                <label data-cy="email_radioBtn" htmlFor="2">
                  Email
                </label>
              </span>
              <span className="custom-rdo">
                <input
                  type="radio"
                  name="entry_id"
                  id="3"
                  checked={radioEntryValue === radioEntryArray[2]}
                  onClick={(e) => handleClickRadio(e, radioEntryArray[2])}
                />
                <label data-cy="online_website_radioBtn" htmlFor="3">
                  Online/Website
                </label>
              </span>
              <span className="custom-rdo">
                <input
                  type="radio"
                  name="entry_id"
                  id="4"
                  checked={
                    radioEntryValue !== undefined &&
                    radioEntryArray.indexOf(radioEntryValue) < 0
                  }
                  onClick={(e) => handleClickRadio(e, "")}
                />
                <label data-cy="others_radioBtn" htmlFor="4">
                  Others
                </label>
              </span>
            </span>

            {!radioEntry || entry_id === undefined ? (
              <input
                type="text"
                data-cy="others_entry_text"
                minLength={1}
                maxLength={100}
                id="isEntryNameValid"
                name="entry_id"
                className="CustomInput disabled"
                disabled
                placeholder=""
                value=""
              />
            ) : (
              <input
                type="text"
                data-cy="others_entry_text"
                minLength={1}
                maxLength={100}
                id="isEntryNameValid"
                name="entry_id"
                className={
                  //@ts-ignore
                  "CustomInput " + inputStatus.isEntryNameValid.inputClass ?? ""
                }
                onChange={handleInputChange}
                value={entry_id ?? ""}
              />
            )}
            <span className={inputStatus.isEntryNameValid.spanClass}>
              {inputStatus.isEntryNameValid.text}
            </span>
          </div>
          <div
            className="vni-w-1/5"
            style={{ marginTop: "3.5rem" }}
            ref={errorKey === "isDateAndTimeReceivedValid" ? inputRef : null}
          >
            <label htmlFor="">
              Date and Time Received{" "}
              {!radioEntry || entry_id === undefined ? (
                <span className="vni-text-red-500">*</span>
              ) : (
                ""
              )}
            </label>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DateTimePicker
                format="MM/dd/yyyy hh:mma"
                data-cy="date_time_received"
                className="CustomInput vni-datepicker create-date-fix"
                name="date_time_received"

                maxDate={new Date()}

                onChange={(value) =>
                  handleDateAndTimeReceived(
                    value,
                    "isDateAndTimeReceivedValid",
                    "date_time_received"
                  )
                }
                value={date_time_received ? new Date(date_time_received) : null}
                slotProps={{
                  textField: {
                    variant: 'outlined',
                    error:
                      !inputStatus.isDateAndTimeReceivedValid.valid &&
                      !(inputStatus.isDateAndTimeReceivedValid.text === ""),
                  },
                }}
              />
            </LocalizationProvider>
            <span
              style={{ maxWidth: 270 }}
              className={inputStatus.isDateAndTimeReceivedValid.spanClass}
            >
              {inputStatus.isDateAndTimeReceivedValid.text}
            </span>
          </div>
        </div>
        <div className="vni-block vni-mt-10">
          <h3 className="vni-block vni-text-xl vni-font-bold vni-mb-5">
            Client Information
          </h3>
          <Grid className="vni-flex vni-mb-5">
            <Grid container className="vni-flex">
              <Grid container className="vni-flex vni-mb-5">
                <Grid
                  item
                  xs={4}
                  className="vni-w-1/2 vni-pr-8"
                  ref={errorKey === "isChannelOfRequestValid" ? inputRef : null}
                >
                  <label htmlFor="">
                    Channel of Request{" "}
                    <span className="vni-text-red-500">*</span>
                  </label>
                  <Select
                    data-cy="channel_of_request"
                    value={channel_of_request ?? ""}
                    className={
                      `CustomSelect selectInput ${group_name === 'BDO' ? "disabled" : ""}` +
                      inputStatus.isChannelOfRequestValid.inputClass
                    }
                    disabled={group_name === 'BDO'}
                    name="channel_of_request"
                    onChange={handleChannelOfRequest}
                  >
                    {channelOfRequestList.map((option, index) => (
                      <MenuItem
                        data-cy={`channel-option-${index + 1}`}
                        key={index}
                        value={option.value}
                      >
                        {option.name}
                      </MenuItem>
                    ))}
                  </Select>
                  <span
                    className={inputStatus.isChannelOfRequestValid.spanClass}
                  >
                    {inputStatus.isChannelOfRequestValid.text}
                  </span>
                </Grid>
              </Grid>

              <Grid container className="vni-flex">
                <Grid
                  item
                  xs={3}
                  className="vni-w-1/2 vni-pr-8"
                  ref={errorKey === "isReFranchiseValid" ? inputRef : null}
                >
                  <label htmlFor="">
                    Is this a Returning Franchise?{" "}
                    <span className="vni-text-red-500">*</span>
                  </label>
                  <Select
                    value={is_refranchise ?? ""}
                    className={
                      "CustomSelect selectInput " +
                      inputStatus.isReFranchiseValid.inputClass
                    }
                    name="is_refranchise"
                    data-cy="is_refranchise"
                    onChange={handleIsRefranchiseChange}
                  >
                    <MenuItem data-cy="returning-yes" key={0} value={"Yes"}>
                      Yes
                    </MenuItem>
                    <MenuItem data-cy="returning-no" key={1} value={"No"}>
                      No
                    </MenuItem>
                  </Select>
                  <span className={inputStatus.isReFranchiseValid.spanClass}>
                    {inputStatus.isReFranchiseValid.text}
                  </span>
                </Grid>
                <Grid
                  item
                  xs={3}
                  className="vni-w-1/2 vni-pr-8"
                  ref={errorKey === "isClientIdValid" ? inputRef : null}
                >
                  <label htmlFor="">
                    Client ID <span className="vni-text-red-500">*</span>
                  </label>
                  {!reFranchise ? (
                    <input
                      type="text"
                      name="client_id"
                      data-cy="no_refranchise"
                      minLength={1}
                      maxLength={100}
                      className="CustomInput vni-w-full disabled"
                      disabled
                      value=""
                    />
                  ) : (
                    <input
                      type="text"
                      name="client_id"
                      data-cy="client_id"
                      minLength={8}
                      maxLength={8}
                      id="isClientIdValid"
                      className={
                        "CustomInput vni-w-full " +
                        inputStatus.isClientIdValid.inputClass
                      }
                      onChange={handleClientId}
                      onBlur={handleClientId}
                      value={client_id ?? ""}
                    />
                  )}
                  <span className={inputStatus.isClientIdValid.spanClass}>
                    {inputStatus.isClientIdValid.text}
                  </span>
                </Grid>
                <Grid
                  item
                  xs={6}
                  className="vni-w-1/2"
                  ref={errorKey === "isOldAccountNameValid" ? inputRef : null}
                >
                  <label htmlFor="">Old Account Name</label>
                  <input
                    type="text"
                    name="old_account_name"
                    data-cy="old_account_name"
                    minLength={1}
                    maxLength={100}
                    id="isOldAccountNameValid"
                    className={
                      "CustomInput vni-w-full " +
                      inputStatus.isOldAccountNameValid.inputClass
                    }
                    onChange={handleInputChange}
                    onBlur={handleInputChange}
                    value={old_account_name ?? ""}
                  />
                  <span className={inputStatus.isOldAccountNameValid.spanClass}>
                    {inputStatus.isOldAccountNameValid.text}
                  </span>
                </Grid>
              </Grid>
            </Grid>
          </Grid>

          <div className="vni-flex vni-mb-5">
            <div
              className="vni-w-1/2 vni-pr-8"
              ref={errorKey === "isCorporateNameValid" ? inputRef : null}
            >
              <label htmlFor="">
                Registered Corporate Name{" "}
                <span className="vni-text-red-500">*</span>
              </label>
              <CustomCheckbox
                disableTouchRipple
                data-cy="checkbox-mother-company"
                checked={
                  isEditing && is_mother_company !== undefined
                    ? is_mother_company
                    : is_mother_company !== undefined
                    ? is_mother_company
                    : disableAffiliates
                }
                onChange={handleChangeCheckbox}
              />
              <label className="customCheckboxLabel">Is this a mother company?</label>
              <input
                type="text"
                id="isCorporateNameValid"
                data-cy="corporate_name"
                name="corporate_name"
                minLength={1}
                maxLength={100}
                className={
                  "CustomInput vni-w-full " +
                  inputStatus.isCorporateNameValid.inputClass
                }
                onChange={handleInputChange}
                value={corporate_name ?? ""}
              />
              <span className={inputStatus.isCorporateNameValid.spanClass}>
                {inputStatus.isCorporateNameValid.text}
              </span>
            </div>
            <div
              className="vni-w-1/2"
              ref={errorKey === "isBrandNameValid" ? inputRef : null}
            >
              <label htmlFor="">
                Trade / Brand Name <span className="vni-text-red-500">*</span>
              </label>
              <input
                type="text"
                id="isBrandNameValid"
                data-cy="brand_name"
                minLength={1}
                maxLength={100}
                onChange={handleInputChange}
                name="brand_name"
                className={
                  "CustomInput vni-w-full " +
                  inputStatus.isBrandNameValid.inputClass
                }
                value={brand_name ?? ""}
              />
              <span className={inputStatus.isBrandNameValid.spanClass}>
                {inputStatus.isBrandNameValid.text}
              </span>
            </div>
          </div>

          <div className="vni-flex vni-flex-col vni-mb-5">
            <div className="vni-flex d-flex-row">
              <Grid
                className="vni-w-1/2 vni-pr-8"
                ref={errorKey === "isCompanyAffiliatesValid" ? inputRef : null}
              >
                <span className="vni-block vni-mb-5">
                  <label htmlFor="">
                    Specify Mother Company
                  </label>
                  <Autocomplete
                    openOnFocus={true}
                    classes={{ option: customOption ? optionStyle.option : "" }}
                    className={`CustomAutocomplete "  + ${
                      inputStatus.isCompanyAffiliatesValid.inputClass
                    } + 
                                        ${
                                          (disableAffiliates &&
                                            !isEditing &&
                                            (is_mother_company === undefined ||
                                              is_mother_company)) ||
                                          (disableAffiliates &&
                                            isEditing &&
                                            is_mother_company)
                                            ? "disabled"
                                            : ""
                                        }`}
                    value={
                      company_affiliates
                        ? {
                            corporate_name: company_affiliates,
                            _id: company_affiliates_id,
                          }
                        : { corporate_name: "", _id: "" }
                    }
                    disabled={
                      isEditing
                        ? is_mother_company
                        : is_mother_company !== undefined
                        ? is_mother_company
                        : disableAffiliates
                    }
                    id="isCompanyAffiliatesValid"
                    data-cy="member_companies"
                    onChange={(e, value) => {
                      handleInputAffiliates(
                        "input",
                        value ? value : { corporate_name: "", _id: "" },
                        "company_affiliates"
                      );
                    }}
                    isOptionEqualToValue={(option, value) =>
                      value.corporate_name === "" ||
                      option.corporate_name === value.corporate_name
                    }
                    options={motherCompanyListData}
                    getOptionLabel={(option: any) => option.corporate_name}
                    renderInput={(params) => (
                      <div ref={params.InputProps.ref}>
                        <input
                          style={AutocompleteInputStyle}
                          type="text"
                          {...params.inputProps}
                        />
                      </div>
                    )}
                  />
                  <span
                    className={
                      !disableAffiliates &&
                      !inputStatus.isCompanyAffiliatesValid.valid
                        ? inputStatus.isCompanyAffiliatesValid.spanClass
                        : ""
                    }
                  >
                    {!disableAffiliates &&
                    !inputStatus.isCompanyAffiliatesValid.valid
                      ? inputStatus.isCompanyAffiliatesValid.text
                      : ""}
                  </span>
                </span>
              </Grid>
              <Grid className="vni-w-1/2">
                <span className="vni-flex">
                  <span
                    className="vni-w-7/12 vni-pr-4"
                    ref={errorKey === "isTaxNumberValid" ? inputRef : null}
                  >
                    <label htmlFor="">Tax Identification Number</label>

                    <PatternFormat
                      id="isTaxNumberValid"
                      type="text"
                      data-cy="tax_number"
                      onKeyPress={blockSpecialChar}
                      className={
                        "CustomInput vni-w-full " +
                        inputStatus.isTaxNumberValid.inputClass
                      }
                      onChange={handleTaxInputChange}
                      name="tax_number"
                      value={tax_number ?? ""}
                      format="###-###-###-###"
                      allowEmptyFormatting
                      maxLength={maxTIN}
                    />

                    <span className={inputStatus.isTaxNumberValid.spanClass}>
                      {inputStatus.isTaxNumberValid.text}
                    </span>
                  </span>
                  <span
                    className="vni-w-5/12 vni-pl-4"
                    ref={errorKey === "isCompanyNumberValid" ? inputRef : null}
                  >
                    <label htmlFor="">
                      Company Contact Number{" "}
                      <span className="vni-text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="isCompanyNumberValid"
                      data-cy="company_contact"
                      minLength={1}
                      maxLength={11}
                      // onKeyDown={allowNumbersOnKeyDown}
                      // onKeyPress={blockSpecialChar}
                      className={
                        "CustomInput vni-w-full " +
                        inputStatus.isCompanyNumberValid.inputClass
                      }
                      onChange={(e: any) =>
                        handleNAInput(e, "isCompanyNumberValid")
                      }
                      name="company_number"
                      value={company_number ?? ""}
                    />
                    <span
                      className={inputStatus.isCompanyNumberValid.spanClass}
                    >
                      {inputStatus.isCompanyNumberValid.text}
                    </span>
                  </span>
                </span>
              </Grid>
            </div>
          </div>
          <Grid className="vni-w-full vni-mb-5">
            <label htmlFor="">
              Registered Business Address (Complete Address)
            </label>
          </Grid>
          <Grid className="vni-flex vni-flex-col vni-mb-5">
            <Grid className="vni-flex vni-w-5/6">
              <Grid className="vni-w-1/3">
                <div className="vni-flex vni-mb-5">
                  <span
                    className="vni-w-1/2 vni-pr-8"
                    ref={errorKey === "isFloorValid" ? inputRef : null}
                  >
                    <label htmlFor="">
                      Floor<span className="vni-text-red-500">*</span>
                    </label>
                    <input
                      name="isFloorValid"
                      type="text"
                      id="business_address_floor"
                      data-cy="business_address_floor"
                      minLength={1}
                      maxLength={50}
                      onChange={handleChangeAddress("floor")}
                      className={
                        "CustomInput vni-w-full " +
                        inputStatus.isFloorValid.inputClass
                      }
                      value={
                        business_address ? business_address.floor ?? "" : ""
                      }
                    />
                    <span className={inputStatus.isFloorValid.spanClass}>
                      {inputStatus.isFloorValid.text}
                    </span>
                  </span>
                  <span
                    className="vni-w-1/2 vni-pr-8"
                    ref={errorKey === "isUnitValid" ? inputRef : null}
                  >
                    <label htmlFor="">
                      Unit <span className="vni-text-red-500">*</span>
                    </label>
                    <input
                      name="isUnitValid"
                      type="text"
                      id="business_address_unit"
                      data-cy="business_address_unit"
                      onChange={handleChangeAddress("unit")}
                      className={
                        "CustomInput vni-w-full " +
                        inputStatus.isUnitValid.inputClass
                      }
                      value={
                        business_address ? business_address.unit ?? "" : ""
                      }
                    />
                    <span className={inputStatus.isUnitValid.spanClass}>
                      {inputStatus.isUnitValid.text}
                    </span>
                  </span>
                </div>
              </Grid>

              <Grid className="vni-w-1/3">
                <div
                  className="vni-mb-5 vni-pr-8"
                  ref={errorKey === "isBldgNoValid" ? inputRef : null}
                >
                  <label htmlFor="">
                    Building Number <span className="vni-text-red-500">*</span>
                  </label>
                  <input
                    name="isBldgNoValid"
                    type="text"
                    id="business_address_bldg_no"
                    data-cy="business_address_bldg_no"
                    onChange={handleChangeAddress("bldg_no")}
                    className={
                      "CustomInput vni-w-full " +
                      inputStatus.isBldgNoValid.inputClass
                    }
                    value={
                      business_address ? business_address.bldg_no ?? "" : ""
                    }
                  />
                  <span className={inputStatus.isBldgNoValid.spanClass}>
                    {inputStatus.isBldgNoValid.text}
                  </span>
                </div>
              </Grid>

              <Grid>
                <div
                  className="vni-mb-5 vni-pr-8"
                  ref={errorKey === "isBldgNameValid" ? inputRef : null}
                >
                  <label htmlFor="">
                    Building Name <span className="vni-text-red-500">*</span>
                  </label>
                  <input
                    name="isBldgNameValid"
                    type="text"
                    id="business_address_bldg_name"
                    data-cy="business_address_bldg_name"
                    minLength={1}
                    maxLength={50}
                    onChange={handleChangeAddress("bldg_name")}
                    className={
                      "CustomInput vni-w-full " +
                      inputStatus.isBldgNameValid.inputClass
                    }
                    value={
                      business_address ? business_address.bldg_name ?? "" : ""
                    }
                  />
                  <span className={inputStatus.isBldgNameValid.spanClass}>
                    {inputStatus.isBldgNameValid.text}
                  </span>
                </div>
              </Grid>
            </Grid>

            <Grid className="vni-flex vni-w-5/6">
              <Grid className="vni-w-1/3">
                <div
                  className="vni-mb-5 vni-pr-8"
                  ref={errorKey === "isStreetValid" ? inputRef : null}
                >
                  <label htmlFor="">
                    Street <span className="vni-text-red-500">*</span>
                  </label>
                  <input
                    name="isStreetValid"
                    type="text"
                    id="business_address_street"
                    data-cy="business_address_street"
                    minLength={1}
                    maxLength={50}
                    onChange={handleChangeAddress("street")}
                    className={
                      "CustomInput vni-w-full " +
                      inputStatus.isStreetValid.inputClass
                    }
                    value={
                      business_address ? business_address.street ?? "" : ""
                    }
                  />
                  <span className={inputStatus.isStreetValid.spanClass}>
                    {inputStatus.isStreetValid.text}
                  </span>
                </div>
              </Grid>

              <Grid className="vni-w-1/3">
                <div
                  className="vni-mb-5 vni-pr-8"
                  ref={errorKey === "isRegionValid" ? inputRef : null}
                >
                  <label htmlFor="">
                    Region <span className="vni-text-red-500">*</span>
                  </label>
                  <Select
                    name="isRegionValid"
                    value={
                      business_address ? business_address.region ?? "" : ""
                    }
                    className={
                      "CustomSelect vni-w-full selectInput " +
                      inputStatus.isRegionValid.inputClass
                    }
                    id="business_address_region"
                    data-cy="business_address_region"
                    onChange={handleChangeAddress("region")}
                  >
                    {regionListItem &&
                      regionListItem.map((option: any, index: number) => (
                        <MenuItem
                          data-cy={`region-option-${index + 1}`}
                          key={index}
                          value={option.name}
                        >
                          {option.name}
                        </MenuItem>
                      ))}
                  </Select>
                  <span className={inputStatus.isRegionValid.spanClass}>
                    {inputStatus.isRegionValid.text}
                  </span>
                </div>
              </Grid>

              <Grid className="vni-w-1/3">
                <div
                  className="vni-mb-5 vni-pr-8"
                  ref={errorKey === "isProvinceValid" ? inputRef : null}
                >
                  <label htmlFor="">
                    Province <span className="vni-text-red-500">*</span>
                  </label>
                  <Select
                    name="isProvinceValid"
                    value={business_address ? business_address.province : ""}
                    className={
                      "CustomSelect vni-w-full selectInput " +
                      inputStatus.isProvinceValid.inputClass
                    }
                    id="business_address_province"
                    data-cy="business_address_province"
                    onChange={handleChangeAddress("province")}
                  >
                    {filteredProvinceList &&
                      filteredProvinceList.map((option: any, index: any) => {
                        let isTrue: any =
                          business_address && business_address.region;
                        let isLoading: any =
                          filteredProvinceList.length === 1 &&
                          !option.value &&
                          !option.status;
                        return (
                          <MenuItem
                            data-cy={`region-option-${index + 1}`}
                            key={index}
                            value={isTrue ? option.name : ""}
                            disabled={isLoading}
                          >
                            {option.name}
                          </MenuItem>
                        );
                      })}
                  </Select>
                  <span className={inputStatus.isProvinceValid.spanClass}>
                    {inputStatus.isProvinceValid.text}
                  </span>
                </div>
              </Grid>
            </Grid>

            <Grid className="vni-flex vni-w-5/6">
              <Grid className="vni-w-1/3">
                <div
                  className="vni-mb-5 vni-pr-8"
                  ref={errorKey === "isCityValid" ? inputRef : null}
                >
                  <label htmlFor="">
                    City/Municipality{" "}
                    <span className="vni-text-red-500">*</span>
                  </label>
                  <Select
                    name="isCityValid"
                    value={business_address ? business_address.city : ""}
                    className={
                      "CustomSelect vni-w-full selectInput " +
                      inputStatus.isCityValid.inputClass
                    }
                    id="business_address_city"
                    data-cy="business_address_city"
                    onChange={handleChangeAddress("city")}
                    // displayEmpty={true}
                  >
                    {filteredCityList.map((option: any, index: any) => {
                      let isTrue: any =
                        (business_address && business_address.province) ||
                        (business_address && business_address.region);
                      let isLoading: any =
                        filteredCityList.length === 1 &&
                        !option.value &&
                        !option.status;
                      return (
                        option.name && (
                          <MenuItem
                            data-cy={`city-option-${index + 1}`}
                            key={index}
                            value={isTrue ? option.name : ""}
                            disabled={isLoading}
                          >
                            {option.name}
                          </MenuItem>
                        )
                      );
                    })}
                  </Select>
                  <span className={inputStatus.isCityValid.spanClass}>
                    {inputStatus.isCityValid.text}
                  </span>
                </div>
              </Grid>

              <Grid className="vni-w-1/3">
                <div
                  className="vni-mb-5 vni-pr-8"
                  ref={errorKey === "isBrgyValid" ? inputRef : null}
                >
                  <label htmlFor="">
                    Barangay <span className="vni-text-red-500">*</span>
                  </label>

                  <Select
                    name="isBrgyValid"
                    value={business_address ? business_address.brgy : ""}
                    className={
                      "CustomSelect vni-w-full " +
                      inputStatus.isBrgyValid.inputClass
                    }
                    id="business_address_brgy"
                    data-cy="business_address_brgy"
                    onChange={handleChangeAddress("brgy")}
                  >
                    {filteredBrgyList.map((option: any, index: any) => {
                      let isTrue: any =
                        business_address && business_address.city;
                      let isLoading: any =
                        filteredBrgyList.length === 1 &&
                        !option.value &&
                        !option.status;
                      return (
                        option.name && (
                          <MenuItem
                            data-cy={`city-option-${index + 1}`}
                            key={index}
                            value={isTrue ? option.name : ""}
                            disabled={isLoading}
                          >
                            {option.name}
                          </MenuItem>
                        )
                      );
                    })}
                  </Select>
                  <span className={inputStatus.isBrgyValid.spanClass}>
                    {inputStatus.isBrgyValid.text}
                  </span>
                </div>
              </Grid>

              <Grid className="vni-w-1/3">
                <div
                  className="vni-mb-5 vni-pr-8"
                  ref={errorKey === "isZipCodeValid" ? inputRef : null}
                >
                  <label htmlFor="">
                    ZIP Code <span className="vni-text-red-500">*</span>
                  </label>
                  <input
                    name="isZipCodeValid"
                    type="text"
                    id="business_address_zip_code"
                    data-cy="business_address_zip_code"
                    minLength={1}
                    maxLength={7}
                    onChange={handleChangeAddress("zip_code")}
                    className={
                      "CustomInput vni-w-full " +
                      inputStatus.isZipCodeValid.inputClass
                    }
                    value={
                      business_address ? business_address.zip_code ?? "" : ""
                    }
                  />
                  <span className={inputStatus.isZipCodeValid.spanClass}>
                    {inputStatus.isZipCodeValid.text}
                  </span>
                </div>
              </Grid>
            </Grid>
          </Grid>

          <div className="vni-flex vni-flex-col">
            <div className="vni-flex">
              <div className="vni-w-full">
                <span className="vni-flex">
                  <span
                    className="vni-w-1/5 vni-pr-4"
                    ref={
                      errorKey === "isIndustryClassIdValid" ? inputRef : null
                    }
                  >
                    <label htmlFor="">
                      Industry Classification{" "}
                      <span className="vni-text-red-500">*</span>
                    </label>
                    <Select
                      value={industry_class_id ?? ""}
                      className={
                        "CustomSelect selectInput " +
                        inputStatus.isIndustryClassIdValid.inputClass
                      }
                      name="industry_class_id"
                      data-cy="industry_classification"
                      onChange={(e) =>
                        handleInputChange(e, "select", "isIndustryClassIdValid")
                      }
                    >
                      {industryListData &&
                        industryListData.map((option: any, index: number) => (
                          <MenuItem
                            data-cy={`industry-option-${index + 1}`}
                            key={index}
                            value={option.name.toUpperCase()}
                          >
                            {option.name.toUpperCase()}
                          </MenuItem>
                        ))}
                    </Select>
                    <span
                      className={inputStatus.isIndustryClassIdValid.spanClass}
                    >
                      {inputStatus.isIndustryClassIdValid.text}
                    </span>
                  </span>

                  {/******* AUTHORIZED PERSON ADDITIONAL SECTION ******/}

                  <span
                    className="vni-flex vni-w-1/2 vni-flex-direction-col"
                    style={{ width: "80%" }}
                  >
                    <span className="vni-flex">
                      <span
                        className=" vni-pl-4 vni-pr-8"
                        style={{ width: "38%" }}
                      >
                        <label htmlFor="">
                          Authorized Contact Person{" "}
                          <span className="vni-text-red-500">*</span>
                        </label>
                      </span>
                      <span className="vni-w-1/2" style={{ width: "62%" }}>
                        <label htmlFor="">
                          Designation / Position of the Contact Person{" "}
                          <span className="vni-text-red-500">*</span>
                        </label>
                      </span>
                    </span>

                    {authorized_contact_persons.map((x: any, i: number) => {
                      let authInputStats =
                        inputStatus.isAuthorizedContactPersons[i] ??
                        emptyNameTypeValue;
                      return (
                        <>
                          <span
                            className="vni-flex vni-pos-relative"
                            ref={
                              errorKey === `isAuthorizedContactPersons-${i}`
                                ? inputRef
                                : null
                            }
                          >
                            <span
                              className=" vni-pl-4 vni-pr-8 vni-mb-2"
                              style={{ width: "38%" }}
                            >
                              <input
                                type="text"
                                minLength={1}
                                maxLength={50}
                                id={`authorized_contact_person${i + 1}`}
                                data-cy={`auth-name-${i + 1}`}
                                className={
                                  "CustomInput vni-w-full " +
                                  authInputStats.name.inputClass
                                }
                                name="name"
                                onChange={(e) => handleAuthorizeChange(e, i)}
                                value={x.name ?? ""}
                              />
                              <span
                                style={{ paddingBottom: 0 }}
                                className={authInputStats.name.spanClass}
                              >
                                {authInputStats.name.text}
                              </span>
                            </span>
                            <span
                              className="vni-w-1/2 vni-designation vni-mb-2"
                              style={{ width: "62%" }}
                            >
                              <input
                                type="text"
                                minLength={1}
                                maxLength={50}
                                id={`designated_contact_person${i + 1}`}
                                data-cy={`auth-designation-${i + 1}`}
                                className={
                                  "CustomInput vni-w-full " +
                                  authInputStats.type.inputClass
                                }
                                name="type"
                                onChange={(e) => handleAuthorizeChange(e, i)}
                                value={x.type ?? ""}
                              />
                              <span
                                style={{ paddingBottom: 0 }}
                                className={authInputStats.type.spanClass}
                              >
                                {authInputStats.type.text}
                              </span>
                              {i > 0 && (
                                <ListItemIcon
                                  data-cy={`remote-authorized-contact${i + 1}`}
                                  className={`vni-remove-btn${i + 1}`}
                                  onClick={() => handleRemoveAuth(i)}
                                >
                                  <CancelIcon />
                                </ListItemIcon>
                              )}
                            </span>
                          </span>
                        </>
                      );
                    })}

                    <span className="vni-flex">
                      <button
                        data-cy="add-authorized-contact"
                        className="vni-text-ocean_green vni-pl-4 vni-add-btn"
                        onClick={handleAddAuth}
                      >
                        <i
                          className="fas fa-plus vni-pr-2"
                          aria-hidden="true"
                        ></i>
                        <span className="text-ocean_green">
                          Add Authorized Contact Person
                        </span>
                      </button>
                    </span>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="vni-block vni-mt-10">
          <h3 className="vni-block vni-text-xl vni-font-bold vni-mb-5">
            Franchise Applicant's Information (Sales Channel)
          </h3>
          <div className="vni-flex vni-mb-5">
            <div
              className="vni-w-1/2 vni-pr-8"
              ref={errorKey === "isApplicantNameValid" ? inputRef : null}
            >
              <label htmlFor="">
                Franchisee Name <span className="vni-text-red-500">*</span>
              </label>
              {(channel_of_request === "" ||
                channel_of_request === undefined) && (
                <input
                  type="text"
                  minLength={1}
                  maxLength={50}
                  data-cy="franchisee_name"
                  className={"CustomInput vni-w-full disabled"}
                  name="applicant_name"
                />
              )}
              {channel_of_request === "Business Development Officer" && group_name === 'BDO' && (
                <input
                  type="text"
                  minLength={1}
                  maxLength={50}
                  data-cy="franchisee_name"
                  className={"CustomInput vni-w-full disabled"}
                  name="applicant_name"
                  value={currentUser}
                />
              )}
              {(channel_of_request === "Broker" ||
                channel_of_request === "New Business Agency" ||
                channel_of_request === "New Business Associate") && (
                <form autoComplete="off">
                  <Autocomplete
                    freeSolo
                    classes={{
                      option: customOption ? optionStyle.option : "",
                    }}
                    className={`CustomAutocomplete ${inputStatus.isApplicantNameValid.inputClass}`}
                    value={applicant_name}
                    inputValue={applicant_name || ''}
                    options={contactListData}
                    onChange={(e, val) => {
                      
                      const data: any = contactList.find(
                        (d: any) => d.name === val
                      );
                      
                      if (data) {
                        handleFranchiseeName(val as string, { user_id: data?._id});
                        handleApplicantAddress(data);
                        handleFranchiseeContactNumber(data.contact_number);
                        handleFranchiseeContactPersonInput(data.contact_person);
                        handleFranchiseeEmail(data.email_address);
                      }
                    }}
                    onInputChange={(e, val) => {

                      if(val.includes('-')){
                        const data: any = contactList.find(
                          (d: any) => d.name === val.split("-")[1].trim()
                        );
                        handleFranchiseeName(val as string, { user_id: data._id});
                      }else{
                        handleFranchiseeName(val as string, { user_id: ''});
                      }
                    }}
                    getOptionLabel={(option: string) => {
                      const opts: any = contactList.find(
                        (d: any) => d.name === option
                      );
                      if (opts) {
                        return `${opts?.code} - ${opts?.name}`;
                      }

                      return option;
                    }}
                    noOptionsText={"Code does not exists"}
                    renderInput={(params) => (
                      <div ref={params.InputProps.ref}>
                        <input
                          style={AutocompleteInputStyle}
                          type="text"
                          {...params.inputProps}
                        />
                      </div>
                    )}
                    onKeyDown={(event) => {
                      if (event.key === "Enter") {
                        event.preventDefault();
                      }
                    }}
                  />
                </form>
              )}
              {(channel_of_request === "Called-In Request for Proposal" ||
                channel_of_request === "Walk-In Request for Proposal" ||
                channel_of_request === "In-House" ||
                (channel_of_request === "Business Development Officer" && group_name !== 'BDO')) && (
                <>
                  <Autocomplete
                    freeSolo
                    classes={{
                      option: customOption ? optionStyle.option : "",
                    }}
                    className={`CustomAutocomplete ${inputStatus.isApplicantNameValid.inputClass}`}
                    value={applicant_name}
                    inputValue={applicant_name || ''}
                    options={contactListData}
                    onChange={(e, val) => {
                      const match = bdoUserListData.find((u:any) => u.full_name === val)
                      handleFranchiseeName(val as string, { user_id: match.user_id });
                    }}
                    onInputChange={(e, val) => {
                      const match = bdoUserListData.find((u:any) => u.full_name === val)
                      if(match){
                        handleFranchiseeName(val, { user_id: match.user_id });
                      }else {
                        handleFranchiseeName(val, { user_id: '' });
                      }
                    }}
                    noOptionsText={"Code does not exists"}
                    renderInput={(params) => (
                      <div ref={params.InputProps.ref}>
                        <input
                          style={AutocompleteInputStyle}
                          type="text"
                          {...params.inputProps}
                        />
                      </div>
                    )}
                    onKeyDown={(event) => {
                      if (event.key === "Enter") {
                        event.preventDefault();
                      }
                    }}
                  />
                </>
              )}
              <span className={inputStatus.isApplicantNameValid.spanClass}>
                {inputStatus.isApplicantNameValid.text}
              </span>
            </div>

            <div
              className="vni-w-1/2"
              ref={
                errorKey === "isApplicantContactPersonValid" ? inputRef : null
              }
            >
              <label htmlFor="">Contact Person <span className="vni-text-red-500">*</span></label>

              <input
                type="text"
                ref={contactPerson}
                minLength={1}
                maxLength={50}
                id="isApplicantContactPersonValid"
                data-cy="contact_person"
                className={"CustomInput vni-w-full " + inputStatus.isApplicantContactPersonValid.inputClass}
                name="applicant_contact_person"
                onChange={handleInputChange}
                value={applicant_contact_person ? applicant_contact_person : ""}
              />
              <span
                className={inputStatus.isApplicantContactPersonValid.spanClass}
              >
                {inputStatus.isApplicantContactPersonValid.text}
              </span>
            </div>
          </div>

          <div className="vni-flex vni-flex-col vni-mb-5">
            <div className="vni-flex">
              <div
                className="vni-w-1/2 vni-pr-8"
                ref={
                  errorKey === "isApplicantBusinessAddress" ? inputRef : null
                }
              >
                <label htmlFor="">
                  Business Address <span className="vni-text-red-500">*</span>
                </label>
                <textarea
                  minLength={1}
                  maxLength={100}
                  id="isApplicantBusinessAddress"
                  data-cy="business_addr"
                  className={
                    "CustomInput vni-w-full " +
                    inputStatus.isApplicantBusinessAddress.inputClass
                  }
                  name="applicant_business_address"
                  onChange={handleInputChange}
                  value={applicant_business_address ? applicant_business_address : ""}
                ></textarea>
                <span
                  className={inputStatus.isApplicantBusinessAddress.spanClass}
                >
                  {inputStatus.isApplicantBusinessAddress.text}
                </span>
              </div>
              <div className="vni-w-1/2">
                <span className="vni-flex">
                  <span
                    className="vni-w-5/12 vni-pr-4"
                    ref={
                      errorKey === "isApplicantContactNumber" ? inputRef : null
                    }
                  >
                    <label htmlFor="">
                      Contact Number <span className="vni-text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      minLength={1}
                      maxLength={11}
                      // onKeyDown={allowNumbersOnKeyDown}
                      // onKeyPress={blockSpecialChar}
                      data-cy="applicant_contact_number"
                      id="isApplicantContactNumber"
                      className={
                        "CustomInput vni-w-full " +
                        inputStatus.isApplicantContactNumber.inputClass
                      }
                      name="applicant_contact_number"
                      onChange={(e: any) =>
                        handleNAInput(e, "isApplicantContactNumber")
                      }
                      value={applicant_contact_number ? applicant_contact_number : ""}
                    />
                    <span
                      className={inputStatus.isApplicantContactNumber.spanClass}
                    >
                      {inputStatus.isApplicantContactNumber.text}
                    </span>
                  </span>
                  <span
                    className="vni-w-7/12 vni-pl-4"
                    ref={
                      errorKey === "isApplicantEmailAddress" ? inputRef : null
                    }
                  >
                    <label htmlFor="">
                      Email Address <span className="vni-text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      minLength={1}
                      maxLength={50}
                      data-cy="applicant_email"
                      id="isApplicantEmailAddress"
                      className={
                        "CustomInput vni-w-full " +
                        inputStatus.isApplicantEmailAddress.inputClass
                      }
                      name="applicant_email_address"
                      onChange={handleInputChange}
                      value={applicant_email_address ? applicant_email_address : ""}
                    />
                    <span
                      className={inputStatus.isApplicantEmailAddress.spanClass}
                    >
                      {inputStatus.isApplicantEmailAddress.text}
                    </span>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="vni-block vni-mt-10">
          <h3 className="vni-block vni-text-xl vni-font-bold vni-mb-5">
            Additional Know Your Customer (KYC) Information
          </h3>
          <div className="xl:vni-pr-8">
            <div className="vni-flex vni-w-1/2 vni-flex vni-justify-between vni-mb-5">
              <div
                className="vni-pr-8"
                ref={errorKey === "isTermsValid" ? inputRef : null}
              >
                <label className="vni-whitespace-no-wrap" htmlFor="">
                  Years/Terms of Existence{" "}
                  <span className="vni-text-red-500">*</span>
                </label>
                {/* <input type="number" data-cy="terms_valid" onKeyPress={blockSpecialChar} onKeyDown={allowNumbersOnKeyDown} min={0} max={999} id="isTermsValid" className={"CustomInput vni-w-full " + inputStatus.isTermsValid.inputClass} name="terms" onChange={handleInputChange} value={terms ?? ""} /> */}
                <input
                  type="text"
                  data-cy="terms_valid"
                  min={0}
                  max={999}
                  id="isTermsValid"
                  className={
                    "CustomInput vni-w-full " +
                    inputStatus.isTermsValid.inputClass
                  }
                  name="terms"
                  onChange={(e: any) => handleNAInput(e, "isTermsValid")}
                  value={terms ?? ""}
                />
                <span className={inputStatus.isTermsValid.spanClass}>
                  {inputStatus.isTermsValid.text}
                </span>
              </div>
              <div
                className="vni-pr-8"
                ref={errorKey === "isNoOfEmployeeValid" ? inputRef : null}
              >
                <label htmlFor="">
                  No. of Employees<span className="vni-text-red-500">*</span>
                </label>
                {/* <input type="number" data-cy="number_of_employees" onKeyPress={blockSpecialChar} onKeyDown={allowNumbersOnKeyDown} min={0} max={99999} id="isNoOfEmployeeValid" className={"CustomInput vni-w-full " + inputStatus.isNoOfEmployeeValid.inputClass} name="no_of_employee" onChange={handleInputChange} value={no_of_employee ?? ""} /> */}
                <input
                  type="text"
                  data-cy="number_of_employees"
                  min={1}
                  max={99999}
                  id="isNoOfEmployeeValid"
                  className={
                    "CustomInput vni-w-full " +
                    inputStatus.isNoOfEmployeeValid.inputClass
                  }
                  name="no_of_employee"
                  onChange={(e: any) => handleNAInput(e, "isNoOfEmployeeValid")}
                  value={no_of_employee ?? ""}
                />
                <span className={inputStatus.isNoOfEmployeeValid.spanClass}>
                  {inputStatus.isNoOfEmployeeValid.text}
                </span>
              </div>
              <div ref={errorKey === "isNoOfDependentsValid" ? inputRef : null}>
                <label htmlFor="">
                  No. of Dependents <span className="vni-text-red-500">*</span>
                </label>
                {/* <input type="number" data-cy="number_of_dependents" onKeyPress={blockSpecialChar} onKeyDown={allowNumbersOnKeyDown} min={0} max={999999} id="isNoOfDependentsValid" className={"CustomInput vni-w-full " + inputStatus.isNoOfDependentsValid.inputClass} name="no_of_dependents" onChange={handleInputChange} value={no_of_dependents ?? ""} /> */}
                <input
                  type="text"
                  data-cy="number_of_dependents"
                  min={0}
                  max={99999}
                  id="isNoOfDependentsValid"
                  className={
                    "CustomInput vni-w-full " +
                    inputStatus.isNoOfDependentsValid.inputClass
                  }
                  name="no_of_dependents"
                  onChange={(e: any) =>
                    handleNAInput(e, "isNoOfDependentsValid")
                  }
                  value={no_of_dependents ?? ""}
                />
                <span className={inputStatus.isNoOfDependentsValid.spanClass}>
                  {inputStatus.isNoOfDependentsValid.text}
                </span>
              </div>
            </div>

            <div className="vni-flex vni-mb-5">
              <div
                className="vni-w-1/2 vni-mr-8"
                ref={errorKey === "isProviderNameValid" ? inputRef : null}
              >
                <label htmlFor="">
                  Name of Current / Previous Provider{" "}
                  <span className="vni-text-red-500">*</span>
                </label>
                <Select
                  id="isProviderNameValid"
                  data-cy="prev_provider"
                  value={provider_name ?? ""}
                  className={
                    "CustomSelect selectInput " +
                    inputStatus.isProviderNameValid.inputClass
                  }
                  name="provider_name"
                  onChange={handleHMOProviderChange}
                >
                  {previousHMOProviderList.map((option, index) => (
                    <MenuItem
                      data-cy={`provider-option-${index + 1}`}
                      key={index}
                      value={option.value}
                    >
                      {option.name}
                    </MenuItem>
                  ))}
                </Select>
                <span className={inputStatus.isProviderNameValid.spanClass}>
                  {inputStatus.isProviderNameValid.text}
                </span>
              </div>
              <div
                className="vni-w-1/4"
                ref={
                  errorKey === "isProviderEfectivityDateFromValid"
                    ? inputRef
                    : null
                }
              >
                <label htmlFor="">Effectivity Date</label>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    format="MM/dd/yyyy"
                    className="CustomInput vni-datepicker create-date-fix"
                    name="provider_effectivity_date_from"
                    value={
                      provider_effectivity_date_from
                        ? new Date(provider_effectivity_date_from)
                        : null
                    }
                    onChange={(e: any) => {
                      handleEffectivityDate(
                        e,
                        "isProviderEfectivityDateFromValid",
                        "provider_effectivity_date_from"
                      );
                    }}
                    slotProps={{
                      textField: {
                        variant: 'outlined',
                        error: !inputStatus.isProviderEfectivityDateFromValid.valid,
                      },
                    }}
                  />
                </LocalizationProvider>
                <span
                  style={{ maxWidth: 270 }}
                  className={
                    inputStatus.isProviderEfectivityDateFromValid.spanClass
                  }
                >
                  {inputStatus.isProviderEfectivityDateFromValid.text}
                </span>
              </div>
              <p className="vni-mx-4 vni-mt-8">–</p>
              <div
                className="vni-w-1/4"
                ref={
                  errorKey === "isProviderEfectivityDateToValid"
                    ? inputRef
                    : null
                }
              >
                <label style={{ opacity: 0 }} htmlFor="">
                  Effectivity Date
                </label>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    format="MM/dd/yyyy"
                    className="CustomInput vni-datepicker create-date-fix"
                    name="provider_effectivity_date_to"
                    disabled={!provider_effectivity_date_from}
                    minDate={
                      provider_effectivity_date_from
                        ? new Date(provider_effectivity_date_from)
                        : undefined
                    }
                    value={
                      provider_effectivity_date_to &&
                      provider_effectivity_date_from &&
                      provider_effectivity_date_from !== "Invalid date"
                        ? new Date(provider_effectivity_date_to)
                        : null
                    }
                    onChange={(e: any) => {
                      handleEffectivityDate(
                        e,
                        "isProviderEfectivityDateToValid",
                        "provider_effectivity_date_to"
                      );
                    }}
                    slotProps={{
                      textField: {
                        variant: 'outlined',
                        error: !inputStatus.isProviderEfectivityDateToValid.valid,
                        id: "isProviderEfectivityDateToValid",
                        inputProps: {
                          'data-cy': "provider_effectivity_date_to"
                        }
                      }
                    }}
                  />
                </LocalizationProvider>
                <span
                  style={{ maxWidth: 270 }}
                  className={
                    inputStatus.isProviderEfectivityDateToValid.spanClass
                  }
                >
                  {inputStatus.isProviderEfectivityDateToValid.text}
                </span>
              </div>
            </div>

            <div className="vni-flex vni-w-2/6 vni-justify-between vni-mb-5">
              <div
                className="vni-pr-8"
                ref={errorKey === "isVoluntaryValid" ? inputRef : null}
              >
                <label htmlFor="" style={{ whiteSpace: "nowrap" }}>
                  Enrollees Voluntary?{" "}
                  <span className="vni-text-red-500">*</span>
                </label>
                <Select
                  data-cy="is_voluntary_enrollees"
                  value={is_voluntary_enrollees ?? ""}
                  className={
                    "CustomSelect selectInput " +
                    inputStatus.isVoluntaryValid.inputClass
                  }
                  name="is_voluntary_enrollees"
                  onChange={(e) =>
                    handleInputChange(e, "select", "isVoluntaryValid")
                  }
                >
                  <MenuItem key={0} data-cy="voluntary-yes" value={"Yes"}>
                    Yes
                  </MenuItem>
                  <MenuItem key={1} data-cy="voluntary-no" value={"No"}>
                    No
                  </MenuItem>
                </Select>
                <span className={inputStatus.isVoluntaryValid.spanClass}>
                  {inputStatus.isVoluntaryValid.text}
                </span>
              </div>
              <div
                ref={errorKey === "isCompanyPaidSharedValid" ? inputRef : null}
              >
                <label htmlFor="" style={{ whiteSpace: "nowrap" }}>
                  Company Paid/Shared<span className="vni-text-red-500">*</span>
                </label>
                {/* <input type="number" data-cy="company_paid" onKeyPress={blockSpecialChar} onKeyDown={allowNumbersOnKeyDown} min={0} max={999999} id="isCompanyPaidSharedValid" className={"CustomInput vni-w-full " + inputStatus.isCompanyPaidSharedValid.inputClass} name="company_paid_shared" onChange={handleInputChange} value={company_paid_shared ?? ""} /> */}
                <input
                  type="text"
                  data-cy="company_paid"
                  min={0}
                  max={999999}
                  id="isCompanyPaidSharedValid"
                  className={
                    "CustomInput vni-w-full " +
                    inputStatus.isCompanyPaidSharedValid.inputClass
                  }
                  name="company_paid_shared"
                  onChange={(e: any) =>
                    handleNAInput(e, "isCompanyPaidSharedValid")
                  }
                  value={company_paid_shared ?? ""}
                />
                <span
                  className={inputStatus.isCompanyPaidSharedValid.spanClass}
                >
                  {inputStatus.isCompanyPaidSharedValid.text}
                </span>
              </div>
            </div>
            <div className="vni-w-3/5">
              {/******* STAKEHOLDER ADDITIONAL SECTION ******/}
              <Grid className="vni-flex">
                <Grid className="vni-w-3/5">
                  <label htmlFor="">
                    Name of Board of Directors / Officers / Principal
                    Stakeholders <span className="vni-text-red-500">*</span>
                  </label>
                </Grid>
                <Grid className="vni-w-2/5 vni-pl-4">
                  <label htmlFor="">
                    Type of ID Presented{" "}
                    <span className="vni-text-red-500">*</span>
                  </label>
                </Grid>
              </Grid>
              {stakeholders.map((x: any, i: number) => {
                let shInputStats =
                  inputStatus.isStakeholdersValid[i] ?? emptyNameTypeValue;
                return (
                  <Grid
                    className="added vni-flex vni-relative vni-mb-2"
                    ref={
                      errorKey === `isStakeholdersValid-${i}` ? inputRef : null
                    }
                  >
                    <Grid className="vni-w-3/5">
                      <input
                        type="text"
                        data-cy={`board_of_directors_name_${i + 1}`}
                        minLength={1}
                        maxLength={100}
                        id={`isStakeholdersValidName${i}`}
                        className={
                          "CustomInput vni-w-full " +
                          shInputStats.name.inputClass
                        }
                        name="name"
                        onChange={(e) => handleSHInputChange(e, i)}
                        value={x.name ?? ""}
                      />
                      <span className={shInputStats.name.spanClass}>
                        {shInputStats.name.text}
                      </span>
                    </Grid>
                    <Grid className="vni-w-2/5 vni-pl-4">
                      <input
                        type="text"
                        data-cy={`board_of_directors_type_${i + 1}`}
                        minLength={1}
                        maxLength={50}
                        id={`isStakeholdersValidType${i}`}
                        className={
                          "CustomInput vni-w-full " +
                          shInputStats.type.inputClass
                        }
                        name="type"
                        onChange={(e) => handleSHInputChange(e, i)}
                        value={x.type ?? ""}
                      />
                      <span className={shInputStats.type.spanClass}>
                        {shInputStats.type.text}
                      </span>
                      {i > 0 && (
                        <ListItemIcon
                          data-cy={`remove-board-${i + 1}`}
                          className={`vni-remove-btn${i + 1}`}
                          onClick={() => handleRemoveSH(i)}
                        >
                          <CancelIcon />
                        </ListItemIcon>
                      )}
                    </Grid>
                  </Grid>
                );
              })}
              <span className="vni-flex">
                <button
                  data-cy="add-board"
                  className="vni-text-ocean_green vni-pb-4 vni-add-btn"
                  onClick={handleAddSH}
                >
                  <i className="fas fa-plus vni-pr-2" aria-hidden="true"></i>
                  <span className="text-ocean_green">
                    Add Board of Directors / Officers / Principal Stakeholders
                  </span>
                </button>
              </span>

              {/******* SIGNATORIES ADDITIONAL SECTION ******/}
              <Grid className="vni-flex">
                <Grid className="vni-w-3/5">
                  <label htmlFor="">
                    Name of Authorized Signatories{" "}
                    <span className="vni-text-red-500">*</span>
                  </label>
                </Grid>
                <Grid className="vni-w-2/5 vni-pl-4">
                  <label htmlFor="">
                    Type of ID Presented{" "}
                    <span className="vni-text-red-500">*</span>
                  </label>
                </Grid>
              </Grid>
              {signatories.map((x: any, i: number) => {
                let sgInputStats =
                  inputStatus.isSignatoriesValid[i] ?? emptyNameTypeValue;
                return (
                  <Grid
                    className="added vni-flex vni-relative vni-mb-2"
                    ref={
                      errorKey === `isSignatoriesValid-${i}` ? inputRef : null
                    }
                  >
                    <Grid className="vni-w-3/5">
                      <input
                        type="text"
                        data-cy={`name_of_signatories_name_${i + 1}`}
                        minLength={1}
                        maxLength={100}
                        id={`isSignatoriesValidName${i}`}
                        className={
                          "CustomInput vni-w-full " +
                          sgInputStats.name.inputClass
                        }
                        name="name"
                        onChange={(e) => handleSignatoriesChange(e, i)}
                        value={x.name ?? ""}
                      />
                      <span className={sgInputStats.name.spanClass}>
                        {sgInputStats.name.text}
                      </span>
                    </Grid>
                    <Grid className="vni-w-2/5 vni-pl-4">
                      <input
                        type="text"
                        data-cy={`name_of_signatories_type_${i + 1}`}
                        minLength={1}
                        maxLength={50}
                        id={`isSignatoriesValidType${i}`}
                        className={
                          "CustomInput vni-w-full " +
                          sgInputStats.type.inputClass
                        }
                        name="type"
                        onChange={(e) => handleSignatoriesChange(e, i)}
                        value={x.type ?? ""}
                      />
                      <span className={sgInputStats.type.spanClass}>
                        {sgInputStats.type.text}
                      </span>
                      {i > 0 && (
                        <ListItemIcon
                          data-cy={`remove-signatories-${i + 1}`}
                          className={`vni-remove-btn${i + 1}`}
                          onClick={() => handleRemoveSignatories(i)}
                        >
                          <CancelIcon />
                        </ListItemIcon>
                      )}
                    </Grid>
                  </Grid>
                );
              })}
              <span className="vni-flex">
                <button
                  data-cy="add-signatories"
                  className="vni-text-ocean_green vni-pb-4 vni-add-btn"
                  onClick={handleAddSignatories}
                >
                  <i className="fas fa-plus vni-pr-2" aria-hidden="true"></i>
                  <span className="text-ocean_green">
                    Add Authorized Signatories
                  </span>
                </button>
              </span>

              <div className="vni-flex">
                <div
                  className="vni-w-1/2"
                  ref={errorKey === "isSourceOfFundsValid" ? inputRef : null}
                >
                  <label htmlFor="">
                    Source of Funds <span className="vni-text-red-500">*</span>
                  </label>
                  <Select
                    id="isSourceOfFundsValid"
                    data-cy="source_of_funds"
                    value={source_of_funds ?? ""}
                    className={
                      "CustomSelect selectInput " +
                      inputStatus.isSourceOfFundsValid.inputClass
                    }
                    name="source_of_funds"
                    onChange={handleSourceFundChange}
                  >
                    {sourcesOfFundsList.map((option, index) => (
                      <MenuItem
                        data-cy={`source-of-fund-option-${index + 1}`}
                        key={index}
                        value={option.value}
                      >
                        {option.name}
                      </MenuItem>
                    ))}
                  </Select>
                  <span className={inputStatus.isSourceOfFundsValid.spanClass}>
                    {inputStatus.isSourceOfFundsValid.text}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Modal
        fullWidth={false}
        maxWidth="md"
        open={openErrorModal}
        // onClose={handleCloseErrorModal}
        //@ts-ignore
				onClose={(event, reason) => {
					if(reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
						handleCloseErrorModal()
					}
				}}
        // hideCloseButton={hideModalCloseButton}
      >
        <Grid>
          <Typography variant="h5" className={classes.modalTitle}>
            {validationContent.title}
          </Typography>
          <Typography
            className={classes.modalMessage}
            style={{ maxWidth: 500 }}
            paragraph={true}
          >
            {validationContent.p}
          </Typography>
          <Grid className="btn-group" style={{ textAlign: "center" }}>
            <Button
              data-cy="Okay"
              className={classes.rightButton}
              onClick={handleCloseErrorModal}
            >
              Okay
            </Button>
          </Grid>
        </Grid>
      </Modal>
    </>
  );
};

export default CreateEditFranchiseRequest;
