import React from 'react'
import { IFileItem } from '../../shared/models/Franchise';
import { useDropzone } from 'react-dropzone';

const UploadFileItem: React.FC<IFileItem> = (props) => {
    const {getRootProps} = useDropzone({noClick:true,disabled:true});
    return (
      <div {...getRootProps({className: props.objectStatus})}>
        <div>
            {
              props.handleClick ? 
                <input data-cy={"select-"+ props.id} type="checkbox" className="custom-checkbox" key={props.id} id={props.id} onClick={props.handleClick}/>
                :
                null
            }
            <label htmlFor={props.id}></label>
            <div className="file-info">
                <i className={props.icon}></i>
                <h5>{props.name}</h5>
            </div>
            <div className={props.loading}>
                <div className={props.progress} style={{width: props.percentage}}></div>
            </div>
        </div>
        <p className={props.errorClass}>{props.errorMessage}</p>
      </div>
    );
}


export default UploadFileItem;