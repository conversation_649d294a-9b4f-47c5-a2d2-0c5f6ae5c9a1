import React, { useEffect } from 'react'
import { Franchise } from '../../shared/models/Franchise';
import { List, ListItem, Typography } from '@mui/material';
import { NavLink } from 'react-router-dom';
// import { convertAddressRegionName } from '../../utils/StringHelper';
import { getFranchisingRole } from '../../utils/DataHelper';
import moment from 'moment';
// import { decryptCredentials } from '../../utils/LoginService';
interface Props {
    franchise: Franchise,
    handleDownload: any
}
const SubmitFranchiseRequest: React.FC<Props> = (props) => {

    useEffect(() => {
        window.scrollTo(0, 0);
    }, [])

    const {
        franchise,
        handleDownload
    } = props
    const { 
        business_address,
        old_account_name,
        corporate_name,
        brand_name,
        channel_of_request,
        is_refranchise,
        client_id,
        company_affiliates,
        tax_number,
        company_number,
        industry_class_id,
        authorized_contact_persons,
        applicant_name,
        applicant_contact_person,
        applicant_business_address,
        applicant_contact_number,
        applicant_email_address,
        terms,
        provider_name,
        provider_effectivity_date_from,
        provider_effectivity_date_to,
        no_of_employee,
        is_voluntary_enrollees,
        stakeholders,
        source_of_funds,
        signatories,
        company_paid_shared,
        no_of_dependents,
        supporting_docs_obj,
        date_time_received,
        appointment_letter
    } = franchise

    // const [decryptedApplicantName, setDecryptedApplicantName] = useState(applicant_name);

    // useEffect(() => {
    //     if (applicant_name.includes("|")) {
    //         const name = applicant_name.split(" ");
    //         let decrypted = [];
    //         console.log('decrypted ba', name);
    //         name.forEach((val) => {
    //             const newValue = decryptCredentials(val);
    //             console.log('decrypted ba foreach', newValue);
    //             decrypted.push(newValue);
    //         })
    //         console.log('decrypted ba', decrypted);
    //         const decryptedName = decrypted.join(" ");
    //         setDecryptedApplicantName(decryptedName);
    //     }
    // },[])

    return (
        <div className="main vni-py-8 vni-px-12" id="submit_request">
            {/* Review Franchise Request */}
            <div className="vni-flex vni-justify-between vni-items-start">
                <span>
                    <h3 className="vni-block vni-text-lg vni-font-bold">Review Franchise Request</h3>
                    <p>Please review the request before submitting</p>
                </span>
                <span className="supporting-documents-list">
                    <h5>Supporting Documents</h5>
                    <List>
                        {
                            supporting_docs_obj && supporting_docs_obj.map((value, i) => (
                                value.main_role
                                && (value.main_role === "FRF_ENCODER" || getFranchisingRole(value.roles) === "FRF_ENCODER")
                                && <ListItem key={i}>
                                    <Typography>
                                        <NavLink onClick={(e) => handleDownload(e, value._id, value.file_name)} to={value.path}>{value.file_name}</NavLink>
                                    </Typography>
                                </ListItem>
                            ))
                        }
                    </List>
                    { appointment_letter?.length > 0  ? (
                        <>
                            <h5>Appointment Letter</h5>
                            <List>
                                {
            
                                    appointment_letter?.map((file: any, i: number) => {
                                        return (
                                        <ListItem key={i}>
                                            <Typography>
                                                <NavLink onClick={(e) => handleDownload(e, file._id, file.file_name)} to={file.path}>{file.file_name}</NavLink>
                                            </Typography>
                                        </ListItem>
    
                                        )
                                    }) 
                                }
                            </List>
                        </>
                        
                    ): null}


                    
                </span>
            </div>

            <div className="vni-block vni-mt-10">
                <h3 className="vni-text-xl vni-font-bold vni-mb-5">Client Information</h3>
                <div className="preview-request-container">
                    <div className="info-row">
                        <span className="vni-w-1/3">
                            <small>Channel of Request</small>
                            <h4>{channel_of_request}</h4>
                        </span>
                        <span className="vni-w-1/3">
                            <small>Date and Time Received</small>
                            <h4>{moment(date_time_received).format("MM/DD/YYYY hh:mm A")}</h4>
                        </span>
                    </div>
                    <div className="info-row">
                        <span className="vni-w-1/3">
                            <small>Returning Franchise</small>
                            <h4>{is_refranchise === "Yes" ? "Yes" : "No"}</h4>
                        </span>
                        <span className="vni-w-1/3">
                            <small>Client ID</small>
                            <h4>{is_refranchise === "Yes" ? client_id : "--"}</h4>
                        </span>
                        <span className="vni-w-1/3">
                            <small>Old Account Name</small>
                            <h4>{old_account_name}</h4>
                        </span>
                    </div>
                    <div className="info-row">
                        <span className="vni-w-1/3">
                            <small>Registered Corporate Name</small>
                            <h4>{corporate_name}</h4>
                        </span>
                        <span className="vni-w-1/3">
                            <small>Trade/Brand Name</small>
                            <h4>{brand_name}</h4>
                        </span>
                        <span className="vni-w-1/3"> 
                            <small>Registered Business Address</small>
                            <h4>{business_address?`${business_address.floor.toLowerCase()!=="n/a" ?business_address.floor+",":""} ${business_address.unit.toLowerCase()!=="n/a" ?business_address.unit+",":""} ${business_address.bldg_no.toLowerCase()!=="n/a" ? business_address.bldg_no + ",":""} ${business_address.bldg_name.toLowerCase()!=="n/a" ?business_address.bldg_name+",":""} ${business_address.street.toLowerCase()!=="n/a" ?business_address.street+",":""} ${business_address.brgy.toLowerCase()!=="n/a"?business_address.brgy+",":""} ${business_address.city}, ${business_address.province ? business_address.province+",":""} ${business_address.region}${business_address.zip_code.toLowerCase()!=="n/a" ? ", " + business_address.zip_code:""}`:""}</h4>
                        </span>
                    </div>
                    <div className="info-row">
                        <span className="vni-w-1/3">
                            <small>Specify Mother Company</small>
                            <h4>{company_affiliates}</h4>
                        </span>
                        <span className="vni-w-1/3">
                            <small>TIN Number</small>
                            <h4>{tax_number}</h4>
                        </span>
                        <span className="vni-w-1/3">
                            <small>Company Contact Number/s</small>
                            <h4>{company_number}</h4>
                        </span>
                    </div>
                    <div className="info-row">
                        <span className="vni-w-1/3">
                            <small>Industry Classification</small>
                            <h4>{industry_class_id}</h4>
                        </span>
                        <span className="vni-w-1/3">
                            <small>Authorized Contact Person</small>
                            {
                                authorized_contact_persons && authorized_contact_persons.map((value, i) => (
                                    <h4 style={{overflow: 'hidden', textOverflow: 'ellipsis'}} key={i}>{value.name}</h4>
                                ))
                            }
                        </span>
                        <span className="vni-w-1/3">
                            <small>Designation/Position of Contact Person</small>
                            {
                                authorized_contact_persons && authorized_contact_persons.map((value, i) => (
                                    <h4 style={{overflow: 'hidden', textOverflow: 'ellipsis'}} key={i}>{value.type}</h4>
                                ))
                            }
                        </span>
                    </div>
                </div>
            </div>

            <div className="vni-block vni-mt-5">
                <h3 className="vni-text-xl vni-font-bold vni-mb-5">Franchise Applicant's Information (Sales Channel)</h3>
                <div className="preview-request-container">
                    <div className="info-row">
                        <span className="vni-w-1/3">
                            <small>Franchisee Name</small>
                            {/* <h4>{decryptedApplicantName}</h4> */}
                            <h4>{applicant_name}</h4>
                        </span>
                        <span className="vni-w-1/3">
                            <small>Contact Person (If Broker)</small>
                            <h4>{applicant_contact_person}</h4>
                        </span>
                    </div>
                    <div className="info-row">
                        <span className="vni-w-1/3">
                            <small>Business Address</small>
                            <h4>{applicant_business_address}</h4>
                        </span>
                        <span className="vni-w-1/3">
                            <small>Contact Number</small>
                            <h4>{applicant_contact_number}</h4>
                        </span>
                        <span className="vni-w-1/3">
                            <small>Email Address</small>
                            <h4>{applicant_email_address}</h4>
                        </span>
                    </div>
                </div>
            </div>

            <div className="vni-block vni-mt-10">
                <h3 className="vni-text-xl vni-font-bold vni-mb-5">Additional Know Your Customer (KYC) Information</h3>
                <div className="preview-request-container">
                    <div className="info-row">
                        <span className="vni-w-1/4">
                            <small>Established (Years)</small>
                            <h4>{terms}</h4>
                        </span>
                        <span className="vni-w-1/4">
                            <small>Current/Previous Provider</small>
                            <h4>{provider_name}</h4>
                        </span>
                        <span className="vni-w-1/4">
                            <small>Effectivity Date</small>
                            <h4>{provider_effectivity_date_from && provider_effectivity_date_to ? `${moment(provider_effectivity_date_from).format("MM/DD/YYYY")} - ${moment(provider_effectivity_date_to).format("MM/DD/YYYY")}`: "-"}</h4>
                        </span>
                        <span className="vni-w-1/4">
                            <small>Total Number of Employees</small>
                            <h4>{no_of_employee}</h4>
                        </span>
                    </div>
                    <div className="info-row">
                        <span className="vni-w-1/4">
                            <small>Name of Board of Directors</small>
                            {
                                stakeholders && stakeholders.map((value, i) => (
                                    <h4 style={{overflow: 'hidden', textOverflow: 'ellipsis'}} key={i}>{value.name}</h4>
                                ))
                            }
                        </span>
                        <span className="vni-w-1/4">
                            <small>Type of ID Presented</small>
                            {
                                stakeholders && stakeholders.map((value, i) => (
                                    <h4 key={i}>{value.type}</h4>
                                ))
                            }
                        </span>
                        <span className="vni-w-1/4">
                            <small>Name of Authorized Signatories</small>
                            {
                                signatories && signatories.map((value, i) => (
                                    <h4 style={{overflow: 'hidden', textOverflow: 'ellipsis'}} key={i}>{value.name}</h4>
                                ))
                            }
                        </span>
                        <span className="vni-w-1/4">
                            <small>Type of ID Presented</small>
                            {
                                signatories && signatories.map((value, i) => (
                                    <h4 key={i}>{value.type}</h4>
                                ))
                            }
                        </span>
                    </div>
                    <div className="info-row">
                        <span className="vni-w-1/4">
                            <small>Enrolees Voluntary?</small>
                            <h4>{is_voluntary_enrollees}</h4>
                        </span>
                        <span className="vni-w-1/4">
                            <small>Company Paid/Shared</small>
                            <h4>{company_paid_shared}</h4>
                        </span>
                        <span className="vni-w-1/4">
                            <small>Number of Dependents</small>
                            <h4>{no_of_dependents}</h4>
                        </span>
                        <span className="vni-w-1/4">
                            <small>Source of Funds</small>
                            <h4>{source_of_funds}</h4>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default SubmitFranchiseRequest;