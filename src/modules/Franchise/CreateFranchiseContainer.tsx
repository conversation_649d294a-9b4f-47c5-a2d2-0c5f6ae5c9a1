import React, { useState, useEffect, useRef } from "react";
import { RootState } from "../../shared/reducers/rootReducer";
import { useSelector, useDispatch } from "react-redux";
import CreateFranchiseFooterButton from "./CreateFranchiseFooterButton";
import CreateEditFranchiseRequest from "./CreateEditFranchiseRequest";
import UploadFranchiseDocuments from "./UploadFranchiseDocuments";
import SubmitFranchiseRequest from "./SubmitFranchiseRequest";
import { Dialog, DialogTitle, DialogContent, DialogActions } from "@mui/material";
import { logout } from "../../shared/reducers/LoginSlice";
import {
  createProgress,
  setFranchiseData,
  createBackProgress,
  saveFranchise,
  addFranchise,
  editFranchise,
  resetState,
  goBackAndCreateNew,
  clearValidationModalContent,
  updateFranchiseRequest,
  setUserInputValues,
  downloadFranchiseFileUpload,
  validateInput,
  setIsCreate,
  patchFranchiseRequest,
  fetchBDOUsers,
  uploadAppointmentLetter,
  setCurrentRecordId,
  validateInputTINId,
} from "../../shared/reducers/FranchiseSlice";
import {
  emptyInputValidStatus,
  InputValidStatus,
  // IErrorAttributes,
  IErrorNameTypeObj,
  CompanyAffiliates,
  // franchise,
} from "../../shared/models/Franchise";
import { useHistory, useLocation } from "react-router-dom";
import { validEmailRegex } from "../../utils/StringHelper";
import { Modal } from "../../shared/components/Modal";
import { Grid, Typography, Button } from "@mui/material";
import { PreviewStyles } from "../../shared/components/ViewPDFReport/style";
import moment from "moment";
import { apiURL } from "../../utils/Environment";

const scrollToRef = (ref: any) => {
  if (ref.current.offsetTop) {
    window.scrollTo(0, ref.current.offsetTop);
  }
};

const CreateFranchiseContainer = (props: any) => {
  const classes = PreviewStyles();
  const myRef = useRef(null);
  const { match } = props;
  let { id } = match.params;
  const history = useHistory();
  const dispatch = useDispatch();
  let location = useLocation();
  const sessionExpired = useSelector((state:RootState) => state.login.auth.sessionExpired)
  const [viewExpiredModal, setViewExpiredModal] = useState(false);
  const token = useSelector((state: RootState) => state.login.token);

  const tab = useSelector((state: RootState) => state.franchise.tab);
  const newFranchise = useSelector((state: RootState) => state.franchise.new);
  const franchiseData = useSelector((state: RootState) => state.franchise);

  const validationContent = useSelector(
    (state: RootState) => state.franchise.validationModalContent
  );
  const validationInputContent = useSelector(
    (state: RootState) => state.franchise.validationInputContent
  );
  const { canAddFR, canUploadDocs } = useSelector(
    (state: RootState) => state.login.userPermissions
  );
  const group_name = useSelector((state: RootState) => state.login.group_name);
  // const isValidating = useSelector((state: RootState) => state.franchise.isValidating)
  const [inputStatus, setInputStatus] = useState(emptyInputValidStatus);
  const isEditLoading = useSelector(
    (state: RootState) => state.franchise.isEditLoading
  );
  const isEdit = useSelector((state: RootState) => state.franchise.isEdit);
  const isCreate = useSelector((state: RootState) => state.franchise.isCreate);
  const [values, setValues] = useState(newFranchise);
  const formStateId = useSelector((state: RootState) => (state.franchise.currentRecordId || (isEdit ? state.franchise.new._id : null)));

  const firstname = useSelector((state: RootState) => state.login.firstname);
  // const [decryptedFirstName, setDecryptedFirstName] = React.useState("");
  const middlename = useSelector((state: RootState) => state.login.middlename);
  // const [decryptedMiddleName, setDecryptedMiddleName] = React.useState("");
  const lastname = useSelector((state: RootState) => state.login.lastname);
  // const [decryptedLastName, setDecrptedLastName] = React.useState("");
  const suffix = useSelector((state: RootState) => state.login.suffix);

  // const [currentUser, setCurrentUser] = React.useState("");

  // const bdoUserList = useSelector(
  //   (state: RootState) => state.franchise.bdoUserListData
  // );

  const uploadedAppointmentLetter = useSelector(
    (state: RootState) => state.franchise.uploadedAppoinmentLetter
  );

  useEffect(() => {
    // add appointment letter details to franchise object
    if (Object.keys(uploadedAppointmentLetter).length > 0) {
      setValues({ ...values, appointment_letter: [uploadedAppointmentLetter] });
    } else {
      setValues({ ...values, appointment_letter: [] });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [uploadedAppointmentLetter]);

  let currentUser = "";
  if (suffix) {
    currentUser = `${firstname} ${middlename} ${lastname} ${suffix}`;
  } else {
    currentUser = `${firstname} ${middlename} ${lastname}`;
  }

  // useEffect(() => {
  //   let decryptedFName = "";
  //   let decryptedMName = "";
  //   let decryptedLName = "";

  //   if (firstname) {
  //     decryptedFName = decryptCredentials(firstname);
  //     setDecryptedFirstName(decryptedFName);
  //   }

  //   if (middlename) {
  //     decryptedMName = decryptCredentials(middlename);
  //     setDecryptedMiddleName(decryptedMName);
  //   }

  //   if (lastname) {
  //     decryptedLName = decryptCredentials(lastname);
  //     setDecrptedLastName(decryptedLName);
  //   }

  //   let fullName;
  //   if (suffix) {
  //     fullName = `${decryptedFName} ${decryptedMName} ${decryptedLName} ${suffix}`;
  //     setCurrentUser(fullName);
  //   } else {
  //     fullName = `${decryptedFName} ${decryptedMName} ${decryptedLName}`;
  //     setCurrentUser(fullName);
  //   }

  //   console.log('decrypted', fullName);

  // }, [firstname, middlename, lastname]);

  const [openModal, setOpenModal] = React.useState<any>(false);
  const [modalContent, setModalContent] = React.useState<any>({
    title: "",
    description: "",
    button: <></>,
  });
  const { title, description, remarks, button } = modalContent;
  const isRequesting = useSelector(
    (state: RootState) => state.franchise.isRequesting
  );


  const [isValidated, setIsValidated] = React.useState<any>(false);
  const [buttonClick, setButtonClick] = React.useState<any>(false);
  const [buttonAction, setButtonAction] = React.useState<any>(false);
  const [fName, setFname] = React.useState("");
  const [salesChannel, setSalesChannel] = useState("");
  const [isBDOUser, setIsBDO] = React.useState<any>(false);

  const initialNameTypeValue = { name: "", type: "" };
  const validObject = {
    valid: true,
    inputClass: "",
    spanClass: "",
    text: "",
  };
  const invalidEmptyObject = {
    valid: false,
    inputClass: "",
    spanClass: "",
    text: "",
  };
  const invalidObject = {
    valid: false,
    inputClass: "error",
    spanClass: "vni-text-red-500 vni-block vni-py-1",
    text: "This field is required.",
  };

  useEffect(() => {
    dispatch(fetchBDOUsers("encoder"));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    dispatch(clearValidationModalContent());
    window.scrollTo(0, 0);
    if (location.pathname === "/franchising/franchise/create") {
      dispatch(setIsCreate(true));
      window.scrollTo(0, 0);
    } else {
      dispatch(setIsCreate(false));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location]);

  useEffect(() => {
    if (isCreate) {
      window.scrollTo(0, 0);
    }
  }, [isCreate]);

  /**
   * This will handle input validation from the api response.
   * <AUTHOR>
   */
  useEffect(() => {
    const { label, valid, id } = validationInputContent;
    let validation = {
      valid,
      inputClass: valid ? "" : "error",
      spanClass: valid ? "" : "vni-text-red-500 vni-block vni-py-1",
      text: label,
    };
    if (id) {
      setInputStatus({ ...inputStatus, "isTaxNumberValid": validation})
    } else {
      setInputStatus({ ...inputStatus, "isClientIdValid": validation });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [validationInputContent]);

  /**
   * This will handle success/error messages from the api response of save/submit/update.
   * <AUTHOR>
   */
  useEffect(() => {
    const { p, title } = validationContent;
    if (p && title) {
      setModalContent({
        title,
        description: p,
        button: (
          <>
            <Button
              data-cy="back-to-dashboard"
              className={classes.leftButtonOutline}
              onClick={(e) => handleConfirmButtonClick(e, "back")}
            >
              Back to Dashboard
            </Button>
            {canAddFR ? (
              <Button
                data-cy="create-new"
                className={classes.rightButton}
                onClick={(e) => handleConfirmButtonClick(e, "new")}
              >
                Create New
              </Button>
            ) : null}
          </>
        ),
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [validationContent]);


  useEffect(() => {
    if (!isEditLoading && !isEdit && id) {
      dispatch(editFranchise(id));
      window.scrollTo(0, 0);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location, id, isEdit, isEditLoading]);

  useEffect(() => {
    if (
      (salesChannel === "In-House" ||
        salesChannel === "Walk-In Request for Proposal" ||
        salesChannel === "Called-In Request for Proposal") &&
      fName !== ""
    ) {
      if (currentUser.toUpperCase() === fName.toUpperCase()) {
        setIsBDO(true);
      } else {
        setIsBDO(false);
      }

      if (isBDOUser === true) {
        setValues({
          ...values,
          "channel_of_request": "Business Development Officer",
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [salesChannel, isBDOUser, fName, currentUser]);

  const setDefaultBdoSalesChannel = (grp_name: string) => {
    if (grp_name === 'BDO') {
      let isApplicantNameValid = {
        valid: true,
        inputClass: "",
        spanClass: "",
        text: "",
      };
      let inputClass = "";
      let spanClass = "";
      let text = "";
      let valid = true;
      setSalesChannel("Business Development Officer");

      setInputStatus({
        ...inputStatus,
        "isChannelOfRequestValid": { valid, inputClass, spanClass, text },
        "isApplicantNameValid": isApplicantNameValid,
      });

      return {
        "channel_of_request": "Business Development Officer",
        "applicant_name": currentUser,
        "applicant_id": ""
      }
    }
    return {}
  }

  useEffect(() => {
    setValues({ ...newFranchise, ...setDefaultBdoSalesChannel(group_name) });
    if (isEdit) {
      const entries = Object.entries(inputStatus);
      let combinedInputStatus = {} as InputValidStatus;
      for (let index = 0; index < entries.length; index++) {
        const key = entries[index][0];
        const objIError = entries[index][1];
        if (objIError.constructor !== Array) {
          Object.assign(combinedInputStatus, { [key]: validObject });
        } else {
          let validArray = [] as any[];
          if (key === "isAuthorizedContactPersons") {
            validArray = newFranchise.authorized_contact_persons ?? [
              initialNameTypeValue,
            ];
          } else if (key === "isStakeholdersValid") {
            validArray = newFranchise.stakeholders ?? [initialNameTypeValue];
          } else if (key === "isSignatoriesValid") {
            validArray = newFranchise.signatories ?? [initialNameTypeValue];
          }
          let validArrayObj = validArray.map(() => {
            return {
              name: validObject,
              type: validObject,
            };
          });
          Object.assign(combinedInputStatus, { [key]: validArrayObj });
        }
      }
      const editCount = Object.entries(combinedInputStatus);
      if (editCount) {
        setInputStatus({ ...inputStatus, ...combinedInputStatus });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [newFranchise]);

  useEffect(() => {
    const validateAllInput = () => {
      console.log(inputStatus);
      const entries = Object.entries(inputStatus);
      let combinedInputStatus = {} as InputValidStatus;
      for (let index = 0; index < entries.length; index++) {
        const key = entries[index][0];
        const objIError = entries[index][1];
        if (objIError.constructor !== Array) {
          // console.log("check inputstatus: ", inputStatus);
          if (!objIError.valid) {
            if (objIError.text.length === 0) {
              Object.assign(combinedInputStatus, { [key]: invalidObject });
            } else {
              Object.assign(combinedInputStatus, { [key]: objIError });
            }
          }
        } else {
          let nameTypeArray = objIError.map(
            ({ name, type }: IErrorNameTypeObj) => {
              let isInValidName = !name.valid && name.text === "";
              let isInValidType = !type.valid && type.text === "";
              return {
                name: isInValidName ? invalidObject : { ...name },
                type: isInValidType ? invalidObject : { ...type },
              };
            }
          );
          let isSomeInValid = nameTypeArray.some(
            ({ name, type }: IErrorNameTypeObj) => {
              return !name.valid || !type.valid;
            }
          );
          if (isSomeInValid) {
            Object.assign(combinedInputStatus, { [key]: nameTypeArray });
          }
        }
      }
      const issueCount = Object.entries(combinedInputStatus);
      console.log("REMAINING INVALID INPUT", combinedInputStatus);
      setButtonClick(false);
      console.log("REMAINING INVALID COUNT", issueCount.length);
      if (issueCount.length) {
        setInputStatus({ ...inputStatus, ...combinedInputStatus });
        return false;
      }
      return true;
    };
    if (buttonClick) {
      setIsValidated(validateAllInput());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [buttonClick]);

  useEffect(() => {
    switch (buttonAction) {
      case "save":
        if (isValidated) {
          dispatch(setFranchiseData(values));
          setModalContent({
            title: "Save Request?",
            description:
              "Are you sure you want to save your Franchise Request? You may still Edit/Submit it later.",
            button: (
              <>
                <Button
                  data-cy="no-btn"
                  className={classes.leftButtonOutline}
                  onClick={handleClose}
                >
                  No
                </Button>
                <Button
                  data-cy="yes-btn"
                  className={classes.rightButton}
                  onClick={(e) => handleConfirmButtonClick(e, "save")}
                >
                  Yes
                </Button>
              </>
            ),
          });
          setOpenModal(true);
        } else {
          if (myRef && myRef.current) {
            scrollToRef(myRef);
          }
        }
        break;
      case "next":
        if (isValidated) {
          // console.log('ANONA VALUES next', values);
          dispatch(createProgress());
          dispatch(setFranchiseData(values));
          setIsValidated(false);
          setButtonAction("");
        } else {
          if (myRef && myRef.current) {
            scrollToRef(myRef);
          }
        }
        break;
      default:
        break;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isValidated, buttonClick]);

  const handleClose = () => {
    setOpenModal(false);
    setIsValidated(false);
    setButtonAction("");
  };

  // const { business_address } = values;

  const handleInputBusinessAddress = (e: any, obj: {}) => {
    //@ts-ignore
    dispatch(setUserInputValues(true));

    const pattern = new RegExp(/\D/);
    let { name, value } = e.target;
    let inputBusinessAddress = {};
    let inputClass = "";
    let spanClass = "";
    let text = "";
    let valid = false;

    if (value) {
      valid = true;
      if (name === "isRegionValid") {
        inputBusinessAddress = {
          [name]: { valid, inputClass, spanClass, text },
          "isProvinceValid": {
            valid: false,
            inputClass: inputStatus.isProvinceValid.inputClass,
            spanClass: inputStatus.isProvinceValid.spanClass,
            text: inputStatus.isProvinceValid.text,
          },
          "isCityValid": {
            valid: false,
            inputClass: inputStatus.isCityValid.inputClass,
            spanClass: inputStatus.isCityValid.spanClass,
            text: inputStatus.isCityValid.text,
          },
          "isBrgyValid": {
            valid: false,
            inputClass: inputStatus.isBrgyValid.inputClass,
            spanClass: inputStatus.isBrgyValid.spanClass,
            text: inputStatus.isBrgyValid.text,
          },
        };
      } else if (name === "isProvinceValid") {
        inputBusinessAddress = {
          [name]: { valid, inputClass, spanClass, text },
          "isCityValid": {
            valid: false,
            inputClass: inputStatus.isCityValid.inputClass,
            spanClass: inputStatus.isCityValid.spanClass,
            text: inputStatus.isCityValid.text,
          },
          "isBrgyValid": {
            valid: false,
            inputClass: inputStatus.isBrgyValid.inputClass,
            spanClass: inputStatus.isBrgyValid.spanClass,
            text: inputStatus.isBrgyValid.text,
          },
        };
      } else if (name === "isCityValid") {
        inputBusinessAddress = {
          [name]: { valid, inputClass, spanClass, text },
          "isBrgyValid": {
            valid: false,
            inputClass: inputStatus.isBrgyValid.inputClass,
            spanClass: inputStatus.isBrgyValid.spanClass,
            text: inputStatus.isBrgyValid.text,
          },
        };
      } else if (name === "isBldgNoValid" || name === "isZipCodeValid") {
        if (
          (value && value.toUpperCase() === "N/A") ||
          (value && !pattern.test(value))
        ) {
          inputBusinessAddress = {
            [name]: { valid, inputClass, spanClass, text },
          };
        } else {
          valid = false;
          inputClass = "error";
          spanClass = "vni-text-red-500 vni-block vni-py-1";
          text = value === "" ? "This field is required." : "Invalid Input";
          inputBusinessAddress = {
            [name]: { valid, inputClass, spanClass, text },
          };
        }
      } else {
        inputBusinessAddress = {
          [name]: { valid, inputClass, spanClass, text },
        };
      }
    } else {
      inputClass = "error";
      spanClass = "vni-text-red-500 vni-block vni-py-1";
      text = "This field is required.";
      inputBusinessAddress = { [name]: { valid, inputClass, spanClass, text } };
    }
    setInputStatus({ ...inputStatus, ...inputBusinessAddress });
    setValues({
      ...values,
      "business_address": { ...values.business_address, ...obj },
    });
  };

  /**
   * This function is for setting values & validation for ff:
   * 1. authorized contact person
   * 2. stakeholders
   * 3. signatories
   * @param arr
   * @param key
   * @param action
   * @param lastIndex : optional for add entry
   *
   */
  const handleInputDynamicArray = (
    arr: any[],
    key: string,
    action: string,
    lastIndex?: number
  ) => {
    //@ts-ignore
    dispatch(setUserInputValues(true));
    let inputACStatus = arr.map((obj, index) => {
      const { name, type } = obj;
      const isLastIndex = lastIndex && lastIndex === index;
      const isValidName = name !== "";
      const isValidType = type !== "";
      return {
        name: isLastIndex
          ? invalidEmptyObject
          : isValidName
          ? validObject
          : invalidObject,
        type: isLastIndex
          ? invalidEmptyObject
          : isValidType
          ? validObject
          : invalidObject,
      };
    });
    setValues({ ...values, [key]: arr });
    setInputStatus({ ...inputStatus, [action]: inputACStatus });
  };

  const handleInputRefranchise = (e: any, id: string) => {
    //@ts-ignore
    dispatch(setUserInputValues(true));
    let { name, value } = e.target;
    let inputClass = "";
    let spanClass = "";
    let text = "";
    let valid = true;

    let isValid = {
      valid: true,
      inputClass: "",
      spanClass: "",
      text: "",
    };

    if (value === "Yes") {
      isValid = {
        valid: false,
        inputClass: "",
        spanClass: "",
        text: "",
      };
      setValues({ ...values, [name]: value, "client_id": "" });
      setInputStatus({
        ...inputStatus,
        [id]: { valid, inputClass, spanClass, text },
        "isClientIdValid": isValid,
      });
    } else {
      if (values.client_id) delete values.client_id;
      setValues({ ...values, [name]: value });
      setInputStatus({
        ...inputStatus,
        [id]: { valid, inputClass, spanClass, text },
        "isClientIdValid": isValid,
      });
    }
  };

  const handleInputChannelOfRequest = (e: any, id: string) => {
    //@ts-ignore
    dispatch(setUserInputValues(true));
    const channelOfReq = values.channel_of_request;
    let { name, value } = e.target;
    setSalesChannel(value);
    let isApplicantNameValid = {
      valid: true,
      inputClass: "",
      spanClass: "",
      text: "",
    };
    let isApplicantDetailsInvalid = {
      valid: false,
      inputClass: "",
      spanClass: "",
      text: "",
    };
    let inputClass = "";
    let spanClass = "";
    let text = "";
    let valid = true;
    setSalesChannel(value);

    if (value === "Business Development Officer" && group_name === 'BDO') {
      if (channelOfReq === "Broker") {
        setValues({
          ...values,
          [name]: value,
          "applicant_name": "",
          "applicant_id": "",
          "applicant_contact_person": "",
          "applicant_business_address": "",
          "applicant_contact_number": "",
          "applicant_email_address": "",
        });
        setInputStatus({
          ...inputStatus,
          [id]: { valid, inputClass, spanClass, text },
          "isApplicantNameValid": isApplicantNameValid,
          "isApplicantBusinessAddress": isApplicantDetailsInvalid,
          "isApplicantContactNumber": isApplicantDetailsInvalid,
          "isApplicantEmailAddress": isApplicantDetailsInvalid,
          "isApplicantContactPersonValid": isApplicantDetailsInvalid
        });
      } else {
        setValues({
          ...values,
          [name]: value,
          "applicant_name": currentUser,
          "applicant_id": "",
        });
        setInputStatus({
          ...inputStatus,
          [id]: { valid, inputClass, spanClass, text },
          "isApplicantNameValid": isApplicantNameValid,
        });
      }
    } else {
      isApplicantNameValid = {
        valid: false,
        inputClass: "error",
        spanClass: "vni-text-red-500 vni-block vni-py-1",
        text: "This field is required.",
      };
      if (value === "Broker") {
        setValues({
          ...values,
          [name]: value,
          "applicant_name": "",
          "applicant_id": "",
          "applicant_contact_person": "",
        });
        setInputStatus({
          ...inputStatus,
          [id]: { valid, inputClass, spanClass, text },
          "isApplicantNameValid": isApplicantNameValid,
        });
      } else if (channelOfReq === "Broker") {
        setValues({
          ...values,
          [name]: value,
          "applicant_name": "",
          "applicant_id": "",
          "applicant_contact_person": "",
          "applicant_business_address": "",
          "applicant_contact_number": "",
          "applicant_email_address": "",
        });
        setInputStatus({
          ...inputStatus,
          [id]: { valid, inputClass, spanClass, text },
          "isApplicantNameValid": isApplicantNameValid,
          "isApplicantBusinessAddress": isApplicantDetailsInvalid,
          "isApplicantContactNumber": isApplicantDetailsInvalid,
          "isApplicantEmailAddress": isApplicantDetailsInvalid,
          "isApplicantContactPersonValid": isApplicantDetailsInvalid
        });
      } else {
        setValues({
          ...values,
          [name]: value,
          "applicant_name": "",
          "applicant_id": "",
        });
        setInputStatus({
          ...inputStatus,
          [id]: { valid, inputClass, spanClass, text },
          "isApplicantNameValid": isApplicantNameValid,
        });
      }
    }
  };

  const handleAutocomplete = (e: any, selectedVal?: any) => {
    //@ts-ignore
    dispatch(setUserInputValues(true));

    console.log("SELECTED VAL ==> ", selectedVal);
    let id = e.target.id;

    if (typeof e === "object") {
      id = e.target.id;
    }
    console.log("ID ===>", id);

    let inputClass = "";
    let spanClass = "";
    let text = "";
    let valid = true;
    let bdoName = "";
    let bdoId = "";

    if (selectedVal) {
      valid = true;
      bdoName = `${selectedVal.name}`;
      bdoId = selectedVal.user_id;
      if (selectedVal.id) {
        id = id.split("-")[0];
      }
    }

    if (!selectedVal || selectedVal === "") {
      valid = false;
      inputClass = "error";
      spanClass = "vni-text-red-500 vni-block vni-py-1";
      text = "This field is required.";
    }

    setInputStatus({
      ...inputStatus,
      [id]: { valid, inputClass, spanClass, text },
    });
    setValues((vals) => ({
      ...vals,
      applicant_name: bdoName,
      applicant_id: bdoId,
    }));
  };

  const handleInputClientId = (e: any, id: string) => {
    //@ts-ignore
    dispatch(setUserInputValues(true));
    let { name, value } = e.target;
    let isClientIdValid = {
      valid: false,
      inputClass: "",
      spanClass: "",
      text: "",
    };
    if (value.length >= 8) {
      dispatch(validateInput({ ...values, [name]: value }));
    } else if (value) {
      isClientIdValid = {
        valid: false,
        inputClass: "error",
        spanClass: "vni-text-red-500 vni-block vni-py-1",
        text: "Invalid Client ID Format.",
      };
    } else {
      isClientIdValid = {
        valid: false,
        inputClass: "error",
        spanClass: "vni-text-red-500 vni-block vni-py-1",
        text: "This field is required.",
      };
    }
    setValues({ ...values, [name]: value });
    setInputStatus({ ...inputStatus, [id]: isClientIdValid });
  };

  const handleInputTIN = async (name: string, id: string, value: string) => {
    //@ts-ignore
    dispatch(setUserInputValues(true));
    let isTaxNumberValid = {
      valid: true,
      inputClass: "",
      spanClass: "",
      text: "",
    };

    const trimmedValue = value.replace(/[^0-9]/g, "").length;

    if (value && trimmedValue > 0 && trimmedValue < 12) {
      isTaxNumberValid = {
        valid: false,
        inputClass: "error",
        spanClass: "vni-text-red-500 vni-block vni-py-1",
        text: "Invalid Tax Identification Number Format.",
      }
    } else {
      dispatch(validateInputTINId({...values, [name]: value}))
    }
    setValues({ ...values, [name]: trimmedValue === 0 ? "" : value });
    setInputStatus({ ...inputStatus, [id]: isTaxNumberValid });
    

  };

  const handleNumNA = (name: string, id: string, value: string) => {
    //@ts-ignore
    dispatch(setUserInputValues(true));
    const pattern = new RegExp(/\D/);

    let isValid = {
      valid: true,
      inputClass: "",
      spanClass: "",
      text: "",
    };

    if (
      (value && value.toUpperCase() === "N/A") ||
      (value && !pattern.test(value))
    ) {
      isValid = {
        valid: true,
        inputClass: "",
        spanClass: "",
        text: "",
      };
    } else {
      isValid = {
        valid: false,
        inputClass: "error",
        spanClass: "vni-text-red-500 vni-block vni-py-1",
        text: value === "" ? "This field is required" : "Invalid Input.",
      };
    }
    
    if (id === 'isNoOfEmployeeValid' && Number(value) === 0) {
      isValid = {
        valid: false,
        inputClass: "error",
        spanClass: "vni-text-red-500 vni-block vni-py-1",
        text: value === "" ? "This field is required" : "The minimum number should be 1 or higher",
      };
    }

    setValues({ ...values, [name]: value.toUpperCase() });
    setInputStatus({ ...inputStatus, [id]: isValid });
  };

  const handleDateAndTimeReceived = (value: any, id: string, name: string) => {
    //@ts-ignore
    dispatch(setUserInputValues(true));

    let isValidDateAndTimeReceived = {
      valid: true,
      inputClass: "",
      spanClass: "",
      text: "",
    };

    if (value) {
      if (!moment(value).isValid()) {
        isValidDateAndTimeReceived.valid = false;
        isValidDateAndTimeReceived.inputClass = "error";
        isValidDateAndTimeReceived.spanClass =
          "vni-text-red-500 vni-block vni-py-1";
        isValidDateAndTimeReceived.text = "Invalid date format.";
        setValues({ ...values, [name]: value });
      } else if (moment(value).isAfter(moment())) {
        isValidDateAndTimeReceived.valid = false;
        isValidDateAndTimeReceived.inputClass = "error";
        isValidDateAndTimeReceived.spanClass =
          "vni-text-red-500 vni-block vni-py-1";
        isValidDateAndTimeReceived.text = "Future date is not allowed";
      } else {
        setValues({
          ...values,
          [name]: moment(value).format("MM/DD/YYYY hh:mm A"),
        });
      }
    } else {
      setValues({ ...values, [name]: "" });
    }
    setInputStatus({ ...inputStatus, [id]: isValidDateAndTimeReceived });
  };

  const handleEffectivityDateChange = (value: any, id: any, name: any) => {
    //@ts-ignore
    dispatch(setUserInputValues(true));
    let isValidProviderDateFrom = {
      valid: true,
      inputClass: "",
      spanClass: "",
      text: "",
    };

    let isValidProviderDateTo = {
      valid: true,
      inputClass: "",
      spanClass: "",
      text: "",
    };

    switch (id) {
      case "isProviderEfectivityDateFromValid":
        if (value) {
          if (!moment(value).isValid()) {
            isValidProviderDateFrom.inputClass = "error";
            isValidProviderDateFrom.spanClass =
              "vni-text-red-500 vni-block vni-py-1";
            isValidProviderDateFrom.text = "Invalid date format.";
            isValidProviderDateFrom.valid = false;
            setValues({ ...values, [name]: value });
          } else {
            switch (values.provider_effectivity_date_to) {
              //@ts-ignore
              case "" &&
                ((values.provider_effectivity_date_from &&
                  values.provider_effectivity_date_from !== "Invalid date") ||
                  values.provider_effectivity_date_from !== ""):
                isValidProviderDateTo.inputClass = "error";
                isValidProviderDateTo.spanClass =
                  "vni-text-red-500 vni-block vni-py-1";
                isValidProviderDateTo.text =
                  "Please input end date or leave both fields blank.";
                isValidProviderDateTo.valid = false;
                break;
                //@ts-ignore
              case undefined &&
                ((values.provider_effectivity_date_from &&
                  values.provider_effectivity_date_from !== "Invalid date") ||
                  values.provider_effectivity_date_from !== ""):
                isValidProviderDateTo.inputClass = "error";
                isValidProviderDateTo.spanClass =
                  "vni-text-red-500 vni-block vni-py-1";
                isValidProviderDateTo.text =
                  "Please input end date or leave both fields blank.";
                isValidProviderDateTo.valid = false;
                break;
                //@ts-ignore
              case null &&
                ((values.provider_effectivity_date_from &&
                  values.provider_effectivity_date_from !== "Invalid date") ||
                  values.provider_effectivity_date_from !== ""):
                isValidProviderDateTo.inputClass = "error";
                isValidProviderDateTo.spanClass =
                  "vni-text-red-500 vni-block vni-py-1";
                isValidProviderDateTo.text =
                  "Please input end date or leave both fields blank.";
                isValidProviderDateTo.valid = false;
                break;
              default:
                if (
                  moment(value).startOf("day") >
                  moment(values.provider_effectivity_date_to).startOf("day")
                ) {
                  isValidProviderDateTo.inputClass = "error";
                  isValidProviderDateTo.spanClass =
                    "vni-text-red-500 vni-block vni-py-1";
                  isValidProviderDateTo.text = "Invalid date";
                  isValidProviderDateTo.valid = false;
                }
                break;
            }
            setValues({
              ...values,
              [name]: moment(value).format("MM/DD/YYYY"),
            });
          }
        } else {
          setValues({
            ...values,
            [name]: "",
            "provider_effectivity_date_to": "",
          });
        }
        break;

      case "isProviderEfectivityDateToValid":
        if (value) {
          if (!moment(value).isValid()) {
            isValidProviderDateTo.inputClass = "error";
            isValidProviderDateTo.spanClass =
              "vni-text-red-500 vni-block vni-py-1";
            isValidProviderDateTo.text = "Invalid date format.";
            isValidProviderDateTo.valid = false;
            setValues({ ...values, [name]: value });
          } else {
            if (
              moment(value).startOf("day") <
              moment(values.provider_effectivity_date_from).startOf("day")
            ) {
              isValidProviderDateTo.inputClass = "error";
              isValidProviderDateTo.spanClass =
                "vni-text-red-500 vni-block vni-py-1";
              isValidProviderDateTo.text = "Invalid date";
              isValidProviderDateTo.valid = false;
            }
            setValues({
              ...values,
              [name]: moment(value).format("MM/DD/YYYY"),
            });
          }
        } else {
          setValues({ ...values, [name]: "" });
          if (
            values.provider_effectivity_date_from &&
            values.provider_effectivity_date_from !== ""
          ) {
            isValidProviderDateTo.inputClass = "error";
            isValidProviderDateTo.spanClass =
              "vni-text-red-500 vni-block vni-py-1";
            isValidProviderDateTo.text =
              "Please input end date or leave both fields blank.";
            isValidProviderDateTo.valid = false;
          }
        }
        break;
      default:
        break;
    }
    setInputStatus({
      ...inputStatus,
      "isProviderEfectivityDateFromValid": isValidProviderDateFrom,
      "isProviderEfectivityDateToValid": isValidProviderDateTo,
    });
  };

  const handleCursor = (e: any, input_type?: string) => {
    let ss = e.target.selectionStart;
    let se = e.target.selectionEnd;

    if (input_type === "select") {
      // eslint-disable-next-line
      e.target.value = e.target.value;
    } else {
      // eslint-disable-next-line
      e.target.value = e.target.value.toUpperCase();
    }

    e.target.selectionStart = ss;
    e.target.selectionEnd = se;

    return;
  };

  const handleInput = (e: any, input_type?: string, input_id?: string) => {
    //@ts-ignore
    dispatch(setUserInputValues(true));
    handleCursor(e, input_type);

    let { name, value, id, type, min, max } = e.target;

    let inputClass = "";
    let spanClass = "";
    let text = "";
    let valid = false;
    if (input_type) {
      type = input_type;
    }
    if (input_type === "select") {
      id = input_id;
    }
    if (value) {
      valid = true;
      switch (type) {
        case "text":
          switch (id) {
            case "isApplicantEmailAddress":
              if (
                validEmailRegex.test(value) ||
                value.toUpperCase() === "N/A"
              ) {
                valid = true;
              } else {
                valid = false;
                inputClass = "error";
                spanClass = "vni-text-red-500 vni-block vni-py-1";
                text = "Invalid email address.";
              }
              break;
            case "isApplicantContactPersonValid":
              valid = true;
              break;
            case "isApplicantNameValid":
              valid = true;
              break;
            default:
              break;
          }
          break;
        case "number":
          let num = Number(value);
          value =
            Math.max(Number(min), Math.min(Number(max), Number(num))) + "";
          break;
        default:
          break;
      }
    } else {
      if (id === "isOldAccountNameValid") {
        valid = true;
      } else {
        inputClass = "error";
        spanClass = "vni-text-red-500 vni-block vni-py-1";
        text = "This field is required.";
        valid = false
      }

      if (id === 'isApplicantContactPersonValid') {
        valid = false;
        inputClass = "error";
        spanClass = "vni-text-red-500 vni-block vni-py-1";
        text = "This field is required.";
      }
    }
    setInputStatus({
      ...inputStatus,
      [id]: { valid, inputClass, spanClass, text },
      // isApplicantContactPersonValid: {
      //   valid: true,
      //   inputClass: "",
      //   spanClass: "",
      //   text: "",
      // },
    });
    setValues({ ...values, [name]: value });
    setFname(value);
  };

  const handleInputIsMotherCompany = (value: boolean, name: string) => {
    //@ts-ignore
    dispatch(setUserInputValues(true));
    setValues({ ...values, [name]: value });
  };

  const handleInputContactPerson = (e: any, value: any, valueObject?: any) => {
    if (e) e.preventDefault();

    const fullAddress =
      value && value.business_address
        ? `${
            value.business_address.unit
              ? value.business_address.unit + ", "
              : ""
          }
		${value.business_address.floor ? value.business_address.floor + ", " : ""}
		${value.business_address.bldg_name}, 
		${value.business_address.street}, 
		${value.business_address.brgy}, 
		${value.business_address.city}, 
		${value.business_address.province}, 
		${
      value.business_address.zip_code
        ? value.business_address.country + ", "
        : value.business_address.country
    }
		${value.business_address.zip_code ? value.business_address.zip_code : ""}`
        : "";

    //@ts-ignore
    dispatch(setUserInputValues(true));

    let isApplicantContactPersonValid = {
      valid: true,
      inputClass: "",
      spanClass: "",
      text: "",
    };

    let isApplicantContactPersonInvalid = {
      valid: false,
      inputClass: "error",
      spanClass: "vni-text-red-500 vni-block vni-py-1",
      text: "This field is required.",
    };

    if (e && value && typeof value === "object") {
      setValues({
        ...values,
        "applicant_contact_person": value.contact_person,
        "applicant_business_address": fullAddress
          .replace(/(\r\n|\n|\r)/gm, "")
          .replace(/\t+/g, "")
          .trim()
          .toUpperCase(),
        "applicant_contact_number": value.contact_number.toUpperCase(),
        "applicant_email_address": value.email_address.toUpperCase(),
      });
      setInputStatus({
        ...inputStatus,
        "isApplicantBusinessAddress": isApplicantContactPersonValid,
        "isApplicantContactNumber": isApplicantContactPersonValid,
        isApplicantContactPersonValid: isApplicantContactPersonValid,
      });
    } else if (!e && value && typeof value !== "object") {
      setValues({
        ...values,
        "applicant_contact_person": value.toUpperCase(),
      });
      setInputStatus({
        ...inputStatus,
        isApplicantContactPersonValid: isApplicantContactPersonValid,
      });
    } else if (!value && valueObject) {
      setValues({
        ...values,
        "applicant_contact_person": "",
        "applicant_business_address": "",
        "applicant_contact_number": "",
        "applicant_email_address": "",
      });
      setInputStatus({
        ...inputStatus,
        "isApplicantBusinessAddress": isApplicantContactPersonInvalid,
        "isApplicantContactNumber": isApplicantContactPersonInvalid,
        "isApplicantEmailAddress": isApplicantContactPersonInvalid,
        isApplicantContactPersonValid: isApplicantContactPersonValid,
      });
    } else {
      setValues({ ...values, "applicant_contact_person": "" });
      setInputStatus({
        ...inputStatus,
        isApplicantContactPersonValid: isApplicantContactPersonValid,
      });
    }
  };

  const handleInputAffiliates = (
    type: any,
    value: CompanyAffiliates,
    name: string
  ) => {
    //@ts-ignore
    dispatch(setUserInputValues(true));

    let isCompanyAffiliatesValid = {
      valid: true,
      inputClass: "",
      spanClass: "",
      text: "",
    };

    let isCompanyAffiliatesInvalid = {
      valid: false,
      inputClass: "error",
      spanClass: "vni-text-red-500 vni-block vni-py-1",
      text: "This field is required.",
    };

    switch (type) {
      case "reset":
        setValues({
          ...values,
          [name]: value!.corporate_name,
          "company_affiliates_id": value!._id,
        });
        setInputStatus({
          ...inputStatus,
          "isCompanyAffiliatesValid": isCompanyAffiliatesValid,
        });
        break;
      case "input":
        if (value!.corporate_name && value!.corporate_name?.trim().length > 0) {
          setValues({
            ...values,
            [name]: value!.corporate_name,
            "company_affiliates_id": value!._id,
          });
          setInputStatus({
            ...inputStatus,
            "isCompanyAffiliatesValid": isCompanyAffiliatesValid,
          });
        } else {
          setValues({
            ...values,
            [name]: value!.corporate_name,
            "company_affiliates_id": value!._id,
          });
          setInputStatus({
            ...inputStatus,
            "isCompanyAffiliatesValid": isCompanyAffiliatesInvalid,
          });
        }
        break;
      case "validate":
        setInputStatus({
          ...inputStatus,
          "isCompanyAffiliatesValid": isCompanyAffiliatesInvalid,
        });
        break;
      default:
        break;
    }
  };

  const handleSelectRadio = (name: string, value: string, id: string) => {
    //@ts-ignore
    dispatch(setUserInputValues(true));
    setValues({ ...values, [name]: value });
    if (value) {
      setInputStatus({
        ...inputStatus,
        [id]: { valid: value !== "", inputClass: "", spanClass: "", text: "" }
      });
    } else {
      setInputStatus({
        ...inputStatus,
        [id]: { valid: value !== "", inputClass: "", spanClass: "", text: "" }
      });
    }
  };

  const getUploadFileGetter = (files: any[]) => {
    const name = "supporting_docs";
    const objname = "supporting_docs_obj";
    let newFiles = [] as any[];
    let newCombinedFiles = [] as any[];
    let newFilesObj = [];
    if (values.supporting_docs_obj) {
      newFilesObj = [...values.supporting_docs_obj, ...files];
    } else {
      newFilesObj = files;
    }
    for (var i = 0; i < files.length; i++) {
      const file = files[i];
      newFiles.push(file._id);
    }
    if (values.supporting_docs) {
      newCombinedFiles = [...values.supporting_docs, ...newFiles];
    } else {
      newCombinedFiles = newFiles;
    }
    console.log({
      ...values,
      [name]: newCombinedFiles,
      [objname]: newFilesObj,
    });
    setValues({ ...values, [name]: newCombinedFiles, [objname]: newFilesObj });
  };

  const setClearFilterFile = (file: any[]) => {
    const name = "supporting_docs";
    const objname = "supporting_docs_obj";
    let files = file;
    let newFiles = [] as any[];

    for (var i = 0; i < files.length; i++) {
      const file = files[i];
      newFiles.push(file._id);
    }

    if (values.supporting_docs_obj) {
      const appointmentLetter = values.supporting_docs_obj?.filter(
        (file) => file.purpose === "appointment_letter"
      );
      if (appointmentLetter.length > 0) {
        files = [...file, appointmentLetter[0]];
        newFiles.push(appointmentLetter[0].id);
      }
    }

    setValues({ ...values, [name]: newFiles, [objname]: files });
  };

  // const handleInputRemark = (e: any) => {
  //   e.preventDefault();
  //   let { name, value } = e.target;
  //   setValues({ ...values, [name]: value });
  // };

  // let remarksGrid = (
  //   <Grid className="vni-flex vni-flex-col vni-mt-3 vni-mb-5">
  //     <Typography>Remarks (Optional)</Typography>

  //     <TextField
  //       data-cy="remarks-textarea"
  //       className="CustomInput"
  //       variant="outlined"
  //       name="resubmit_remarks"
  //       id="remarks"
  //       label=""
  //       multiline
  //       rowsMax={3}
  //       rows={3}
  //       onChange={handleInputRemark}
  //     />
  //   </Grid>
  // );

  const appointmentLetterForUpload = useSelector(
    (state: RootState) => state.franchise.appointmentLetter
  );
  const appointmentLetterDetails = useSelector(
    (state: RootState) => state.franchise.appointmentLetterDetails
  );
  const pendingAppointmentLetter = useSelector(
    (state: RootState) => state.franchise.appointmentLetterToUpload
  );

  const handleFooterButtonClick = (e: any, action: string) => {
    e.preventDefault();
    action = action.toLowerCase();
    switch (action) {
      case "cancel create":
        setModalContent({
          title: "Cancel Create?",
          description:
            "Are you sure you want to cancel creating your Franchise Request? This will not be saved.",
          button: (
            <>
              <Button
                data-cy="no-btn"
                className={classes.leftButtonOutline}
                onClick={handleClose}
              >
                No
              </Button>
              <Button
                data-cy="yes-btn"
                className={classes.rightButton}
                onClick={(e) => handleConfirmButtonClick(e, action)}
              >
                Yes
              </Button>
            </>
          ),
        });
        setOpenModal(true);
        break;
      case "cancel edit":
        setModalContent({
          title: "Cancel Edit?",
          description:
            "Are you sure you want to cancel editing Franchise Request? This will not be saved.",
          button: (
            <>
              <Button
                data-cy="no-btn"
                className={classes.leftButtonOutline}
                onClick={handleClose}
              >
                No
              </Button>
              <Button
                data-cy="yes-btn"
                className={classes.rightButton}
                onClick={(e) => handleConfirmButtonClick(e, action)}
              >
                Yes
              </Button>
            </>
          ),
        });
        setOpenModal(true);
        break;
      case "save":
        if (pendingAppointmentLetter) {
          appointmentLetterForUpload.forEach((file) => {
            dispatch(uploadAppointmentLetter(file, appointmentLetterDetails));
          });

          setButtonClick(true);
          setButtonAction(action);
        } else {
          setButtonClick(true);
          setButtonAction(action);
        }
        break;
      case "back":
        dispatch(createBackProgress());
        break;
      case "next":
        if (pendingAppointmentLetter) {
          appointmentLetterForUpload.forEach((file) => {
            dispatch(uploadAppointmentLetter(file, appointmentLetterDetails));
          });

          setButtonClick(true);
          setButtonAction(action);
        } else {
          setButtonClick(true);
          setButtonAction(action);
        }
        break;
      case "submit":
        dispatch(setFranchiseData(values));
        setModalContent({
          title: "Submit Request?",
          description:
            "Are you sure you want to submit your Franchise Request? This will be subject for review by the Franchising Specialist.",
          button: (
            <>
              <Button
                data-cy="submit-no"
                className={classes.leftButtonOutline}
                onClick={handleClose}
              >
                No
              </Button>
              <Button
                data-cy="submit-yes"
                className={classes.rightButton}
                onClick={(e) => handleConfirmButtonClick(e, "submit")}
              >
                Yes
              </Button>
            </>
          ),
        });
        setOpenModal(true);
        break;
      case "update":
        dispatch(setFranchiseData(values));
        setModalContent({
          title: "Update Request?",
          description:
            "Are you sure you want to update your Franchise Request?",
          button: (
            <>
              <Button
                data-cy="update-no"
                className={classes.leftButtonOutline}
                onClick={handleClose}
              >
                No
              </Button>
              <Button
                data-cy="update-yes"
                className={classes.rightButton}
                onClick={(e) => handleConfirmButtonClick(e, "submit")}
              >
                Yes
              </Button>
            </>
          ),
        });
        setOpenModal(true);
        break;
      default:
        break;
    }
    dispatch(clearValidationModalContent());
  };

  const handleDownload = (e: any, _id: string, filename: string) => {
    e.preventDefault();
    dispatch(downloadFranchiseFileUpload(_id, filename));
  };

  const handleConfirmButtonClick = (e: any, action: string) => {
    e.preventDefault();

    console.log("NEW VALUES: ", values);

    switch (action.toLowerCase()) {
      case "cancel create":
        setModalContent({
          title: "Request Cancelled",
          description: "You have cancelled Create Franchise Request.",
          button: (
            <Button
              data-cy="back-to-dash"
              className={classes.leftButtonOutline}
              onClick={(e) => handleConfirmButtonClick(e, "back")}
            >
              Back to Dashboard
            </Button>
          ),
        });
        dispatch(setCurrentRecordId(null));
        break;
      case "cancel edit":
        setModalContent({
          title: "Request Cancelled",
          description: "You have cancelled Edit Franchise Request.",
          button: (
            <>
              <Button
                data-cy="back-to-dashboard"
                className={classes.leftButtonOutline}
                onClick={(e) => handleConfirmButtonClick(e, "back")}
              >
                Back to Dashboard
              </Button>
              {canAddFR && (
                <Button
                  data-cy="create-new"
                  className={classes.rightButton}
                  onClick={(e) => handleConfirmButtonClick(e, "new")}
                >
                  Create New
                </Button>
              )}
            </>
          ),
        });
        dispatch(setCurrentRecordId(null));
        break;
      case "new":
        dispatch(resetState());
        dispatch(setCurrentRecordId(null));
        dispatch(setIsCreate(true));
        dispatch(goBackAndCreateNew());
        setInputStatus(emptyInputValidStatus);
        history.push("/franchising/franchise/create");
        window.scrollTo(0, 0);
        setOpenModal(false);
        break;
      case "save":
        console.log('save handleconfirm', values);
        dispatch(saveFranchise(values));
        if (pendingAppointmentLetter) {
          appointmentLetterForUpload.forEach((file) => {
            dispatch(uploadAppointmentLetter(file, appointmentLetterDetails));
          });
        }
        break;
      case "submit":
        if (values.status === "RETURNED") {
          dispatch(updateFranchiseRequest(values._id, "resubmit", "encoder", values));
        } else if (
          values.status === "RESUBMITTED" ||
          values.status === "SUBMITTED"
        ) {
          // this will not change status
          dispatch(patchFranchiseRequest(values));
        } else {
          // this will change status to submitted
          dispatch(addFranchise(values));
        }

        break;
      default:
        dispatch(resetState());
        // dispatch(setsaveSubmitInProgress(false))
        history.push("/franchising/");
        break;
    }
    dispatch(clearValidationModalContent());
  };

  // const hideModalCloseButton = () => {
  //   return (!validationContent.error && validationContent.title !== "") ||
  //   title === "Request Cancelled";
  // }

  const handleFranchiseeContactPersonInput = (person: string) => {
    setValues((values) => ({ ...values, applicant_contact_person: person }));
    if (values) {
      setInputStatus((stat) => ({
        ...stat,
        isApplicantContactPersonValid: {
          valid: true,
          inputClass: "",
          spanClass: "",
          text: "",
        },
      }));
    } else {
      setInputStatus((stat) => ({
        ...stat,
        isApplicantContactPersonValid: {
          valid: false,
          inputClass: "error",
          spanClass: "vni-text-red-500 vni-block vni-py-1",
          text: "This field is required",
        },
      }));
    }
  };

  const handleFranchiseeBusinessAddress = (address: string) => {
    setValues((values) => ({ ...values, applicant_business_address: address }));
     if (values) {
      setInputStatus((stat) => ({
        ...stat,
        isApplicantBusinessAddress: {
          valid: true,
          inputClass: "",
          spanClass: "",
          text: "",
        },
      }));
    } else {
      setInputStatus((stat) => ({
        ...stat,
        isApplicantBusinessAddress: {
          valid: true,
          inputClass: "error",
          spanClass: "vni-text-red-500 vni-block vni-py-1",
          text: "This field is required",
        },
      }));
    }
    
  };

  const handleFranchiseeEmail = (email: string) => {
    // handleInput({ target: {id: 'isApplicantEmailAddress', value: email }}) 
    setValues((values) => ({ ...values, applicant_email_address: email }));
      if (email) {
        if(validEmailRegex.test(email)) {
          setInputStatus((stat) => ({
            ...stat,
            isApplicantEmailAddress: {
              valid: true,
              inputClass: "",
              spanClass: "",
              text: "",
            },
          }));
        } else {
          setInputStatus((stat) => ({
            ...stat,
            isApplicantEmailAddress: {
              valid: false,
              inputClass: "error",
              spanClass: "vni-text-red-500 vni-block vni-py-1",
              text: "Invalid email address",
            },
          }));
        }
    } else {
      setInputStatus((stat) => ({
        ...stat,
        isApplicantEmailAddress: {
          valid: false,
          inputClass: "error",
          spanClass: "vni-text-red-500 vni-block vni-py-1",
          text: "This field is required",
        },
      }));
    }
  };

  const handleFranchiseeContactNumber = (number: any) => {
    // handleNumNA('applicant_contact_number', 'isApplicantContactNumber', number)
    setValues((values) => ({ ...values, applicant_contact_number: number }));
   
    if (number) {
      if(!isNaN(number)){
        setInputStatus((stat) => ({
          ...stat,
          isApplicantContactNumber: {
            valid: true,
            inputClass: "",
            spanClass: "",
            text: "",
          },
        }));
      } else {

      setInputStatus((stat) => ({
        ...stat,
        isApplicantContactNumber: {
          valid: false,
          inputClass: "error",
          spanClass: "vni-text-red-500 vni-block vni-py-1",
          text: "Invalid input",
        },
      }));
      }

    } else {
      setInputStatus((stat) => ({
        ...stat,
        isApplicantContactNumber: {
          valid: false,
          inputClass: "error",
          spanClass: "vni-text-red-500 vni-block vni-py-1",
          text: "This field is required",
        },
      }));
    }
  };


  const handleFranchiseeName = (name: string, { user_id }: { user_id: string}) => {
    if (name && name.includes("-")) {
      setValues((values) => ({
        ...values,
        applicant_id: user_id,
        applicant_name: name.split("-")[1],
      }));
    } else {
      setValues((values) => ({ 
        ...values,
        applicant_id: user_id,
        applicant_name: name 
      }));
    }

    if (name) {
      setInputStatus((stat) => ({
        ...stat,
        isApplicantNameValid: {
          valid: true,
          inputClass: "",
          spanClass: "",
          text: "",
        },
      }));
    } else {
      setInputStatus((stat) => ({
        ...stat,
        isApplicantNameValid: {
          valid: true,
          inputClass: "error",
          spanClass: "vni-text-red-500 vni-block vni-py-1",
          text: "This field is required",
        },
      }));
    }
  };

  const checkToken = (accessToken: any) => {
    let URL = apiURL.userManagement + '/underwriting/user/check'

    return fetch(URL, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + accessToken,
        }}).then(async (res: any) => {
            if (!res.ok) {
                let respjson = await res.json()
                let errormessage = respjson.error.message
                throw Error(errormessage)
            }
            return res ? res.json() : null
        })
  }

  const isTokenValid = async () => {
    let isValid = true,
    isPmValid = true,
    isApiValid = true;
    
    if (!localStorage.getItem('api_token') || !localStorage.getItem('pm_token')) {
      isValid = false;
    } else if (
      localStorage.getItem('api_token') &&
      localStorage.getItem('pm_token')
    ) {
      try {
        let api_token = await checkToken(localStorage.getItem('api_token'));
        let pm_token = await checkToken(localStorage.getItem('pm_token'));
        if (!api_token.status) {
          isApiValid = false;
        }
        if (!pm_token.status) {
          isPmValid = false;
        }
      } catch (error) {
        console.log(error, 'error');
        isPmValid = false;
        isApiValid = false;
      }
    }

    console.log('TOKEN VALIDITY', isValid, isPmValid, isApiValid);
    if (!isValid || !isPmValid || !isApiValid) {
      // console.log('INVALID TOKEN');
      // var a = '../customer-care/index.html#/franchising' + '/invalidSession';
      // window.location.href = a;
      //onLogout();
      setViewExpiredModal(true);
    }
  };
  
  useEffect(() => {
    console.log('state current record id', formStateId || newFranchise._id);
    if (formStateId || (isEdit && newFranchise._id)) {
      setValues((values) => ({ 
        ...values,
        _id: formStateId,
        isEdit: isEdit
      }));
    }
    console.log('state edit', isEdit)
  }, [formStateId, franchiseData, isEdit])

  useEffect(() => {
    isTokenValid()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token])

  console.log('state current value', values)
  return (
    <span>
      {tab === 0 ? (
        <CreateEditFranchiseRequest
          group_name={group_name}
          handleInputTIN={handleInputTIN}
          handleNumNA={handleNumNA}
          inputRef={myRef}
          franchise={values}
          inputStatus={inputStatus}
          handleFranchiseeContactPersonInput={
            handleFranchiseeContactPersonInput
          }
          handleFranchiseeName={handleFranchiseeName}
          handleFranchiseeBusinessAddress={handleFranchiseeBusinessAddress}
          handleFranchiseeEmail={handleFranchiseeEmail}
          handleFranchiseeContactNumber={handleFranchiseeContactNumber}
          handleInputChange={handleInput}
          handleInputCursor={handleCursor}
          handleRadioChange={handleSelectRadio}
          handleInputBusinessAddress={handleInputBusinessAddress}
          handleInputRefranchise={handleInputRefranchise}
          handleInputAutocomplete={handleAutocomplete}
          handleInputChannelOfRequest={handleInputChannelOfRequest}
          handleInputClientId={handleInputClientId}
          authorized_contact_persons={
            values.authorized_contact_persons ?? [initialNameTypeValue]
          }
          stakeholders={values.stakeholders ?? [initialNameTypeValue]}
          signatories={values.signatories ?? [initialNameTypeValue]}
          handleInputArray={handleInputDynamicArray}
          handleEffectivityDate={handleEffectivityDateChange}
          isEditing={isEdit}
          validationContent={validationContent}
          handleInputAffiliates={handleInputAffiliates}
          handleInputIsMotherCompany={handleInputIsMotherCompany}
          handleInputContactPerson={handleInputContactPerson}
          handleDateAndTimeReceived={handleDateAndTimeReceived}
        />
      ) : tab === 1 && canUploadDocs ? (
        <>
          <UploadFranchiseDocuments
            handleUploadDocument={getUploadFileGetter}
            franchise={values}
            handleClearFileUpload={setClearFilterFile}
          />
        </>
      ) : (
        <SubmitFranchiseRequest
          handleDownload={handleDownload}
          franchise={values}
        />
      )}

      <CreateFranchiseFooterButton
        tab={tab}
        handleButton={handleFooterButtonClick}
      />

      {!isRequesting && (
        <Modal
          fullWidth={false}
          maxWidth="md"
          open={openModal}
          onClose={handleClose}
          // //@ts-ignore
          // onClose={(event, reason) => {
          //   if(reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
          //     hideModalCloseButton()
          //   }
          // }}
        >
          <Grid>
            <Typography variant="h5" className={classes.modalTitle}>
              {title}
            </Typography>
            <Typography
              className={classes.modalMessage}
              style={{ maxWidth: 500 }}
              paragraph={true}
            >
              {description}
            </Typography>
            {remarks}
            <Grid className="btn-group" style={{ textAlign: "center" }}>
              {button}
            </Grid>
          </Grid>
        </Modal>
      )}

        { sessionExpired && (


        <Dialog 
          id="session-modal"
          maxWidth='xs'
          open={sessionExpired}
          onClose={() => {
            dispatch(logout())
            window.location.replace('../index.html#/')
          }}
          className="vni-m-auto"
        >
          <DialogTitle className="vni-px-5 vni-pt-3 vni-mb-8">
            <h2 className="title">Session Expired</h2>
          </DialogTitle>
          <DialogContent>
            <p className="vni-mb-8">
            Your account has logged in on another device. Please contact your administrator if it was not you.
            </p>
          </DialogContent>
          <DialogActions className="vni-flex d-flex-center">
            <button className="CustomPrimaryButton vni-mr-5" data-cy="session-timeout-logout-btn" onClick={() => {
              dispatch(logout())
              window.location.replace('../index.html#/')
              
              }}>Okay</button>
          </DialogActions>
        </Dialog>

        
        )}

      {viewExpiredModal === true && (

        <Dialog 
            id="session-modal-expired"
            maxWidth='xs'
            open={viewExpiredModal}
            onClose={() => {
            dispatch(logout())
            window.location.replace('../index.html#/')
            }}
            className="vni-m-auto"
        >
            <DialogTitle className="vni-px-5 vni-pt-3 vni-mb-8">
              <h2 className="title">Session Expired</h2>
            </DialogTitle>
            <DialogContent>
              <p className="vni-mb-8">
              Your account has been logged out. Please try logging in again.
              </p>
            </DialogContent>
            <DialogActions className="vni-flex d-flex-center">
              <button className="CustomPrimaryButton vni-mr-5" data-cy="session-timeout-logout-btn" onClick={() => {
                  dispatch(logout())
                  window.location.replace('../index.html#/')
        
                  }}>Okay</button>
            </DialogActions>
        </Dialog>
      )};

    </span>
  );
};
export default CreateFranchiseContainer;
