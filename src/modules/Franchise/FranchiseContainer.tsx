import React, { Fragment, useEffect, useState } from "react";
import { useLocation, Switch, Route } from "react-router-dom";
import { useDispatch } from "react-redux";
import { changeFilter } from "../../shared/reducers/FranchiseSlice";

import { Box } from "@mui/material";
import { RootState } from "../../shared/reducers/rootReducer";
import { useSelector } from "react-redux";

// Franchises
import SalesFranchise from "./FranchiseTable/SalesFranchise";
import SpecialistFranchise from "./FranchiseTable/SpecialistFranchise";
import SupervisorFranchise from "./FranchiseTable/SupervisorFranchise";
import ManagerFranchise from "./FranchiseTable/ManagerFranchise";

import { frsRole } from "../../shared/components/Layout/constants";

const FranchiseContainer: React.FC = () => {
  // const token = useSelector((state: RootState) => state.login.token);
  const isRequesting = useSelector(
    (state: RootState) => state.franchise.isRequesting
  );

  const { isManager, isSpecialist, isSalesUser, isSupervisor } = useSelector(
    (state: RootState) => state.login.userRoles
  );

  const [isMultipleRoles, setIsMultipleRoles] = useState<boolean>(false);
  const dispatch = useDispatch();
  let location = useLocation();

  useEffect(() => {
    dispatch(changeFilter({ filterIndex: 0, name: "All" }));
    window.scrollTo(0, 0);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location]);

  const subRoles = useSelector((state: RootState) => state.login.role);
  const mainRole = useSelector((state: RootState) => state.login.main_role);
  const canSearchFR = useSelector(
    (state: RootState) => state.login.userPermissions.canSearchFR
  );

  useEffect(() => {
    if (subRoles.length || mainRole) {
      const _roles = subRoles
        .filter(
          (i: any) =>
            i.module === "Franchising" && Object.keys(frsRole).includes(i.name)
        )
        .map((r: any) => r.name);
      if(Object.keys(frsRole).includes(mainRole)) _roles.unshift(mainRole);
      setIsMultipleRoles(
        _roles.filter((value, index, self) => self.indexOf(value) === index)
          .length > 1
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subRoles]);

  return (
    <Fragment>
      <Box className="main vni-py-8 vni-px-12">
      <Switch>
          {isSalesUser && (
            <Route
              path="/franchising/franchise/encoder"
              render={(props) => (
                <SalesFranchise
                  {...props}
                  canSearchFR={canSearchFR}
                  isRequesting={isRequesting}
                  isMultipleRoles={isMultipleRoles}
                />
              )}
            />
          )}
          {isSpecialist && (
            <Route
              path="/franchising/franchise/validator"
              render={(props) => (
                <SpecialistFranchise
                  {...props}
                  canSearchFR={canSearchFR}
                  isMultipleRoles={isMultipleRoles}
                />
              )}
            />
          )}
          {isSupervisor && (
            <Route
              path="/franchising/franchise/supervisor"
              render={(props) => (
                <SupervisorFranchise
                  {...props}
                  canSearchFR={canSearchFR}
                  isMultipleRoles={isMultipleRoles}
                />
              )}
            />
          )}
          {isManager && (
            <Route
              path="/franchising/franchise/manager"
              render={(props) => (
                <ManagerFranchise
                  {...props}
                  canSearchFR={canSearchFR}
                  isMultipleRoles={isMultipleRoles}
                />
              )}
            />
          )}
          {/* {!token && (
            <Route path="/franchising/*" render={(props) => <SessionExpired {...props} token={token} />}></Route>
          )} */}
        </Switch>
      </Box>
    </Fragment>
  );
};

export default FranchiseContainer;
