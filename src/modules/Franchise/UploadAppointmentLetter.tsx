import React, { useEffect, useState } from "react";

import { useDropzone } from "react-dropzone";
import { useDispatch, useSelector } from "react-redux";
import {
  setAppointmentLetterDetails,
  setAppointmentLetter,
  clearAppointmentLetter,
  uploadAppointmentLetter,
} from "../../shared/reducers/FranchiseSlice";
import UploadFileItem from "./UploadFileItem";
import { RootState } from "../../shared/reducers/rootReducer";
import { changeFileIcon } from "./UploadFranchiseDocuments";
import {
  LocalizationProvider,
  DatePicker
} from "@mui/x-date-pickers";
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { fileTypes, TOTAL_MAX_SIZE } from "../../utils/Environment";
import {
  Typography,
  Grid,
  Button,
  FormControlLabel,
  RadioGroup,
  Radio,
} from "@mui/material";
import { Modal } from "../../shared/components/Modal";
import { Franchise } from "../../shared/models/Franchise";
import { PreviewStyles } from "../../shared/components/ViewPDFReport/style";

type UploadAppointmentLetterProps = {
  franchise: Franchise;
};

export default function UploadAppointmentLetter(
  props: UploadAppointmentLetterProps
) {
  const { getRootProps, getInputProps, open } = useDropzone({
    accept: fileTypes,
    onDrop: (e) => handleFileUpload(e),
    onDropRejected: (e) => handleFileOnError(e),
    // getFilesFromEvent: (e) => handleFileUploadEvent(e),
    noClick: true,
    noKeyboard: true,
    minSize: 0,
    maxSize: TOTAL_MAX_SIZE,
  });

  const classes = PreviewStyles();

  const { franchise } = props;
  const [effectivityDate, setEffectivityDate] = useState(
    new Date().toLocaleDateString()
  );
  const [expiryDate, setExpiryDate] = useState(new Date().toLocaleDateString());
  const [agent, setAgent] = useState("none");
  const [rejectedFiles, setRejectedFiles] = useState([] as any[]);
  const [openErrModal, setOpenErrModal] = useState(false);
  const [existingFile, setExistingFile] = useState([] as any[]);

  const dispatch = useDispatch();
  const aLetter = useSelector(
    (state: RootState) => state.franchise.appointmentLetter
  );
  const aDetails = useSelector(
    (state: RootState) => state.franchise.appointmentLetterDetails
  );

  // useEffect(() => {
  //   // set other details on change
  //   const details = {
  //     agent: agent,
  //     valid_from: effectivityDate || new Date().toLocaleDateString(),
  //     valid_until: expiryDate || new Date().toLocaleDateString(),
  //   };

  //   dispatch(setAppointmentLetterDetails(details));
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [agent, effectivityDate, expiryDate]);

  // useEffect(() => {
  //   // get details and set details on mount
  //   if (aDetails) {
  //     setAgent(aDetails.agent);
  //     setEffectivityDate(aDetails.valid_from);
  //     setExpiryDate(aDetails.valid_until);
  //   }

  //   if (franchise.appointment_letter?.length > 0) {
  //     setExistingFile(franchise.appointment_letter);
  //     setAgent(franchise.appointment_letter[0].agent);
  //     setEffectivityDate(franchise.appointment_letter[0].validFrom);
  //     setExpiryDate(franchise.appointment_letter[0].validTo);
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, []);

  const handleCloseErrModal = () => {
    setOpenErrModal((prevState) => !prevState);
  };

  const handleFileUpload = async (files: Array<File>) => {
    const filesUploaded = [];

    if (existingFile.length === 0 && aLetter.length === 0) {
      for (let i = 0; i < files.length; i++) {
        filesUploaded.push(files[i]);
        dispatch(setAppointmentLetter(files[i]));
        dispatch(uploadAppointmentLetter(files[i], aDetails));
      }
    } else {
      return setOpenErrModal((prevState) => !prevState);
    }
  } 
  
  // const handleFileUploadEvent = async (event: any) => {
  //   const filesUploaded = [];
  //   const files = event.dataTransfer
  //     ? event.dataTransfer.files
  //     : event.target.files;

  //   if (existingFile.length === 0 && aLetter.length === 0) {
  //     for (let i = 0; i < files.length; i++) {
  //       filesUploaded.push(files[i]);
  //       dispatch(setAppointmentLetter(files[i]));
  //     }
  //   } else {
  //     return setOpenErrModal((prevState) => !prevState);
  //   }

  //   return files
  // };

  const handleFileOnError = (files: Array<File>) => {
    setRejectedFiles(files);
  };

  const handleFileErrorMessage = (file: File) => {
    if (fileTypes.indexOf(file.type) < 0) {
      return "File type not supported.";
    } else if (file.size > TOTAL_MAX_SIZE) {
      return "File size exceeds 1GB.";
    } else if (file.hasOwnProperty("fileExceed")) {
      return "Total file size exceeds 1GB.";
    }
    return "File not supported.";
  };

  const dropzoneRef: any = React.useRef<any>(null);
  return (
    <>
      <div className="main vni-py-8 vni-px-12" id="upload_documents">
        <div className="vni-flex vni-justify-between vni-items-center">
          <RadioGroup
            aria-labelledby="demo-radio-buttons-group-label"
            defaultValue="female"
            name="radio-buttons-group"
            row
          >
            <FormControlLabel
              control={
                <Radio
                  checked={agent === "Broker on Record"}
                  onChange={() => setAgent("Broker on Record")}
                />
              }
              label="Broker on Record"
            />
            <FormControlLabel
              control={
                <Radio
                  checked={agent === "Agent on Record"}
                  onChange={() => setAgent("Agent on Record")}
                />
              }
              label="Agent on Record"
            />
            <FormControlLabel
              control={
                <Radio
                  checked={agent === "Direct"}
                  onChange={() => setAgent("Direct")}
                />
              }
              label="Direct"
            />
            <FormControlLabel
              control={
                <Radio
                  checked={agent === "none"}
                  onChange={() => setAgent("none")}
                />
              }
              label="None"
            />
          </RadioGroup>
          <div className="vni-w-48 date-holder">
            <label style={{ opacity: 1 }} htmlFor="">
              Effectivity Date
            </label>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                format="MM/dd/yyyy"
                value={effectivityDate}
                onChange={(e: any) => {
                  setEffectivityDate(new Date(e).toLocaleDateString());
                }}
                disabled={agent === "none" ? true : false}
                slotProps={{
                  textField: {
                    id: "isProviderEfectivityDateFromValid",
                    className: "CustomInput vni-datepicker create-date-fix",
                    name: "provider_effectivity_date_from",
                    variant: "outlined",
                    inputProps: {
                      'data-cy': "provider_effectivity_date_from"
                    }
                  }
                }}
              />
            </LocalizationProvider>
          </div>
          <div className="vni-w-48 date-holder">
            <label style={{ opacity: 1 }} htmlFor="">
              Expiry Date
            </label>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                format="MM/dd/yyyy"
                value={expiryDate}
                onChange={(e: any) => {
                  setExpiryDate(new Date(e).toLocaleDateString());
                }}
                disabled={agent === "none" ? true : false}
                slotProps={{
                  textField: {
                    id: "isProviderEfectivityDateFromValid",
                    className: "CustomInput vni-datepicker create-date-fix",
                    name: "provider_effectivity_date_from",
                    variant: "outlined",
                    inputProps: {
                      'data-cy': "provider_effectivity_date_from"
                    }
                  }
                }}
              />
            </LocalizationProvider>
          </div>
          <span className="vni-flex">
          <label
              data-cy="appt-upload"
              htmlFor="apt-upload"
              className="CustomPrimaryButton vni-mr-5"
            >
              <i className="fas fa-file-upload vni-text-lg vni-mr-1"></i>
              Upload Files
            </label>
            <button
              name="supporting_docs"
              id="apt-upload"
              className="vni-hidden"
              onClick={open}
            />
            <Button
              data-cy="clear-queue"
              className="CustomPrimaryOulineButton scarlet"
              onClick={() => {
                // clear store
                dispatch(clearAppointmentLetter());
                setRejectedFiles([]);
                setExistingFile([]);
              }}
              disabled={
                aLetter.length > 0 || rejectedFiles.length > 0
                  ? false
                  : true || existingFile.length > 0
                  ? false
                  : true
              }
            >
              Clear File
            </Button>
          </span>
        </div>

        <div
          {...getRootProps({ className: "upload-box vni-text-center" })}
          ref={dropzoneRef}
        >
          <input data-cy="appt-upload" {...getInputProps()} />
          {existingFile.map((file, i) => {
            return (
              <UploadFileItem
                objectStatus="file-item"
                key={i}
                name={file.file_name}
                icon={changeFileIcon(file.file_type)}
              />
            );
          })}
          {aLetter.map((file, i) => {
            return (
              <UploadFileItem
                objectStatus="file-item"
                key={i}
                name={file.name}
                icon={changeFileIcon(file.type)}
              />
            );
          })}
          {rejectedFiles.map((file, i) => {
            return (
              <UploadFileItem
                objectStatus="file-item error"
                id={file.lastModified + file.name}
                icon={changeFileIcon(file.type)}
                name={file.name}
                key={i}
                // handleClick={(e) => handleFileSelect(file, e)}
                errorClass="error-desc"
                errorMessage={handleFileErrorMessage(file)}
              />
            );
          })}
          {aLetter.length === 0 &&
            rejectedFiles.length === 0 &&
            existingFile.length === 0 && (
              <div className="vni-self-center vni-text-center vni-w-full">
                <i className="fas fa-file-upload vni-text-xl vni-mb-2 vni-block"></i>
                Drag or Drop file to upload appointment letter.
              </div>
            )}
        </div>

        <p>Maximum upload file size is 1GB</p>
      </div>
      <Modal
        fullWidth={false}
        maxWidth="md"
        open={openErrModal}
        onClose={handleCloseErrModal}
        
      >
        <Grid>
          <Typography variant="h5" className={classes.modalTitle}>
            Appointment letter already added
          </Typography>
          <Typography
            className={classes.modalMessage}
            style={{ maxWidth: 500 }}
            paragraph={true}
          >
            Only one appointment letter is allowed to be uploaded. If you wish
            to upload a new letter, please delete the existing letter and upload
            a new one.
          </Typography>
          <Grid className="btn-group" style={{ textAlign: "center" }}>
            <Button
              data-cy="Okay"
              className={classes.rightButton}
              onClick={handleCloseErrModal}
            >
              Okay
            </Button>
          </Grid>
        </Grid>
      </Modal>
    </>
  );
}
