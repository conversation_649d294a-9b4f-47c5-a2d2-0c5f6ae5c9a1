import React, { useState, useEffect } from "react";
import { Franchise } from "../../shared/models/Franchise";
import UploadFileItem from "./UploadFileItem";
import { useDropzone } from "react-dropzone";
import { useDispatch, useSelector } from "react-redux";
import {
  uploadDocument,
  setUserInputValues,
} from "../../shared/reducers/FranchiseSlice";
import { RootState } from "../../shared/reducers/rootReducer";
import { Button, FormControlLabel, Checkbox } from "@mui/material";
import UploadAppointmentLetter from "./UploadAppointmentLetter";

import { fileTypes, TOTAL_MAX_SIZE } from "../../utils/Environment";
// import { getFranchisingRole } from "../../utils/DataHelper";

interface Props {
  handleUploadDocument: (e: any) => any;
  handleClearFileUpload: (e: any) => any;
  franchise: Franchise;
  uploaded?: File[];
}

const UploadFranchiseDocuments: React.FC<Props> = (props) => {
  const { getRootProps, getInputProps, open } = useDropzone({
    accept: fileTypes,
    onDrop: (event) => handleFileInput(event),
    onDropRejected: (rejectedFiles) => handleRejectedFiles(rejectedFiles),
    getFilesFromEvent: (event) => handleTotalMaxFileSize(event),
    noClick: true,
    minSize: 0,
    maxSize: TOTAL_MAX_SIZE,
  });
  const { isSalesUser = false } = useSelector((state: RootState) => state.login.userRoles);
  const [fileValues, setValues] = useState([] as any[]);
  const [excludeFiles, setExclude] = useState([] as any[]);
  const [rejectedValues, setRejectedFiles] = useState([] as any[]);
  const [failedFiles, setFailedFiles] = useState([] as any[]);
  const [appointmentLetter, setAppointmentLetter] = useState(false);
  const { franchise, handleClearFileUpload, handleUploadDocument } = props;

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  useEffect(() => {
    if (franchise) {
      if (franchise?.appointment_letter?.length > 0) {
        setAppointmentLetter(true);
      }

      if (franchise?.supporting_docs_obj) {
        setValues(franchise.supporting_docs_obj);
      }
    }
  }, [franchise]);

  async function handleTotalMaxFileSize(event: any) {
    const files = [];
    const fileList = event.dataTransfer
      ? event.dataTransfer.files
      : event.target.files;
    let acceptedTotalSize = 0;
    for (var i = 0; i < fileValues.length; i++) {
      const file = fileValues[i];
      if (file.main_role && isSalesUser) {
        acceptedTotalSize += file.file_size;
      }
    }
    for (var j = 0; j < fileList.length; j++) {
      const file = fileList.item(j);
      acceptedTotalSize += file.size;
      Object.defineProperty(file, "fileExceed", {
        value: acceptedTotalSize > TOTAL_MAX_SIZE,
      });
      files.push(file);
    }
    return files;
  }
  const dispatch = useDispatch();
  const handleFileInput = (files: any) => {
    // delcare new files array
    const newFiles = [];
    const maxFiles = [];
    // Loop first the files. and sum all its sizes.
    console.log("filessss", files);
    for (let index = 0; index < files.length; index++) {
      const file = files[index];
      if (file.fileExceed) {
        // add duplicate condition.
        maxFiles.push(file);
      } else {
        newFiles.push(file);
      }
    }

    if (maxFiles.length > 0) {
      setRejectedFiles([...rejectedValues, ...maxFiles]);
    }

    console.log("REMAINING FILES TO BE UPLOADED ==> ", newFiles);

    for (let index = 0; index < newFiles.length; index++) {
      dispatch(uploadDocument(newFiles[index], "encoder"));
    }
  };
  const handleRejectedFiles = (files: any) => {
    setRejectedFiles(files);
  };

  const handleFileInputClear = () => {
    let data = [] as any[];
    if (excludeFiles.length > 0) {
      data = fileValues.filter((el) => !excludeFiles.includes(el));
      handleClearFileUpload(data);
      data = rejectedValues.filter((el) => !excludeFiles.includes(el));
      setRejectedFiles(data);
      data = failedFiles.filter((el) => !excludeFiles.includes(el));
      setFailedFiles(data);
      //@ts-ignore
      dispatch(setUserInputValues(true));
    } else {
      setValues([]);
      setRejectedFiles([]);
      setFailedFiles([]);
      handleClearFileUpload([]);
      //@ts-ignore
      dispatch(setUserInputValues(true));
    }
    setExclude([]);
  };

  const handleFileSelect = (id: any, e: any) => {
    const index = excludeFiles.indexOf(id);
    if (index < 0 && e.target.checked) {
      setExclude([...[id], ...excludeFiles]);
    } else if (index > -1 && !e.target.checked) {
      excludeFiles.splice(index, 1);
      setExclude(excludeFiles);
    }
  };

  const handleFileErrorMessage = (file: File) => {
    if (fileTypes.indexOf(file.type) < 0) {
      return "File type not supported.";
    } else if (file.size > TOTAL_MAX_SIZE) {
      return "File size exceeds 1GB.";
    } else if (file.hasOwnProperty("fileExceed")) {
      return "Total file size exceeds 1GB.";
    }
    return "File not supported.";
  };

  const fileUploading = useSelector(
    (state: RootState) => state.franchise.uploadingFile
  );
  const fileUploaded = useSelector(
    (state: RootState) => state.franchise.uploadedFile
  );
  const rejectedFile = useSelector(
    (state: RootState) => state.franchise.rejectedFile
  );

  useEffect(() => {
    if (
      Object.keys(fileUploaded).length > 0 &&
      fileUploaded.constructor === Object
    ) {
      handleUploadDocument([fileUploaded]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fileUploaded]);

  useEffect(() => {
    if (
      Object.keys(rejectedFile).length > 0 &&
      rejectedFile.constructor === Object
    ) {
      setFailedFiles([...failedFiles, rejectedFile]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rejectedFile]);

  const files = fileValues.map(
    (file, i) =>
      file.main_role && isSalesUser && (
        <UploadFileItem
          objectStatus="file-item"
          id={file._id}
          icon={changeFileIcon(file.file_type)}
          name={file.file_name}
          key={i}
          handleClick={(e) => handleFileSelect(file, e)}
        />
      )
  );

  const rejectFiles = rejectedValues.map((file, i) => (
    <UploadFileItem
      objectStatus="file-item error"
      id={file.lastModified + file.name}
      icon={changeFileIcon(file.type)}
      name={file.name}
      key={i}
      handleClick={(e) => handleFileSelect(file, e)}
      errorClass="error-desc"
      errorMessage={handleFileErrorMessage(file)}
    />
  ));

  const errorFiles = failedFiles.map((file, i) => (
    <UploadFileItem
      objectStatus="file-item error"
      id={file.lastModified + file.name}
      icon={changeFileIcon(file.type)}
      name={file.name}
      key={i}
      handleClick={(e) => handleFileSelect(file, e)}
      errorClass="error-desc"
      errorMessage={file.message}
    />
  ));

  const handleAppointmentLetter = () =>
    setAppointmentLetter((prevState) => !prevState);

  const fileExist =
    errorFiles.length < 1 && rejectFiles.length < 1 && files.length < 1;

  return (
    <>
      <div className="main vni-py-8 vni-px-12" id="upload_documents">
        <div className="vni-flex vni-justify-between vni-items-center">
          <h3 className="vni-block vni-text-lg vni-font-bold">
            Upload Supporting Documents
          </h3>
          <span className="vni-flex">
            <label
              data-cy="upload-item"
              htmlFor="file-upload"
              className="CustomPrimaryButton vni-mr-5"
            >
              <i className="fas fa-file-upload vni-text-lg vni-mr-1"></i>
              Upload Files
            </label>
            <button
              name="supporting_docs"
              id="file-upload"
              className="vni-hidden"
              onClick={open}
            />
            <Button
              data-cy="clear-queue"
              className="CustomPrimaryOulineButton scarlet"
              disabled={fileExist}
              onClick={handleFileInputClear}
            >
              Clear Queue
            </Button>
          </span>
        </div>
        <div {...getRootProps({ className: "upload-box vni-text-center" })}>
          <input data-cy="upload-item" {...getInputProps()} />
          {Object.keys(fileUploading).length > 0 &&
          fileUploading.constructor === Object ? (
            <UploadFileItem
              objectStatus="file-item"
              icon={changeFileIcon(fileUploading.file_type)}
              loading="loading"
              progress="progress"
              percentage={fileUploading.percentCompleted}
            />
          ) : null}
          {files.length ? files : null}
          {rejectFiles.length ? rejectFiles : null}
          {errorFiles.length ? errorFiles : null}
          {fileExist && Object.keys(fileUploading).length < 1 && (
            <div className="vni-self-center vni-text-center vni-w-full">
              <i className="fas fa-file-upload vni-text-xl vni-mb-2 vni-block"></i>
              Drag or Drop file to upload supporting document.
            </div>
          )}
        </div>
        <p>Maximum upload file size is 1GB</p>
      </div>
      <div className="vni-py-1 vni-px-12">
        <FormControlLabel
          control={
            <Checkbox
              checked={appointmentLetter}
              onClick={handleAppointmentLetter}
            />
          }
          label="Appointment Letter"
        />
      </div>
      {appointmentLetter && <UploadAppointmentLetter franchise={franchise} />}
    </>
  );
};

export const changeFileIcon = (type: string) => {
  const fileTypeIcons = [
    // 'far fa-file-video',
    "far fa-file-word",
    "far fa-file-excel",
    "far fa-file-excel",
    "far fa-file-pdf",
    "far fa-file-excel",
    "far fa-file-image",
    "far fa-file-image",
    "far fa-file-word",
  ];
  for (let index = 0; index < fileTypes.length; index++) {
    const fileType = fileTypes[index];
    if (type === fileType) {
      return fileTypeIcons[index];
    }
  }
  return "far fa-file";
};

export default UploadFranchiseDocuments;
