import React from "react";
import { Box, Typography, Select, TextField, Grid, Dialog, DialogTitle, DialogContent, DialogActions } from "@mui/material";
import {
  LocalizationProvider,
  DatePicker,
} from "@mui/x-date-pickers";
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  changeFilter,
  setIsSearching,
} from "../../../shared/reducers/FranchiseSlice";

import { RootState } from "../../../shared/reducers/rootReducer";

import FranchiseList from "../../../shared/components/FranchiseList";

import { useDispatch, useSelector } from "react-redux";
import { apiURL } from "../../../utils/Environment";
import { logout } from "../../../shared/reducers/LoginSlice";
const ManagerFranchise = (props: any) => {
  const { canSearchFR = false, isMultipleRoles = false } = props;
  const dispatch = useDispatch();
  const token = useSelector((state: RootState) => state.login.token);

  // redux
  const franchiseRequests = useSelector(
    (state: RootState) => state.franchise.franchiseList
  );

  //states
  const [searchValue, setSearchValue] = React.useState<string>("");
  const [searchResult, setSearchResult] = React.useState([]);
  // eslint-disable-next-line
  const [selection, setSelection] = React.useState<any[]>([]);
  const [date_from, setDateFrom] = React.useState<any>(null);
  const [date_to, setDateTo] = React.useState<any>(null);
  const [applyDateFilter, setApplyDateFilter] = React.useState<any>(false);
  const [viewExpiredModal, setViewExpiredModal] = React.useState(false);

  const handleSearch = (e: any) => {
    e.preventDefault();

    function filterBy(item: any) {
      let corpoName = item.corporate_name.toLowerCase();
      let brandName = item.brand_name.toLowerCase();
      let franchiseeName = item.applicant_name.toLowerCase();

      let newSearchValue = searchValue;
      let values = newSearchValue.toLowerCase();

      if (
        corpoName.includes(values) ||
        brandName.includes(values) ||
        item.tax_number.includes(searchValue) ||
        franchiseeName.includes(values)
      ) {
        return true;
      }
      return false;
    }

    let item = [];
    if (searchValue) {
      dispatch(setIsSearching(true));
      item = franchiseRequests.filter(filterBy);
      setSearchResult(item);
    } else {
      dispatch(setIsSearching(false));
    }
  };

  const handleSearchTextbox = (e: any) => {
    const { value } = e.target;
    setSearchValue(value);
    dispatch(setIsSearching(false));
  };

  const handleDateFromChange = (e: any) => {
    setDateFrom(e);
    if (e && date_to) {
      setApplyDateFilter(true);
    } else {
      setApplyDateFilter(false);
    }
  };

  const handleDateToChange = (e: any) => {
    setDateTo(e);
    if (e && date_from) {
      setApplyDateFilter(true);
    } else {
      setApplyDateFilter(false);
    }
  };

  let filters: string[] = [
    "All",
    "For Approval",
    "Approval In Process",
    "Approved",
    "Disapproved",
    "Expiring Franchise",
    "Expired Franchise",
  ];
  let filterName: string;

  const handleSelectTile = (e: any) => {
    filterName = e.target.value;

    dispatch(
      changeFilter({
        filterIndex: filters.indexOf(filterName),
        name: filterName,
      })
    );
  };

  const filterOptions = filters.map((filter, index) => (
    <option
      data-cy={`select-status-option-${index}`}
      key={index}
      value={filter}
    >
      {filter}
    </option>
  ));

  const checkToken = (accessToken: any) => {
    let URL = apiURL.userManagement + '/underwriting/user/check'

    return fetch(URL, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + accessToken,
        }}).then(async (res: any) => {
            if (!res.ok) {
                let respjson = await res.json()
                let errormessage = respjson.error.message
                throw Error(errormessage)
            }
            return res ? res.json() : null
        })
  }

  const isTokenValid = async () => {
    let isValid = true,
    isPmValid = true,
    isApiValid = true;
    
    if (!localStorage.getItem('api_token') || !localStorage.getItem('pm_token')) {
      isValid = false;
    } else if (
      localStorage.getItem('api_token') &&
      localStorage.getItem('pm_token')
    ) {
      try {
        let api_token = await checkToken(localStorage.getItem('api_token'));
        let pm_token = await checkToken(localStorage.getItem('pm_token'));
        if (!api_token.status) {
          isApiValid = false;
        }
        if (!pm_token.status) {
          isPmValid = false;
        }
      } catch (error) {
        console.log(error, 'error');
        isPmValid = false;
        isApiValid = false;
      }
    }

    console.log('TOKEN VALIDITY', isValid, isPmValid, isApiValid);
    if (!isValid || !isPmValid || !isApiValid) {
      // console.log('INVALID TOKEN');
      // var a = '../customer-care/index.html#/franchising' + '/invalidSession';
      // window.location.href = a;
      //onLogout();
      setViewExpiredModal(true);
    }
  };

  React.useEffect(() => {
    isTokenValid()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token])

  return (
    <React.Fragment>
      <h1 className="vni-font-bold vni-text-3xl">
        Franchise Request{`${isMultipleRoles ? " (Manager)" : ""}`}
      </h1>
      {/* Search */}
      {/* TODO - transform to resusable component */}
      {canSearchFR && (
        <Box className="vni-search">
          <form className="vni-flex vni-w-2/3" onSubmit={handleSearch}>
            <Box className="add-icon vni-flex-grow">
              <TextField
                data-cy="search-franchise"
                className={"w-100"}
                value={searchValue}
                variant="outlined"
                onChange={handleSearchTextbox}
              />

              <small className="vni-absolute vni-mt-1 vni-italic vni-text-gray-500 vni-block">
                Search for a Registered Corporate Name, Trade/Brand Name, TIN or
                a Franchisee Name.
              </small>
            </Box>
            <button
              type="submit"
              data-cy="search-franchise-btn"
              className="CustomPrimaryButton"
              disabled={searchValue === ""}
            >
              Search
            </button>
          </form>
        </Box>
      )}

      <Box className="vni-my-5"></Box>
      <Box
        className="vni-flex vni-items-center"
        style={{ paddingBottom: "1rem" }}
      >
        <Box className="vni-flex vni-flex-grow" />

        <Grid item xs={12} style={{ textAlign: "right" }}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              width: "100%",
              justifyContent: "flex-end",
              paddingRight: "0.5rem",
            }}
          >
            <Typography
              className="CustomLabel vni-inline-block"
              component="p"
              style={{
                marginLeft: "0.5rem",
                marginRight: "0.5rem",
              }}
            >
              Select Date from
            </Typography>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                className="vni-date-picker"
                format="MM/dd/yyyy"
                maxDate={date_to ?? undefined}
                value={date_from}
                onChange={handleDateFromChange}
                slotProps={{
                  textField: {
                    id: "franchise_reports_date_from",
                    placeholder: "mm/dd/yyyy",
                    variant: "outlined",
                    inputProps: {
                      'data-cy': "franchise_reports_date_from"
                    }
                  }
                }}
              />
            </LocalizationProvider>
            <Typography
              className="CustomLabel vni-inline-block"
              component="p"
              style={{
                marginLeft: "0.5rem",
                marginRight: "0.5rem",
              }}
            >
              to
            </Typography>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                className="vni-date-picker"
                format="MM/dd/yyyy"
                value={date_to}
                minDate={date_from ?? undefined}
                maxDate={new Date()}
                onChange={handleDateToChange}
                slotProps={{
                  textField: {
                    id: "franchise_reports_date_to",
                    placeholder: "mm/dd/yyyy",
                    variant: "outlined",
                    inputProps: {
                      'data-cy': "franchise_reports_date_to"
                    }
                  }
                }}
              />
            </LocalizationProvider>
          </div>
        </Grid>

        <Box className="vni-flex vni-self-end vni-items-center vni-w-64">
          <Typography
            className="CustomLabel vni-inline-block vni-w-32"
            component="p"
          >
            Filter by:
          </Typography>
          <Select
            native
            inputProps={{
              name: "status",
              id: "requestStatus",
              "data-cy": "select-status",
            }}
            onChange={handleSelectTile}
            style={{ width: "22rem" }}
          >
            {filterOptions}
          </Select>
        </Box>
      </Box>
      <Box className="vni-table-container">
        <FranchiseList
          searchResult={searchResult}
          result={searchValue}
          setBulkSelect={setSelection}
          applyFilter={applyDateFilter}
          dateFrom={date_from}
          dateTo={date_to}
          userRole="manager"
          isMultipleRole={isMultipleRoles}
        />
      </Box>


      {viewExpiredModal === true &&(
        <Dialog 
            id="session-modal-expired"
            maxWidth='xs'
            open={viewExpiredModal}
            onClose={() => {
            dispatch(logout())
            window.location.replace('../index.html#/')
            }}
            className="vni-m-auto"
        >
            <DialogTitle className="vni-px-5 vni-pt-3 vni-mb-8">
              <h2 className="title">Session Expired</h2>
            </DialogTitle>
            <DialogContent>
              <p className="vni-mb-8">
              Your account has been logged out. Please try logging in again.
              </p>
            </DialogContent>
            <DialogActions className="vni-flex d-flex-center">
              <button className="CustomPrimaryButton vni-mr-5" data-cy="session-timeout-logout-btn" onClick={() => {
                  dispatch(logout())
                  window.location.replace('../index.html#/')

                  }}>Okay</button>
            </DialogActions>
        </Dialog>
        )};
    </React.Fragment>
  );
  //   return (
  //     <div>ManagerFranchise</div>
  //   )
};

export default ManagerFranchise;
