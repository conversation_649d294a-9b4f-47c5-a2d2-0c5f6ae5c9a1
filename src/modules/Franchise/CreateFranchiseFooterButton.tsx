import React, {useEffect, useState} from "react";
import { Box } from "@mui/material";
import { RootState } from "../../shared/reducers/rootReducer"
import { useSelector } from 'react-redux';

interface Props {
  handleButton: (e: any, action: string) => any;
  tab: number;
}

const CreateFranchiseFooterButton: React.FC<Props> = (props) => {
  const currentSavedData = useSelector((state: RootState) => state.franchise.new);
  const isEdit = useSelector((state: RootState) => state.franchise.isEdit);
  const {canSubmitFR,canUploadDocs} = useSelector((state: RootState) => state.login.userPermissions);
  const [saveForNow , setSaveForNow] = useState<any>();
  const {handleButton,tab} = props

  let submitText: any;
  if (Object.keys(currentSavedData).length > 0 && (currentSavedData.status === "SUBMITTED" || currentSavedData.status === "RESUBMITTED")){
    submitText = tab === 2 || (!canUploadDocs && tab===1) ? "Update":"Next";
  }else{
    submitText = tab === 2 || (!canUploadDocs && tab===1) ? "Submit":"Next";
  }

  useEffect(()=>{
    if(currentSavedData.status === "SUBMITTED" || currentSavedData.status === "RESUBMITTED"){
      setSaveForNow(<></>)
    }else{
      
      setSaveForNow(
        <a 
          href="#!"
          onClick={(e) => handleButton(e, "save")}
        >
          <button
            data-cy="save-for-now"
            className="CustomPrimaryOulineButton green"
          >
            Save for Now
          </button>
        </a>
      )
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  },[currentSavedData])

 
  return (
    // <!-- Footer Button Container -->
    <Box className="kyc-footer-btns vni-mt-8">
      <Box component="span" className="vni-flex">
      <a data-cy="cancel-btn" href="#!" onClick={(e) => handleButton(e, isEdit? "Cancel Edit" :"Cancel Create")}>
          <button className="CustomPrimaryButton bluewood">{isEdit? "Cancel Edit" :"Cancel Create"}</button>
      </a>
      {saveForNow}
      </Box>
      <Box component="span" className="vni-flex">
        {
          tab > 0 &&  <a data-cy="back-btn" href="#!" onClick={(e) => handleButton(e, "back")}>
                    <button className="CustomPrimaryOulineButton green">Back</button>
                  </a>
        }
        {
          submitText !== "Submit" ||  canSubmitFR ?
          <a href="#!" onClick={(e) => handleButton(e, submitText)}>
            <button data-cy={tab === 2 || (!canUploadDocs && tab===1) ? "submit-btn":"next-btn"} className="CustomPrimaryButton">{submitText}</button>
          </a>
          :
          null
        }
      </Box>
    </Box>
  );
};
export default CreateFranchiseFooterButton;
