import React, { useState, useEffect } from "react"
import { useHistory } from "react-router-dom"
import { useDispatch, useSelector } from "react-redux"
import {
	<PERSON>rid,
	Typo<PERSON>,
	Button,
} from "@mui/material"
import {
	setModalContent,
	fetchContact,
} from "../../../../shared/reducers/ContactSlice"
import { Main } from "./Components/Main"
import { RootState } from "../../../../shared/reducers/rootReducer"
import { viewContactStyles } from "./style"
import { Modal } from "../../../../shared/components/Modal"

interface IProps {
	handleClickButton: (e: any, action: string) => any
}

export const ViewContact: React.FC<IProps> = (props: IProps) => {
	const classes = viewContactStyles()
	const dispatch = useDispatch()
	const history = useHistory()

	const { handleClickButton } = props

	const contact = useSelector((state: RootState) => state.contact.details)
	const validationModalContent = useSelector(
		(state: RootState) => state.contact.validationModalContent,
	)

	const [openErrorModal, setOpenErrorModal] = useState(false)

	useEffect(() => {
		if (validationModalContent.error) {
			setOpenErrorModal(true)
		} else {
			setOpenErrorModal(false)
		}
	}, [validationModalContent.error])

	const handleEdit = () => {
		history.push(`/franchising/contact/edit/${contact._id}`);
	}

	const handleCloseErrorModal = () => {
		dispatch(fetchContact(contact._id))
		setOpenErrorModal(false)
		dispatch(setModalContent({ title: "", p: "", error: false }))
	}

	return (
		<>
			<Modal
				fullWidth={false}
				maxWidth="sm"
				open={openErrorModal}
				onClose={handleCloseErrorModal}
			>
				<Grid>
					<Typography
						className={classes.modalTitle}
						style={{ marginBottom: 20 }}
					>
						{validationModalContent.title}
					</Typography>
					<Typography className={classes.modalText}>
						{validationModalContent.p}
					</Typography>
					<Grid className="btn-group" style={{ textAlign: "center" }}>
						<Button
							data-cy="okay-btn"
							style={{ marginTop: 20 }}
							className={classes.rightButton}
							onClick={handleCloseErrorModal}
						>
							Okay
						</Button>
					</Grid>
				</Grid>
			</Modal>
			<Grid container className="vni-flex">
				<Grid item xs={2}>
					<Typography className={classes.title}>
						{contact.code}
					</Typography>
				</Grid>					
			</Grid>
			<Grid container>				
				<Grid className={classes.rightSide} item xs={9}>
					<Main
						classes={classes}
						contactDetails={contact}
					/>
				</Grid>
			</Grid>
			<Grid className={classes.buttonGroup} justifyContent="flex-end" container>
				<Grid item xs={12} style={{ textAlign: "right" }}>
					{contact.status && contact.status === "Active" ? (
						<Button
							data-cy="deactivate-btn"
							className={classes.leftButton}
							onClick={(e) => handleClickButton(e, "deactivate")}
						>
							Deactivate
						</Button>
					) : null}
					{contact.status && contact.status === "Inactive" ? (
						<Button
							data-cy="activate-btn"
							className={classes.leftButton}
							onClick={(e) => handleClickButton(e, "activate")}
						>
							Activate
						</Button>
					) : null}					
					<Button
						data-cy="edit-btn"
						className={classes.rightButton}
						onClick={handleEdit}
					>
						Edit
					</Button>
				</Grid>
			</Grid>
		</>
	)
}
