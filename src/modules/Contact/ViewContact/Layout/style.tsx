import { makeStyles } from '@mui/styles'
import { Theme } from '@mui/material/styles'

const viewContactStyles = makeStyles((theme: Theme) => ({
    title: {
        fontSize: 30,
        fontWeight: 800,
        fontFamily: 'usual',
        color: '#272E4C'
    },
    label: {
        textTransform: 'uppercase',
        color: '#7D8294',
        fontSize: 12,
        fontFamily: 'usual'
    },
    value: {
        color: '#272E4C',
        fontWeight: 600,
        fontSize: 16,
        fontFamily: 'usual'
    },
    rightSide: {
        maxWidth: '100%',
        flexBasis: '100%',
        fontFamily: 'usual'
    },
    divider: {
        marginBottom: '24px !important'
    },
    buttonGroup: {
        marginTop: theme.spacing(2)
    },
    leftButton: {
        background: '#313C53',
        color: '#FFFFFF',
        minWidth: 140,
        marginRight: theme.spacing(2),
        '&:hover': {
            background: '#313C53'
        }
    },
    rightButton: {
        background: '#3AB77D',
        color: '#FFFFFF',
        minWidth: 140,
        '&:hover': {
            background: '#3AB77D'
        }
    },
    modalTitle: {
        color: '#272E4C',
        fontWeight: 800,
        fontSize: 18,
        fontFamily: 'usual'
    },
    modalText: {
        color: '#272E4C',
        fontWeight: 400,
        fontSize: 14,
        fontFamily: 'usual'
    }
}))

export {
    viewContactStyles
}