import React from 'react'
import {
    Typography, Grid, 
    Divider,
} from '@mui/material'
import { Contact } from '../../../../../shared/models/Contact'
 
interface IProps {
    classes?: any,
    contactDetails: Contact,
}

export const Main: React.FC<IProps> = (props: IProps)=> {
    const {
       classes,
       contactDetails,
    } = props

    return (
        <>
			{contactDetails && (
				<div className="main vni-py-6">
					<div className="vni-block">
						<Divider className={classes.divider} />
						<Grid className="vni-flex">
							<Grid container className="vni-flex">
								<Grid container className="vni-flex vni-mb-8">
									<Grid item xs={3}>
										<Typography className={classes.label}>Code</Typography>
										<Typography className={classes.value}>{contactDetails.code}</Typography>
									</Grid>
									<Grid item xs={3}>
										<Typography className={classes.label}>Type</Typography>
										<Typography className={classes.value}>{contactDetails.type}</Typography>
									</Grid>
									<Grid item xs={3}>
										<Typography className={classes.label}>Employee Code</Typography>
										<Typography className={classes.value}>{contactDetails.employee_code || "None"}</Typography>
									</Grid>
									<Grid item xs={3}>
										<Typography className={classes.label}>Status</Typography>
										<Typography className={classes.value}>{contactDetails.status}</Typography>
									</Grid>								
								</Grid>
								<Grid container className="vni-flex vni-mb-8">								
									<Grid item xs={6}>
										<Typography className={classes.label}>Name</Typography>
										<Typography className={classes.value}>{contactDetails.name}</Typography>
									</Grid>
									<Grid item xs={6}>
										<Typography className={classes.label}>Contact Person</Typography>
										<Typography className={classes.value}>{contactDetails.contact_person}</Typography>
									</Grid>
								</Grid>
								<Grid container className="vni-flex vni-mb-8">								
									<Grid item xs={6}>
										<Typography className={classes.label}>Contact Number</Typography>
										<Typography className={classes.value}>{contactDetails.contact_number}</Typography>
									</Grid>
									<Grid item xs={6}>
										<Typography className={classes.label}>Email Address</Typography>
										<Typography className={classes.value}>{contactDetails.email_address}</Typography>
									</Grid>
								</Grid>
								<Grid container className="vni-flex vni-mb-8">
									<Grid item xs={3}>
										<Typography className={classes.label}>Area</Typography>
										<Typography className={classes.value}>{contactDetails.area}</Typography>
									</Grid>
									<Grid item xs={3}>
										<Typography className={classes.label}>Tax Type</Typography>
										<Typography className={classes.value}>{contactDetails.tax_type}</Typography>
									</Grid>
									<Grid item xs={3}>
										<Typography className={classes.label}>Tax Identification Number</Typography>
										<Typography className={classes.value}>{contactDetails.tin}</Typography>
									</Grid>
								</Grid>
								<Grid container className="vni-flex vni-mb-8">
									<Grid item xs={6}>
										<Typography className={classes.label}>EVAT Number</Typography>
										<Typography className={classes.value}>{contactDetails.evat_number || "None"}</Typography>
									</Grid>
									<Grid item xs={6}>
										<Typography className={classes.label}>Input Tax</Typography>
										<Typography className={classes.value}>{contactDetails.input_tax || "None"}</Typography>
									</Grid>
								</Grid>
								<Grid container className="vni-flex vni-mb-8">
									<Grid item xs={6}>
										<Typography className={classes.label}>MDPA Identifier</Typography>
										<Typography className={classes.value}>{contactDetails.mdpa_id || "None"}</Typography>
									</Grid>
								</Grid>
								<Grid container className="vni-flex vni-mb-4">								
									<Grid item xs={12}>
										<Typography className={classes.label}>Address</Typography>
										<Typography className={classes.value}>
											{contactDetails.business_address ? 
												(
													`${contactDetails.business_address.unit ? contactDetails.business_address.unit+", " : ""}
													${contactDetails.business_address.floor ? contactDetails.business_address.floor+", " : ""}
													${contactDetails.business_address.bldg_name}, 
													${contactDetails.business_address.street}, 
													${contactDetails.business_address.brgy}, 
													${contactDetails.business_address.city}, 
													${contactDetails.business_address.province}, 
													${contactDetails.business_address.zip_code ? contactDetails.business_address.country+", " : contactDetails.business_address.country}
													${contactDetails.business_address.zip_code ? contactDetails.business_address.zip_code : ""}`
												) : ""}
										</Typography>
									</Grid>
								</Grid>
							</Grid>
						</Grid>
					</div>		
				</div>
			)}
		</>
    )
}