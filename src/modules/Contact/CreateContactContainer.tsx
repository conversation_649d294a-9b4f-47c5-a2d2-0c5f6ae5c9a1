import React, { useState, useEffect, useRef } from 'react'
import { useHistory, useLocation } from 'react-router-dom'
import { useSelector, useDispatch } from "react-redux"
import {  Grid, Typography,  But<PERSON> } from '@mui/material'
import { 
        emptyInputValidStatus,
        InputValidStatus,
        IErrorNameTypeObj
    } from '../../shared/models/Contact'
import { 
        setContactData,
        addContact,
        editContact,
        resetState, 
        goBackAndCreateNew, 
        clearValidationModalContent, 
        setUserInputValues, 
        setIsCreate,
        patchContactRequest,
    } from '../../shared/reducers/ContactSlice'
import CreateContactFooterButton from './CreateContactFooterButton'
import CreateEditContactRequest from './CreateEditContactRequest'
import { Modal } from '../../shared/components/Modal'
import { RootState } from "../../shared/reducers/rootReducer"
import { PreviewStyles } from '../../shared/components/ViewPDFReport/style'
import { validEmailRegex } from '../../utils/StringHelper'

const scrollToRef = (ref:any) =>{ if(ref.current.offsetTop){ window.scrollTo(0, ref.current.offsetTop)}  }

const CreateContactContainer = (props: any) => {
    const classes = PreviewStyles()
    const myRef = useRef(null)
    const { match } = props;
    let { id } = match.params;
    const history = useHistory();
    const dispatch = useDispatch();
    let location = useLocation();
    
    const newContact = useSelector((state: RootState) => state.contact.new);    
    const validationContent = useSelector((state: RootState) => state.contact.validationModalContent);
    const {canAddFR} = useSelector((state: RootState) => state.login.userPermissions);
    const [inputStatus, setInputStatus] = useState(emptyInputValidStatus);
    const isEditLoading = useSelector((state: RootState) => state.contact.isEditLoading);
    const isEdit = useSelector((state: RootState) => state.contact.isEdit);
    const isCreate = useSelector((state: RootState) => state.contact.isCreate);
    const [values, setValues] = useState(newContact);
    const [openModal, setOpenModal] = React.useState<any>(false)
    const [modalContent, setModalContent] = React.useState<any>({
        title: "",
        description:"",
        button:<></>
    });
    const {title,description,remarks,button} =  modalContent;
    const isRequesting = useSelector((state: RootState) => state.contact.isRequesting)
    const [isValidated, setIsValidated] = React.useState<any>(false);
    const [buttonClick, setButtonClick] = React.useState<any>(false);
    const [buttonAction, setButtonAction] = React.useState<any>(false);

    const validObject = { 
        valid: true, 
        inputClass: "", 
        spanClass: "", 
        text: "" 
    };

    const invalidObject = {
        valid: false,
        inputClass: "error",
        spanClass: "vni-text-red-500 vni-block vni-py-1",
        text: "This field is required."
    }
    
    useEffect(() => {
        dispatch(clearValidationModalContent());
        window.scrollTo(0, 0);
        if(location.pathname === "/franchising/contact/create"){
            dispatch(setIsCreate(true))
            window.scrollTo(0, 0);
        }else{
            dispatch(setIsCreate(false))
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [location])

    useEffect(() => {
        if(isCreate){
            window.scrollTo(0, 0);
        }
    }, [isCreate])

    useEffect(() => {
        const { p, title} = validationContent
        if (p && title) {
            setModalContent(
                {
                    title,
                    description:p,
                    button: <>
                                <Button
                                    data-cy="back-to-dashboard"
                                    className={classes.leftButtonOutline}
                                    onClick={(e) => handleConfirmButtonClick(e,"back")}
                                >
                                    Back to Dashboard
                                </Button>
                                {
                                    (isEdit || isCreate) && canAddFR ?
                                        <Button
                                            data-cy="create-new"
                                            className={classes.rightButton}
                                            onClick={(e) => handleConfirmButtonClick(e,"new")}
                                        >
                                            Create New
                                        </Button>
                                    : null
                                }                                
                            </>
                }
            )
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [validationContent])

    useEffect(() => {
        if (!isEditLoading && !isEdit && id) {
            dispatch(editContact(id))
            window.scrollTo(0, 0);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [location, id, isEdit, isEditLoading])

    useEffect(() => {
        setValues(newContact);
        if (!isEdit) setCodeValue("BRK1234567");
        if (isEdit) {
            const entries = Object.entries(inputStatus);
            let combinedInputStatus = {} as InputValidStatus;
            for (let index = 0; index < entries.length; index++) {
                const key = entries[index][0];
                const objIError = entries[index][1];
                if(objIError.constructor !== Array){
                    Object.assign(combinedInputStatus, { [key]: validObject });
                }else{
                    let validArray = [] as any[];

                    let validArrayObj = validArray.map(() => {
                        return {
                            name:validObject,
                            type:validObject
                        };
                    });
                    Object.assign(combinedInputStatus, { [key]: validArrayObj });
                }
            }
            const editCount = Object.entries(combinedInputStatus);
            if (editCount) {
                setInputStatus({ ...inputStatus, ...combinedInputStatus })
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [newContact])

    useEffect(() => {
        const validateAllInput = () => {
            const entries = Object.entries(inputStatus);
            let combinedInputStatus = {} as InputValidStatus;
            for (let index = 0; index < entries.length; index++) {
                const key = entries[index][0];
                const objIError = entries[index][1];
                if(objIError.constructor !== Array){
                    if (!objIError.valid) {
                        if (objIError.text === "") {
                            Object.assign(combinedInputStatus, { [key]: invalidObject });
                        }else{
                            Object.assign(combinedInputStatus, { [key]: objIError });
                        }
                    }
                }else{
                    let nameTypeArray = objIError.map(({ name,type }:IErrorNameTypeObj) => {
                        let isInValidName = !name.valid && name.text === "";
                        let isInValidType = !type.valid && type.text === "";
                        return {
                            name:isInValidName?invalidObject:{...name},
                            type:isInValidType?invalidObject:{...type}
                        };
                    });
                    let isSomeInValid = nameTypeArray.some(({name,type}:IErrorNameTypeObj) => {return !name.valid || !type.valid});
                    if(isSomeInValid){
                        Object.assign(combinedInputStatus, { [key]: nameTypeArray });
                    }
                }
            }
            const issueCount = Object.entries(combinedInputStatus);
            console.log("REMAINING INVALID INPUT", combinedInputStatus)
            setButtonClick(false)
            console.log("REMAINING INVALID COUNT", issueCount.length)
            if (issueCount.length) {
                setInputStatus({ ...inputStatus, ...combinedInputStatus })
                return false;
            }
            return true;
        };
        if(buttonClick){
            setIsValidated(validateAllInput())
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [buttonClick])

    useEffect(() => {
        switch (buttonAction) {
            case "save":
                if (isValidated) {
                    dispatch(setContactData(values));
                    setModalContent(
                        {
                            title:"Save Contact?",
                            description:"Are you sure you want to save contact details?",
                            button: <>
                                <Button data-cy="submit-no" className={classes.leftButtonOutline} onClick={handleClose}>No</Button>
                                <Button data-cy="submit-yes" className={classes.rightButton} onClick={(e) => handleConfirmButtonClick(e,"submit")}>Yes</Button>
                            </>
                            
                        }
                    )
                    setOpenModal(true);
                }else{
                    if(myRef && myRef.current){
                        scrollToRef(myRef)
                    }
                }
                break;
            default:
                break;
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isValidated,buttonClick])

    const handleClose = () => {
        setOpenModal(false);
        setIsValidated(false)
        setButtonAction("")
    };

    const setCodeValue = (data: any) => {
        setInputStatus({ ...inputStatus, "isCodeValid": { valid: true, inputClass: "", spanClass: "", text:"" } })
        setValues({ ...newContact, "code": data, "business_address": {...newContact.business_address, "country": "Philippines" }})
    }

    const handleInputBusinessAddress = (e:any,obj: {}) => {
        //@ts-ignore
        dispatch(setUserInputValues(true))
        
        const pattern = new RegExp(/\D/);
        let { name, value } = e.target
        let inputBusinessAddress = {};
        let inputClass = "";
        let spanClass = "";
        let text = "";
        let valid = false;        

        if (value) {
            valid = true;
            if (name === "isProvinceValid") {
                inputBusinessAddress = {
                    [name]: { valid, inputClass, spanClass, text },
                    "isCityValid": { valid:false, inputClass:inputStatus.isCityValid.inputClass,spanClass:inputStatus.isCityValid.spanClass, text:inputStatus.isCityValid.text },
                    "isBrgyValid": { valid:false, inputClass:inputStatus.isBrgyValid.inputClass,spanClass:inputStatus.isBrgyValid.spanClass, text:inputStatus.isBrgyValid.text }   
                }
            } else if (name === "isCityValid") {
                inputBusinessAddress = {
                    [name]: { valid, inputClass, spanClass, text },
                    "isBrgyValid": { valid:false, inputClass:inputStatus.isBrgyValid.inputClass,spanClass:inputStatus.isBrgyValid.spanClass, text:inputStatus.isBrgyValid.text }
                }
            } else if (name === "isBldgNoValid" || name === "isZipCodeValid"){
                if ((value && value.toUpperCase() === "N/A") || (value && !pattern.test(value))) {
                    inputBusinessAddress = {[name]: { valid, inputClass, spanClass, text }}
                } else {
                    valid = false;
                    inputClass = "error";
                    spanClass = "vni-text-red-500 vni-block vni-py-1";
                    text = value === "" ? "This field is required." : "Invalid Input";
                    inputBusinessAddress = {[name]: { valid, inputClass, spanClass, text }}
                }
            } else {
                inputBusinessAddress = {[name]: { valid, inputClass, spanClass, text }}
            }
        } else {
            if (name === "isUnitValid" || name === "isZipCodeValid" || name === "isFloorValid") {
                valid = true;
                inputBusinessAddress = {
                    [name]: { valid, inputClass, spanClass, text },
                }
            } else {
                inputClass = "error";
                spanClass = "vni-text-red-500 vni-block vni-py-1";
                text = "This field is required.";
                inputBusinessAddress = {[name]: { valid, inputClass, spanClass, text }}                
            }
        }
        setInputStatus({...inputStatus,...inputBusinessAddress})
        setValues({ ...values, "business_address": {...values.business_address,...obj} })
    };

    const handleCursor = (e: any, input_type?: string)=> {
        let ss = e.target.selectionStart;
        let se = e.target.selectionEnd;
        
        if (input_type === "select") {
            // eslint-disable-next-line
            e.target.value = e.target.value;
        } else {
            // eslint-disable-next-line
            e.target.value = e.target.value;
        }
        
        e.target.selectionStart = ss;
        e.target.selectionEnd = se;

        return
    }

    const handleInput = (e: any, input_type?: string, input_id?: string) => {
        //@ts-ignore
        dispatch(setUserInputValues(true))

        handleCursor(e, input_type)

        let { name, value, id } = e.target

        let inputClass= "";
        let spanClass= "";
        let text= "";
        let valid = false;
        // if (input_type) {
        //     type = input_type;
        // }
        if (input_type === "select") {
            id = input_id;
        }
        if (value) {
            valid = true;
            if (id === "isEmailAddressValid") {
                if (validEmailRegex.test(value) || value.toUpperCase() === "N/A") {
                    valid = true;
                } else {
                    valid = false;
                    inputClass = "error";
                    spanClass = "vni-text-red-500 vni-block vni-py-1";
                    text = "Invalid email address.";
                }
            }
        } else {            
            if (id === "isCodeValid"
            || id === "isEmployeeCodeValid"
            || id === "isEvatNumberValid"
            || id === "isMdpaIdValid"
            || id === "isInputTaxValid") {
                valid = true;
            } else {
                inputClass = "error";
                spanClass = "vni-text-red-500 vni-block vni-py-1";
                text = "This field is required.";
            }
        }
        setInputStatus({ ...inputStatus, [id]: { valid, inputClass, spanClass, text } })
        setValues({ ...values, [name]:value})
    };

    const handleFooterButtonClick = (e: any, action: string) => {
        e.preventDefault();
        action = action.toLowerCase();
        switch (action) {
            case "cancel create":
                setModalContent(
                    {
                        title:"Cancel Create?",
                        description:"Are you sure you want to cancel creating your Contact Request? This will not be saved.",
                        button: <>
                                    <Button data-cy="no-btn" className={classes.leftButtonOutline} onClick={handleClose}>No</Button>
                                    <Button data-cy="yes-btn" className={classes.rightButton} onClick={(e) => handleConfirmButtonClick(e,action)}>Yes</Button>
                                </>
                    }
                )
                setOpenModal(true);
                break;
            case "cancel edit":
                setModalContent(
                    {
                        title:"Cancel Edit?",
                        description:"Are you sure you want to cancel editing Contact Request? This will not be saved.",
                        button: <>
                                    <Button data-cy="no-btn" className={classes.leftButtonOutline} onClick={handleClose}>No</Button>
                                    <Button data-cy="yes-btn" className={classes.rightButton} onClick={(e) => handleConfirmButtonClick(e,action)}>Yes</Button>
                                </>
                    }
                )
                setOpenModal(true);
                break;
            case "save":
                setButtonClick(true)
                setButtonAction(action)
                break;
            case "update":
                dispatch(setContactData(values));
                setModalContent(
                    {
                        title:"Update Request?",
                        description:"Are you sure you want to update your Contact Request?",
                        button: <>
                                    <Button data-cy="update-no" className={classes.leftButtonOutline} onClick={handleClose}>No</Button>
                                    <Button data-cy="update-yes" className={classes.rightButton} onClick={(e) => handleConfirmButtonClick(e,"submit update")}>Yes</Button>
                                </>
                    }
                )
                setOpenModal(true);
                break;
            default:
                break;
        }
        dispatch(clearValidationModalContent());
    };

    const handleInputTIN = (name:string, id: string,value:string)=>{
        //@ts-ignore
        dispatch(setUserInputValues(true))
        let isTinValid = {
            valid: true,
            inputClass: "",
            spanClass: "",
            text: ""
        }
        const trimmedValue = value.replace(/[^0-9]/g,"").length

        if (value && (trimmedValue > 0 && trimmedValue < 12)) {
            isTinValid = {
                valid: false,
                inputClass: "error",
                spanClass: "vni-text-red-500 vni-block vni-py-1",
                text: "Invalid Tax Identification Number Format."
            }
        } else if (trimmedValue === 0) {
            isTinValid = {
                valid: false,
                inputClass: "error",
                spanClass: "vni-text-red-500 vni-block vni-py-1",
                text: "This field is required.",
            }
        }
        setValues({ ...values, [name]: trimmedValue === 0 ? "" : value })
        setInputStatus({...inputStatus,[id]:isTinValid});
    };

    const handleConfirmButtonClick = (e: any, action: string) => {
        e.preventDefault();

        switch (action.toLowerCase()) {
            case "cancel create":
                setModalContent(
                    {
                        title:"Request Cancelled",
                        description:"You have cancelled Create Contact Request.",
                        button: <Button data-cy="back-to-dash" className={classes.leftButtonOutline} onClick={(e) => handleConfirmButtonClick(e,"back")}>Back to Dashboard</Button>
                    }
                )
                break;
            case "cancel edit":
                setModalContent(
                    {
                        title:"Request Cancelled",
                        description:"You have cancelled Edit Contact Request.",
                        button: <>
                                    <Button data-cy="back-to-dashboard" className={classes.leftButtonOutline} onClick={(e) => handleConfirmButtonClick(e,"back")}>Back to Dashboard</Button>
                                    {canAddFR && 
                                    <Button data-cy="create-new" className={classes.rightButton} onClick={(e) => handleConfirmButtonClick(e,"new")}>Create New</Button>}
                                </>
                    }
                )
                break;
            case "new":
                dispatch(resetState())
                dispatch(setIsCreate(true))
                dispatch(goBackAndCreateNew())
                setInputStatus(emptyInputValidStatus);
                history.push("/franchising/contact/create");
                window.scrollTo(0, 0);
                setOpenModal(false);
                break;
            case "submit":
                dispatch(addContact(values)); 
                break;
            case "submit update":
                dispatch(patchContactRequest(values));
                break;
            default:
                dispatch(resetState())
                history.push("/franchising/contact/");
                break;
        }
        dispatch(clearValidationModalContent());
    };

    const hideModalCloseButton = () => {
        return (!validationContent.error && validationContent.title !== "") || title === "Request Cancelled";
    }
    
    return ( 
            <span>
                <CreateEditContactRequest
                    inputRef={myRef}
                    contact={values}
                    inputStatus={inputStatus}
                    handleInputChange={handleInput}
                    handleInputBusinessAddress={handleInputBusinessAddress}
                    handleInputTIN={handleInputTIN}
                    isEditing ={isEdit}
                    validationContent = {validationContent}
                />
                <CreateContactFooterButton
                    isEditing={isEdit}
                    handleButton={handleFooterButtonClick}
                />
                { !isRequesting && 
                    <Modal
                        fullWidth={false}
                        maxWidth='md'
                        open={openModal}
                        //@ts-ignore
                        onClose={(event, reason) => {
                            if(reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
                                hideModalCloseButton()
                            }
                        }}
                        // hideCloseButton={hideModalCloseButton}
                    >
                        <Grid>
                            <Typography variant="h5" className={classes.modalTitle}>{title}</Typography>
                            <Typography className={classes.modalMessage} style={{maxWidth: 500}} paragraph={true}>{description}</Typography>
                                {remarks}
                            <Grid className="btn-group" style={{ textAlign: 'center' }}>     
                                {button}
                            </Grid>
                        </Grid>
                    </Modal>
                }                
            </span>
    );
};
export default CreateContactContainer;