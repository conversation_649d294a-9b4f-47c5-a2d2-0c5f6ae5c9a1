import React, { useState, useEffect } from "react"
import {
	MenuItem,
	Select,
	Grid,
	Typo<PERSON>,
	Button,
} from "@mui/material"
import { useSelector, useDispatch } from "react-redux"
import {PatternFormat} from "react-number-format"
import {
	Contact,
	InputValidStatus,
} from "../../shared/models/Contact"
import {
	fetchLocationCities,
	fetchLocationBrgys,
	fetchLocationProvince,
	clearValidationModalContent,
	fetchOfficeLocations,
} from "../../shared/reducers/ContactSlice"
import {
	contactTypeList,
	contactTaxTypeList,
} from "../../utils/Environment"
import { RootState } from "../../shared/reducers/rootReducer"

import { PreviewStyles } from "../../shared/components/ViewPDFReport/style"
import { Modal } from "../../shared/components/Modal"

interface Props {
	handleInputChange: (e: any, input_type?: string, input_id?: string) => any
	handleInputBusinessAddress: (e: any, obj: {}) => any
	handleInputTIN: (name: string, id: string, value: string) => any
	validationContent: any
	inputStatus: InputValidStatus
	contact: Contact
	inputRef: any
	isEditing: boolean
}

const CreateEditContactRequest: React.FC<Props> = (props) => {
	const classes = PreviewStyles()
	const dispatch = useDispatch()

	const {
		contact,
		inputStatus,
		handleInputChange,
		handleInputBusinessAddress,
		handleInputTIN,
		inputRef,
		isEditing,
		validationContent,
	} = props

	const {
		code,
		type,
		status,
		employee_code,
		name,
		contact_person,
		contact_number,
		email_address,
		tin,
		area,
		tax_type,
		evat_number,
		mdpa_id,
		input_tax,
		business_address,
	} = contact

	const isListFetched = useSelector(
		(state: RootState) => state.contact.isFetchingList,
	)
	const provinceList = useSelector(
		(state: RootState) => state.contact.locationListData.provinces,
	)
	const cityList = useSelector(
		(state: RootState) => state.contact.locationListData.cities,
	)
	const brgyList = useSelector(
		(state: RootState) => state.contact.locationListData.brgy,
	)
	const locationList = useSelector(
		(state: RootState) => state.contact.officeLocationList,
	)

	const [errorKey, setErrorKey] = useState("")
	const [filteredProvinceList, setFilteredProvinceList] = useState<any>([])
	const [filteredCityList, setFilteredCityList] = useState([
		{ name: "Please select a Province first", value: "", province: "" },
	])
	const [filteredBrgyList, setFilteredBrgyList] = useState([
		{ name: "Please select a City first", value: "", city: "" },
	])
	const [openErrorModal, setOpenErrorModal] = React.useState<any>(false)
	const [maxTIN, seTMaxTIN] = useState(16)
	const [areaList, setAreaList] = useState<any>([])

	useEffect(() => {
		if (tin && tin.replace(/[^0-9]/g, "").length === 12) {
			seTMaxTIN(maxTIN - 1)
		} else {
			seTMaxTIN(16)
		}
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [tin])

	useEffect(() => {
		dispatch(fetchLocationProvince())
		dispatch(fetchOfficeLocations())
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [])

	useEffect(() => {
		if (locationList.length) {
			let locations = locationList.filter((item: any) => item.status === "active")
			setAreaList(locations)
		}
	}, [locationList])

	useEffect(() => {
		if (provinceList) {
			let provinces = provinceList.filter((item: any) => item.status === "active")
			setFilteredProvinceList(provinces)
		} else {
			setFilteredCityList([
				{ name: "Please select a Province first", value: "", province: "" },
			])
		}
	}, [provinceList])

	useEffect(() => {
		if (business_address && business_address.province) {
			if (business_address && !business_address.city) {
				setFilteredBrgyList([
					{ name: "Please select a City first", value: "", city: "" },
				])
				setFilteredCityList([
					{ name: "...list loading", value: "", province: "" },
				])
			} else {
				setFilteredCityList([
					{ name: "...list loading", value: "", province: "" },
				])
			}
			
			dispatch(fetchLocationCities())
		} else {
			setFilteredCityList([
				{ name: "Please select a Province first", value: "", province: "" },
			])	
		}
		// }, [business_address && business_address.province])
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [business_address]);

	useEffect(() => {
		if (cityList && business_address && business_address.province) {
			let cities = cityList.filter(
				(item: any) =>
					item.province === business_address.province &&
					item.status === "active",
			)
			if (cities.length > 0) {
				setFilteredCityList(cities)
			} else {
				setFilteredCityList([
					{ name: "list not available", value: "", province: "" },
				])
			}
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [cityList])

	useEffect(() => {
		if (filteredCityList && business_address && business_address.city) {
			if (business_address && business_address.brgy === "") {
				dispatch(fetchLocationBrgys(business_address.city))
				setFilteredBrgyList([{ name: "...list loading", value: "", city: "" }])
			} else if (
				business_address &&
				business_address.brgy !== "" &&
				isEditing
			) {
				if (!isListFetched) {
					dispatch(fetchLocationBrgys(business_address.city))
					setFilteredBrgyList([
						{ name: "...list loading", value: "", city: "" },
					])
				}
			}
		} else {
			setFilteredBrgyList([
				{ name: "Please select a City first", value: "", city: "" },
			])
		}
		// }, [filteredCityList, business_address && business_address.city])
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [filteredCityList, business_address]);
	
	useEffect(() => {
		if (brgyList && business_address && business_address.city) {
			if (brgyList.length > 0) {
				setFilteredBrgyList(brgyList)
			} else {
				setFilteredBrgyList([
					{ name: "list not available", value: "", city: "" },
				])
			}
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [brgyList])

	useEffect(() => {
		const entries = Object.entries(inputStatus)
		for (let index = 0; index < entries.length; index++) {
			const key = entries[index][0]
			const objIError = entries[index][1]

			if (objIError.constructor !== Array) {
				if (!objIError.valid) {
					setErrorKey(key)
					break
				}
			} else {
				for (let counter = 0; counter < objIError.length; counter++) {
					let otherIErrorName = objIError[counter]["name"]
					let otherIErrorType = objIError[counter]["type"]
					if (!otherIErrorName.valid || !otherIErrorType.valid) {
						setErrorKey(`${key}-${counter}`)
						return
					}
				}
			}
		}
	}, [inputStatus])

	const blockSpecialChar = (event: any) => {
		var regex = new RegExp("^[0-9]+$")
		var key = String.fromCharCode(
			!event.charCode ? event.which : event.charCode,
		)
		if (!regex.test(key)) {
			event.preventDefault()
			return false
		}
	}

	const handleTaxInputChange = (e: any) => {
		const { name, value } = e.target
		handleInputTIN(name, "isTinValid", value)
	}

	const handleCloseErrorModal = () => {
		setOpenErrorModal(false)
		dispatch(clearValidationModalContent())
	}

	const handleChangeAddress = (keyName: string) => (event: any) => {
		let emptyAddress = {}

		let ss = event.target.selectionStart
		let se = event.target.selectionEnd

		if (
			keyName === "province" ||
			keyName === "city" ||
			keyName === "brgy"
		) {
			// eslint-disable-next-line
			event.target.value = event.target.value
		} else {
			// eslint-disable-next-line
			event.target.value = event.target.value
		}

		event.target.selectionStart = ss
		event.target.selectionEnd = se
		const { value } = event.target

		if (keyName === "bldg_no" || keyName === "zip_code") {
			Object.assign(emptyAddress, { [keyName]: value })
			handleInputBusinessAddress(event, emptyAddress)
		} else {
			const new_value = value
			switch (keyName) {
				case "province":
					Object.assign(emptyAddress, {
						province: new_value,
						city: "",
						brgy: "",
					})
					handleInputBusinessAddress(event, emptyAddress)
					break
				case "city":
					Object.assign(emptyAddress, { city: new_value, brgy: "" })
					handleInputBusinessAddress(event, emptyAddress)
					break
				default:
					Object.assign(emptyAddress, { [keyName]: new_value })
					handleInputBusinessAddress(event, emptyAddress)
					break
			}
		}
	}

	return (
		<>
			<div className="main vni-py-8 vni-px-12" id="entry_request">				
				<div className="vni-block">
					<Grid className="vni-flex vni-mb-8">
						<Grid container className="vni-flex">
							<Grid container className="vni-flex vni-mb-5">								
								<Grid
									item
									xs={3}
									className="vni-w-1/2 vni-pr-8"
								>
									<label htmlFor="">Code</label>
									<input
										type="text"
										name="code"
										data-cy="code"
										id="isCodeValid"
										className="CustomInput vni-w-full disabled"
										value={code ?? ""}
										disabled
									/>
								</Grid>
								<Grid
									item
									xs={3}
									className="vni-w-1/2 vni-pr-8"
									ref={errorKey === "isTypeValid" ? inputRef : null}
								>
									<label htmlFor="">
										Type <span className="vni-text-red-500">*</span>
									</label>
									<Select
										data-cy="type"
										value={type ?? ""}
										className={
											"CustomSelect selectInput " +
											inputStatus.isTypeValid.inputClass
										}
										name="type"
										onChange={(e) =>
											handleInputChange(e, "select", "isTypeValid")
										}
									>
										{contactTypeList.map((option, index) => (
											<MenuItem
												data-cy={`channel-option-${index + 1}`}
												key={index}
												value={option.value}
											>
												{option.name}
											</MenuItem>
										))}
									</Select>
									<span
										className={inputStatus.isTypeValid.spanClass}
									>
										{inputStatus.isTypeValid.text}
									</span>
								</Grid>
								<Grid
									item
									xs={isEditing ? 3 : 6}
									className="vni-w-1/2 vni-pr-8"
									ref={errorKey === "isEmployeeCodeValid" ? inputRef : null}
								>
									<label htmlFor="">
										Employee Code
									</label>
									<input
										type="text"
										name="employee_code"
										data-cy="employee_code"
										id="isEmployeeCodeValid"
										className={
											"CustomInput vni-w-full " +
											inputStatus.isEmployeeCodeValid.inputClass
										}
										onChange={handleInputChange}
										onBlur={handleInputChange}
										value={employee_code ?? ""}
									/>
									<span className={inputStatus.isEmployeeCodeValid.spanClass}>
										{inputStatus.isEmployeeCodeValid.text}
									</span>
								</Grid>
								{
									isEditing ?									
										<Grid
											item
											xs={3}
											className="vni-w-1/2 vni-pr-8"
										>
											<label htmlFor="">Status</label>
											<input
												type="text"
												name="status"
												data-cy="status"
												id="isStatusValid"
												className="CustomInput vni-w-full disabled"
												value={status ?? ""}
												disabled
											/>
										</Grid>
									: null										
								}
							</Grid>
							<Grid container className="vni-flex vni-mb-5">								
								<Grid
									item
									xs={6}
									className="vni-w-1/2 vni-pr-8"
									ref={errorKey === "isNameValid" ? inputRef : null}
								>
									<label htmlFor="">
										Name <span className="vni-text-red-500">*</span>
									</label>
									<input
										type="text"
										name="name"
										data-cy="name"
										minLength={1}
										maxLength={100}
										id="isNameValid"
										className={
											"CustomInput vni-w-full " +
											inputStatus.isNameValid.inputClass
										}
										onChange={handleInputChange}
										onBlur={handleInputChange}
										value={name ?? ""}
									/>
									<span className={inputStatus.isNameValid.spanClass}>
										{inputStatus.isNameValid.text}
									</span>
								</Grid>
								<Grid
									item
									xs={6}
									className="vni-w-1/2 vni-pr-8"
									ref={errorKey === "isContactPersonValid" ? inputRef : null}
								>
									<label htmlFor="">
										Contact Person <span className="vni-text-red-500">*</span>
									</label>
									<input
										type="text"
										name="contact_person"
										data-cy="contact_person"
										minLength={1}
										maxLength={100}
										id="isContactPersonValid"
										className={
											"CustomInput vni-w-full " +
											inputStatus.isContactPersonValid.inputClass
										}
										onChange={handleInputChange}
										onBlur={handleInputChange}
										value={contact_person ?? ""}
									/>
									<span className={inputStatus.isContactPersonValid.spanClass}>
										{inputStatus.isContactPersonValid.text}
									</span>
								</Grid>
							</Grid>
							<Grid container className="vni-flex vni-mb-5">								
								<Grid
									item
									xs={6}
									className="vni-w-1/2 vni-pr-8"
									ref={errorKey === "isContactNumberValid" ? inputRef : null}
								>
									<label htmlFor="">
										Contact Number <span className="vni-text-red-500">*</span>
									</label>
									<input
										type="text"
										name="contact_number"
										data-cy="contact_number"
										minLength={1}
										maxLength={100}
										id="isContactNumberValid"
										className={
											"CustomInput vni-w-full " +
											inputStatus.isContactNumberValid.inputClass
										}
										onChange={handleInputChange}
										onBlur={handleInputChange}
										value={contact_number ?? ""}
									/>
									<span className={inputStatus.isContactNumberValid.spanClass}>
										{inputStatus.isContactNumberValid.text}
									</span>
								</Grid>
								<Grid
									item
									xs={6}
									className="vni-w-1/2 vni-pr-8"
									ref={errorKey === "isEmailAddressValid" ? inputRef : null}
								>
									<label htmlFor="">
										Email Address <span className="vni-text-red-500">*</span>
									</label>
									<input
										type="text"
										name="email_address"
										data-cy="email_address"
										minLength={1}
										maxLength={100}
										id="isEmailAddressValid"
										className={
											"CustomInput vni-w-full " +
											inputStatus.isEmailAddressValid.inputClass
										}
										onChange={handleInputChange}
										onBlur={handleInputChange}
										value={email_address ?? ""}
									/>
									<span className={inputStatus.isEmailAddressValid.spanClass}>
										{inputStatus.isEmailAddressValid.text}
									</span>
								</Grid>
							</Grid>
							<Grid container className="vni-flex vni-mb-5">
								<Grid
									item
									xs={3}
									className="vni-w-1/2 vni-pr-8"
									ref={errorKey === "isAreaValid" ? inputRef : null}
								>
									<label htmlFor="">
										Area <span className="vni-text-red-500">*</span>
									</label>
									<Select
										data-cy="area"
										value={area ?? ""}
										className={
											"CustomSelect selectInput " +
											inputStatus.isAreaValid.inputClass
										}
										name="area"
										onChange={(e) =>
											handleInputChange(e, "select", "isAreaValid")
										}
									>
										{areaList.map((option:any, index:any) => (
											<MenuItem
												data-cy={`area-option-${index + 1}`}
												key={index}
												value={option.name}
											>
												{option.name}
											</MenuItem>
										))}
									</Select>
									<span
										className={inputStatus.isAreaValid.spanClass}
									>
										{inputStatus.isAreaValid.text}
									</span>
								</Grid>
								<Grid
									item
									xs={3}
									className="vni-w-1/2 vni-pr-8"
									ref={errorKey === "isTaxTypeValid" ? inputRef : null}
								>
									<label htmlFor="">
										Tax Type <span className="vni-text-red-500">*</span>
									</label>
									<Select
										data-cy="tax_type"
										value={tax_type ?? ""}
										className={
											"CustomSelect selectInput " +
											inputStatus.isTaxTypeValid.inputClass
										}
										name="tax_type"
										onChange={(e) =>
											handleInputChange(e, "select", "isTaxTypeValid")
										}
									>
										{contactTaxTypeList.map((option, index) => (
											<MenuItem
												data-cy={`channel-option-${index + 1}`}
												key={index}
												value={option.value}
											>
												{option.name}
											</MenuItem>
										))}
									</Select>
									<span
										className={inputStatus.isTaxTypeValid.spanClass}
									>
										{inputStatus.isTaxTypeValid.text}
									</span>
								</Grid>
								<Grid
									item
									xs={6}
									className="vni-w-1/2 vni-pr-8"
									ref={errorKey === "isTinValid" ? inputRef : null}
								>
									<label htmlFor="">
										TIN <span className="vni-text-red-500">*</span>
									</label>
									<PatternFormat
										id="isTinValid"
										type="text"
										data-cy="tin"
										onKeyPress={blockSpecialChar}
										className={
											"CustomInput vni-w-full " +
											inputStatus.isTinValid.inputClass
										}
										onChange={handleTaxInputChange}
										name="tin"
										value={tin ?? ""}
										format="###-###-###-###"
										allowEmptyFormatting
										maxLength={maxTIN}
									/>
									<span className={inputStatus.isTinValid.spanClass}>
										{inputStatus.isTinValid.text}
									</span>
								</Grid>
							</Grid>
							<Grid container className="vni-flex vni-mb-5">
								<Grid
									item
									xs={6}
									className="vni-w-1/2 vni-pr-8"
									ref={errorKey === "isEvatNumberValid" ? inputRef : null}
								>
									<label htmlFor="">
										EVAT Number
									</label>
									<input
										type="text"
										name="evat_number"
										data-cy="evat_number"
										minLength={1}
										maxLength={100}
										id="isEvatNumberValid"
										className={
											"CustomInput vni-w-full " +
											inputStatus.isEvatNumberValid.inputClass
										}
										onChange={handleInputChange}
										onBlur={handleInputChange}
										value={evat_number ?? ""}
									/>
									<span className={inputStatus.isEvatNumberValid.spanClass}>
										{inputStatus.isEvatNumberValid.text}
									</span>
								</Grid>
								<Grid
									item
									xs={6}
									className="vni-w-1/2 vni-pr-8"
									ref={errorKey === "isInputTaxValid" ? inputRef : null}
								>
									<label htmlFor="">
										Input Tax
									</label>
									<input
										type="text"
										name="input_tax"
										data-cy="input_tax"
										minLength={1}
										maxLength={100}
										id="isInputTaxValid"
										className={
											"CustomInput vni-w-full " +
											inputStatus.isInputTaxValid.inputClass
										}
										onChange={handleInputChange}
										onBlur={handleInputChange}
										value={input_tax ?? ""}
									/>
									<span className={inputStatus.isInputTaxValid.spanClass}>
										{inputStatus.isInputTaxValid.text}
									</span>
								</Grid>
							</Grid>							
							<Grid container className="vni-flex">
								<Grid
									item
									xs={6}
									className="vni-w-1/2 vni-pr-8"
									ref={errorKey === "isMdpaIdValid" ? inputRef : null}
								>
									<label htmlFor="">
										MDPA Identifier
									</label>
									<input
										type="text"
										name="mdpa_id"
										data-cy="mdpa_id"
										minLength={1}
										maxLength={100}
										id="isMdpaIdValid"
										className={
											"CustomInput vni-w-full " +
											inputStatus.isMdpaIdValid.inputClass
										}
										onChange={handleInputChange}
										onBlur={handleInputChange}
										value={mdpa_id ?? ""}
									/>
									<span className={inputStatus.isMdpaIdValid.spanClass}>
										{inputStatus.isMdpaIdValid.text}
									</span>
								</Grid>
							</Grid>
						</Grid>
					</Grid>					
					<Grid className="vni-w-full vni-mb-5">
						<label htmlFor="">
							Address
						</label>
					</Grid>
					<Grid container className="vni-flex vni-flex-col vni-mb-5">
						<Grid container className="vni-flex vni-w-5/6">
							<Grid item xs={6} className="vni-w-1/3">
								<div className="vni-flex vni-mb-5">
									<span
										className="vni-w-1/2 vni-pr-8"
										ref={errorKey === "isUnitValid" ? inputRef : null}
									>
										<label htmlFor="">
											Unit
										</label>
										<input
											name="isUnitValid"
											type="text"
											id="business_address_unit"
											data-cy="business_address_unit"
											onChange={handleChangeAddress("unit")}
											className={
												"CustomInput vni-w-full " +
												inputStatus.isUnitValid.inputClass
											}
											value={
												business_address ? business_address.unit ?? "" : ""
											}
										/>
										<span className={inputStatus.isUnitValid.spanClass}>
											{inputStatus.isUnitValid.text}
										</span>
									</span>
									<span
										className="vni-w-1/2 vni-pr-8"
										ref={errorKey === "isFloorValid" ? inputRef : null}
									>
										<label htmlFor="">
											Floor No
										</label>
										<input
											name="isFloorValid"
											type="text"
											id="business_address_floor"
											data-cy="business_address_floor"
											minLength={1}
											maxLength={50}
											onChange={handleChangeAddress("floor")}
											className={
												"CustomInput vni-w-full " +
												inputStatus.isFloorValid.inputClass
											}
											value={
												business_address ? business_address.floor ?? "" : ""
											}
										/>
										<span className={inputStatus.isFloorValid.spanClass}>
											{inputStatus.isFloorValid.text}
										</span>
									</span>
								</div>
							</Grid>
							<Grid item xs={6}>
								<div
									className="vni-mb-5 vni-pr-8"
									ref={errorKey === "isBldgNameValid" ? inputRef : null}
								>
									<label htmlFor="">
										Building Name <span className="vni-text-red-500">*</span>
									</label>
									<input
										name="isBldgNameValid"
										type="text"
										id="business_address_bldg_name"
										data-cy="business_address_bldg_name"
										minLength={1}
										maxLength={50}
										onChange={handleChangeAddress("bldg_name")}
										className={
											"CustomInput vni-w-full " +
											inputStatus.isBldgNameValid.inputClass
										}
										value={
											business_address ? business_address.bldg_name ?? "" : ""
										}
									/>
									<span className={inputStatus.isBldgNameValid.spanClass}>
										{inputStatus.isBldgNameValid.text}
									</span>
								</div>
							</Grid>
						</Grid>
						<Grid container className="vni-flex vni-w-5/6">
							<Grid item xs={3} className="vni-w-1/3">
								<div
									className="vni-mb-5 vni-pr-8"
									ref={errorKey === "isStreetValid" ? inputRef : null}
								>
									<label htmlFor="">
										Street <span className="vni-text-red-500">*</span>
									</label>
									<input
										name="isStreetValid"
										type="text"
										id="business_address_street"
										data-cy="business_address_street"
										minLength={1}
										maxLength={50}
										onChange={handleChangeAddress("street")}
										className={
											"CustomInput vni-w-full " +
											inputStatus.isStreetValid.inputClass
										}
										value={
											business_address ? business_address.street ?? "" : ""
										}
									/>
									<span className={inputStatus.isStreetValid.spanClass}>
										{inputStatus.isStreetValid.text}
									</span>
								</div>
							</Grid>
							<Grid item xs={3} className="vni-w-1/3">
								<div
									className="vni-mb-5 vni-pr-8"
									ref={errorKey === "isBrgyValid" ? inputRef : null}
								>
									<label htmlFor="">
										Barangay <span className="vni-text-red-500">*</span>
									</label>
									<Select
										name="isBrgyValid"
										value={business_address ? business_address.brgy : ""}
										className={
											"CustomSelect vni-w-full " +
											inputStatus.isBrgyValid.inputClass
										}
										id="business_address_brgy"
										data-cy="business_address_brgy"
										onChange={handleChangeAddress("brgy")}
									>
										{filteredBrgyList.map((option: any, index: any) => {
											let isTrue: any =
												business_address && business_address.city
											let isLoading: any =
												filteredBrgyList.length === 1 &&
												!option.value &&
												!option.status
											return (
												option.name && (
													<MenuItem
														data-cy={`city-option-${index + 1}`}
														key={index}
														value={isTrue ? option.name : ""}
														disabled={isLoading}
													>
														{option.name}
													</MenuItem>
												)
											)
										})}
									</Select>
									<span className={inputStatus.isBrgyValid.spanClass}>
										{inputStatus.isBrgyValid.text}
									</span>
								</div>
							</Grid>
							<Grid item xs={3} className="vni-w-1/3">
								<div
									className="vni-mb-5 vni-pr-8"
									ref={errorKey === "isCityValid" ? inputRef : null}
								>
									<label htmlFor="">
										City/Municipality{" "}
										<span className="vni-text-red-500">*</span>
									</label>
									<Select
										name="isCityValid"
										value={business_address ? business_address.city : ""}
										className={
											"CustomSelect vni-w-full selectInput " +
											inputStatus.isCityValid.inputClass
										}
										id="business_address_city"
										data-cy="business_address_city"
										onChange={handleChangeAddress("city")}
									>
										{filteredCityList.map((option: any, index: any) => {
											let isTrue: any =
												(business_address && business_address.province)
											let isLoading: any =
												filteredCityList.length === 1 &&
												!option.value &&
												!option.status
											return (
												option.name && (
													<MenuItem
														data-cy={`city-option-${index + 1}`}
														key={index}
														value={isTrue ? option.name : ""}
														disabled={isLoading}
													>
														{option.name}
													</MenuItem>
												)
											)
										})}
									</Select>
									<span className={inputStatus.isCityValid.spanClass}>
										{inputStatus.isCityValid.text}
									</span>
								</div>
							</Grid>
							<Grid item xs={3} className="vni-w-1/3">
								<div
									className="vni-mb-5 vni-pr-8"
									ref={errorKey === "isProvinceValid" ? inputRef : null}
								>
									<label htmlFor="">
										State/Province <span className="vni-text-red-500">*</span>
									</label>
									<Select
										name="isProvinceValid"
										value={business_address ? business_address.province : ""}
										className={
											"CustomSelect vni-w-full selectInput " +
											inputStatus.isProvinceValid.inputClass
										}
										id="business_address_province"
										data-cy="business_address_province"
										onChange={handleChangeAddress("province")}
									>
										{filteredProvinceList &&
											filteredProvinceList.map((option: any, index: number) => (
												<MenuItem
													data-cy={`province-option-${index + 1}`}
													key={index}
													value={option.name}
												>
													{option.name}
												</MenuItem>
											))}
									</Select>
									<span className={inputStatus.isProvinceValid.spanClass}>
										{inputStatus.isProvinceValid.text}
									</span>
								</div>
							</Grid>
						</Grid>
						<Grid container className="vni-flex vni-w-5/6">
							<Grid item xs={3} className="vni-w-1/3">
								<div
									className="vni-mb-5 vni-pr-8"
								>
									<label htmlFor="">
										Country
									</label>
									<input
										type="text"
										id="business_address_country"
										data-cy="business_address_country"
										minLength={1}
										maxLength={50}
										className="CustomInput vni-w-full disabled"
										value="Philippines"
									/>
								</div>
							</Grid>
							<Grid item xs={3} className="vni-w-1/3">
								<div
									className="vni-mb-5 vni-pr-8"
									ref={errorKey === "isZipCodeValid" ? inputRef : null}
								>
									<label htmlFor="">
										ZIP Code
									</label>
									<input
										name="isZipCodeValid"
										type="text"
										id="business_address_zip_code"
										data-cy="business_address_zip_code"
										minLength={1}
										maxLength={7}
										onChange={handleChangeAddress("zip_code")}
										className={
											"CustomInput vni-w-full " +
											inputStatus.isZipCodeValid.inputClass
										}
										value={
											business_address ? business_address.zip_code ?? "" : ""
										}
									/>
									<span className={inputStatus.isZipCodeValid.spanClass}>
										{inputStatus.isZipCodeValid.text}
									</span>
								</div>
							</Grid>
						</Grid>
					</Grid>
				</div>	
			</div>			
			<Modal
				fullWidth={false}
				maxWidth="md"
				open={openErrorModal}
				// onClose={handleCloseErrorModal}
				//@ts-ignore
				onClose={(event, reason) => {
					if(reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
						handleCloseErrorModal()
					}
				}}
			>
				<Grid>
					<Typography variant="h5" className={classes.modalTitle}>
						{validationContent.title}
					</Typography>
					<Typography
						className={classes.modalMessage}
						style={{ maxWidth: 500 }}
						paragraph={true}
					>
						{validationContent.p}
					</Typography>
					<Grid className="btn-group" style={{ textAlign: "center" }}>
						<Button
							data-cy="Okay"
							className={classes.rightButton}
							onClick={handleCloseErrorModal}
						>
							Okay
						</Button>
					</Grid>
				</Grid>
			</Modal>
		</>
	)
}

export default CreateEditContactRequest
