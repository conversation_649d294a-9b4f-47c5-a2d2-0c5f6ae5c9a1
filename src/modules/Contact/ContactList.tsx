import React, { useEffect, useState } from "react"
import { useLocation, useHistory } from "react-router-dom"
import { useDispatch, useSelector } from "react-redux"
import {
	Dialog,
	DialogTitle,
	DialogContent,
	DialogActions,
} from "@mui/material"
import {
	columnExtensions,
	columnType,
	tableColumn,
} from "../../shared/models/Contact"
import {
	fetchContacts,
	setModalContent,
	fetchContact,
	clearValidationModalContent,
	updateContactRequestStatus,
	resetState,
} from "../../shared/reducers/ContactSlice"
import { Modal } from "../../shared/components/Modal"
import { RootState } from "../../shared/reducers/rootReducer"
import { TableComponent } from "../../shared/components/TableComponent"
import { ViewContact } from "./ViewContact/Layout/ViewContact"

interface iProps {
	searchResult?: any,
	result?: any
}

const ContactList: React.FC<iProps> = (props) => {
	const { searchResult, result } = props;
	
	let path = useLocation()
	const dispatch = useDispatch()
	const token = useSelector((state: RootState) => state.login.token)
	const role = useSelector((state: RootState) => state.login.roleAPI)
	const history = useHistory()
	let ContactListData = useSelector(
		(state: RootState) => state.contact.contactList,
	)
	const contactListTypeFilterIndex = useSelector(
		(state: RootState) => state.contact.activeFilterIndex,
	)
	const contactListTypeFilterName = useSelector(
		(state: RootState) => state.contact.activeFilterName,
	)
	const contactListStatusFilterIndex = useSelector(
		(state: RootState) => state.contact.activeStatusFilterIndex,
	)
	const contactListStatusFilterName = useSelector(
		(state: RootState) => state.contact.activeStatusFilterName,
	)
	const contactDetails = useSelector((state: RootState) => state.contact.details);
	const isSearching = useSelector(
		(state: RootState) => state.contact.isSearching,
	)
	const isViewLoading = useSelector(
		(state: RootState) => state.contact.isViewLoading,
	)
	const isRequesting = useSelector(
		(state: RootState) => state.contact.isRequesting,
	)
	const validationContent = useSelector(
		(state: RootState) => state.contact.validationModalContent,
	)
	const [openModalView, setOpenModalView] = useState(false)
	const [openModal, setOpenModal] = useState(false)
	const [confirmValues, setConfirmValues] = useState({
		action: "",
		h1: "",
		p: "",
		text: [] as string[],
	})

	let tableContents: any = [];
	let tableRow: any = [];
	let tableColumnExtensions: columnExtensions[] = [];
	let column: columnType | any[] = [];
	// let tableDashboard: number = 0;
	let searchItem = result;

	if (ContactListData.length !== 0) {
		ContactListData =
			searchResult && isSearching && searchItem !== ""
				? searchResult
				: ContactListData

				if ((contactListStatusFilterName === "All" || contactListStatusFilterIndex === 0)
					&& (contactListTypeFilterName === "All" || contactListTypeFilterIndex === 0)) {
					tableContents = ContactListData
					// setTableContents(ContactListData);
				} else if ((contactListStatusFilterName === "All" || contactListStatusFilterIndex === 0)
					&& (contactListTypeFilterName !== "All" || contactListTypeFilterIndex !== 0)) {
					tableContents = ContactListData.filter(
						({ type }) => type === contactListTypeFilterName,
					)
					// setTableContents( ContactListData.filter(
					// 	({ type }) => type === contactListTypeFilterName,
					// ));
				} else if ((contactListTypeFilterName === "All" || contactListTypeFilterIndex === 0)
					&& (contactListStatusFilterName !== "All" || contactListStatusFilterIndex !== 0)) {
					tableContents = ContactListData.filter(
						({ status }) => status === contactListStatusFilterName,
					)
					// setTableContents(ContactListData.filter(
					// 	({ status }) => status === contactListStatusFilterName,
					// ));
				} else {
					tableContents = ContactListData.filter(
						({ status, type }) => (status === contactListStatusFilterName && type === contactListTypeFilterName),
					)
					// setTableContents(ContactListData.filter(
					// 	({ status, type }) => (status === contactListStatusFilterName && type === contactListTypeFilterName),
					// ));
				}
				
				tableRow = tableContents.map((content: any, index: any) => {
					const fullAddress = `${content.business_address.unit ? content.business_address.unit+", " : ""}
					${content.business_address.floor ? content.business_address.floor+", " : ""}
					${content.business_address.bldg_name}, 
					${content.business_address.street}, 
					${content.business_address.brgy}, 
					${content.business_address.city}, 
					${content.business_address.province}, 
					${content.business_address.zip_code ? content.business_address.country+", " : content.business_address.country}
					${content.business_address.zip_code ? content.business_address.zip_code : ""}`
			
					return {
						index_no: index + 1,
						id: content._id,
						code: content.code,
						name: content.name,
						employee_code: content.employee_code,
						type: content.type,
						contact_person: content.contact_person,
						contact_number: content.contact_number,
						area: content.area,
						tax_type: content.tax_type,
						tin: content.tin,
						evat_number: content.evat_number,
						input_tax: content.input_tax,
						mdpa_id: content.mdpa_id,
						address: fullAddress,
						status: content.status,
						action: content._id,
					}
				})
				column = tableColumn;
	} else {
		// setTableContents([]);
		tableContents = [];
	}

	useEffect(() => {
		if (token && role) {
			dispatch(fetchContacts());
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [path, token, role])

	useEffect(() => {
		if (validationContent.title && validationContent.p) {
			setConfirmValues({
				...confirmValues,
				h1: validationContent.title,
				p: validationContent.p,
			})
		}
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [validationContent])

	useEffect(() => {
	}, [ContactListData, searchResult,  contactListTypeFilterIndex]);

	const handleViewClick = (id: string) => {
		setOpenModalView(true)
		dispatch(setModalContent({ title: "", p: "", error: false }))
		dispatch(fetchContact(id))
	}

	const handleCloseView = () => {
		setOpenModalView(false)
	}

	const handleOpenConfirm = (e: any, action: string) => {
		e.preventDefault()
		let h1 = ""
		let p = ""
		let text = [] as string[]
		dispatch(clearValidationModalContent())

		switch (action) {
			case "deactivate":
				h1 = "Confirmation"
				p = `Are you sure you want to deactivate this contact? Please click "Confirm" to proceed.`
				text = ["Cancel", "Confirm"]
				break
			case "activate":
				h1 = "Confirmation"
				p = `Are you sure you want to activate this contact? Please click "Confirm" to proceed.`
				text = ["Cancel", "Confirm"]
				break
		}
		setConfirmValues({ action, h1, p, text })
		setOpenModal(true)
	}

	const handleOpenConfirmed = (e: any, action: string) => {
		e.preventDefault()
		let h1 = ""
		let p = ""
		let text = [] as string[]
		
		switch (action) {
			case "deactivate":
				dispatch(updateContactRequestStatus(contactDetails._id, action, contactDetails));
				text = ["Back to Dashboard"]
                break;
            case "activate":
				dispatch(updateContactRequestStatus(contactDetails._id, action, contactDetails));
				text = ["Back to Dashboard"]
                break;
		}

		setOpenModalView(false)
		setConfirmValues({ action, h1, p, text })
	}

	const handleBackToDashboard = (e: any) => {
		e.preventDefault()
		setOpenModal(false)
		dispatch(resetState())
		history.push("/franchising/contact/")
	}

	const handleClose = (e: any) => {
		e.preventDefault()
		setOpenModal(false)
	}

	return (
		<div className="vni-table">
			<div className="vni-flex-grow" style={{ maxWidth: "100%" }}>
				<div className="scrollable">
					<div
						className={"contactlist vni-relative"}
						style={{ width: "calc(100vw - 152px)", maxWidth: "100%" }}
					>
						<TableComponent
							searchResult={searchResult}
							tableItem={tableContents}							
							columnExtensions={tableColumnExtensions}
							rows={tableRow}
							columns={column}
							enablePaging={true}
							handleViewClick={handleViewClick}
							customNoDataMessage="No records found"
						/>
					</div>
					{!isRequesting && (
						<Modal
							fullWidth={true}
							maxWidth="xl"
							open={openModalView}
							onClose={handleCloseView}
							whereThisIsUsed={`vfr-view-contact`}
						>
							{!isViewLoading && (
									<ViewContact
										handleClickButton={handleOpenConfirm}
									/>
								)}
						</Modal>
					)}
				</div>
			</div>
			{(!isRequesting && (confirmValues.action === "deactivate"  || confirmValues.action === "activate")) && (
				<Dialog
					className="vni-relative mark-modal"
					open={openModal}
					onClose={handleClose}
					aria-labelledby="form-dialog-title"
					maxWidth="md"
				>
					<DialogTitle className="vni-px-5 vni-pt-3">
						<h3 className={"title"} >
							{confirmValues.h1}
						</h3>
					</DialogTitle>
					<DialogContent className=" vni-px-5">
						{
							<p className="vni-mb-5" style={{ maxWidth: 450 }}>
								{confirmValues.p}
							</p>
						}						
					</DialogContent>
					<DialogActions className="btn-group d-flex-center vni-px-5 vni-pb-3">
						{
							(confirmValues.action === "deactivate" || confirmValues.action === "activate") &&
								!validationContent.error && confirmValues.text[0] ? (
									<a
										href="#!"
										data-cy={`${confirmValues.text[0]}-btn`}
										className="CustomPrimaryOulineButton"
										onClick={confirmValues.text[0] === "Cancel" ? handleClose : handleBackToDashboard}
									>
										{confirmValues.text[0]}
									</a>
								) : validationContent.error && confirmValues.h1 && confirmValues.p ? (
									<a
										href="#!"
										data-cy={`${confirmValues.action}-okay-btn`}
										className="CustomPrimaryButton scarlet"
										onClick={handleClose}
									>
										Okay
									</a>
								) : ( <></> )
						}
						{
							(confirmValues.action === "deactivate" || confirmValues.action === "activate") &&
								!validationContent.error && confirmValues.text[1] ? (
									<a
										href="#!"
										data-cy={`${confirmValues.action}-${confirmValues.text[1]}-btn`}
										className={"CustomPrimaryButton"}
										onClick={(e) => handleOpenConfirmed(e, confirmValues.action)}
									>
										{confirmValues.text[1]}
									</a>
								) : ( <></> )
						}
						<a
							href="#!"
							data-cy="data-processing-close-modal"
							className="close-modal"
							onClick={handleClose}
						>
							Close{" "}
						</a>
					</DialogActions>
				</Dialog>
			)}
		</div>
	)
}

export default ContactList
