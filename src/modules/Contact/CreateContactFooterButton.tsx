import React from "react";
import { Box } from "@mui/material";
import { useSelector } from 'react-redux';
import { RootState } from "../../shared/reducers/rootReducer"

interface Props {
  handleButton: (e: any, action: string) => any;
  isEditing: boolean;
}

const CreateContactFooterButton: React.FC<Props> = (props) => {
  const isEdit = useSelector((state: RootState) => state.contact.isEdit);
  const {handleButton,isEditing} = props
 
  return (
    <Box className="kyc-footer-btns vni-mt-8">
      <Box component="span" className="vni-flex"></Box>
      <Box component="span" className="vni-flex">
        <a data-cy="cancel-btn" href="#!" onClick={(e) => handleButton(e, isEdit? "Cancel Edit" :"Cancel Create")}>
            <button className="CustomPrimaryButton bluewood">Cancel</button>
        </a>
        <a href="#!"
          onClick={(e) => handleButton(e, isEditing ? "update" : "save")}
        >
          <button
            data-cy="submit-btn"
            className="CustomPrimaryOulineButton green"
          >
            Save
          </button>
        </a>
      </Box>
    </Box>
  );
};
export default CreateContactFooterButton;
