import React, { Fragment, useEffect, useState } from "react";
import { useLocation, NavLink } from "react-router-dom";
import { useDispatch } from "react-redux";
import {
  changeFilter,
  changeStatusFilter,
  setIsSearching,
  fetchContacts
} from "../../shared/reducers/ContactSlice";
import ContactList from "./ContactList";
import {
  Box,
  Typography,
  Select,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";
import { RootState } from "../../shared/reducers/rootReducer";
import { useSelector } from "react-redux";
import { logout } from "../../shared/reducers/LoginSlice";
import { apiURL } from "../../utils/Environment";

const ContactContainer: React.FC = () => {
  let createUrl = `/franchising/contact/create`
  const [searchValue, setSearchValue] = useState<string>("");
  const [searchResult, setSearchResult] = useState([]);
  const [viewExpiredModal, setViewExpiredModal] = useState(false);
  const token = useSelector((state: RootState) => state.login.token);

  const dispatch = useDispatch();
  let location = useLocation();

  useEffect(() => {
    dispatch(changeFilter({ filterIndex: 0, name: "All" }));
    dispatch(changeStatusFilter({ filterStatusIndex: 0, statusName: "All" }));
    window.scrollTo(0, 0);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location]);

  useEffect(() => {
		dispatch(fetchContacts())
    // eslint-disable-next-line react-hooks/exhaustive-deps
	}, [])
  

  let typeFilters: Array<string> = ["All", "Broker", "Agent"];
  let statusFilters: Array<string> = ["All", "Active", "Inactive"];
  let filterName: string;
  let filterStatusName: string;

  const handleSelectStatus = (e: any) => {
    filterStatusName = e.target.value;

    dispatch(
      changeStatusFilter({
        filterStatusIndex: statusFilters.indexOf(filterStatusName),
        statusName: filterStatusName,
      })
    );
  };

  const handleSelectType = (e: any) => {
    filterName = e.target.value;

    dispatch(
      changeFilter({
        filterIndex: typeFilters.indexOf(filterName),
        name: filterName,
      })
    );
  };

  const filterOptionsStatus = statusFilters.map((filter, index) => (
    <option data-cy={`select-status-option-${index}`} key={index} value={filter}>
      {filter}
    </option>
  ));

  const filterOptionsType = typeFilters.map((filter, index) => (
    <option data-cy={`select-status-option-${index}`} key={index} value={filter}>
      {filter}
    </option>
  ));

  const contactData = useSelector(
    (state: RootState) => state.contact.contactList
  );

  const handleSearchTextbox = (e: any) => {
    const { value } = e.target
    setSearchValue(value);
    dispatch(setIsSearching(false));
  };

  const handleSearch = (e: any) => {
    e.preventDefault();

    function filterBy(item: any) {
      let code = item.code.toLowerCase();
      let name = item.name.toLowerCase();
      let contact_person = item.contact_person.toLowerCase();
      let contact_number = item.contact_number ? item.contact_number.toLowerCase() : "";
      const fullAddress = [
        `${item.business_address.unit ? item.business_address.unit+", " : ""}`,
        `${item.business_address.floor ? item.business_address.floor+", " : ""}`,
        `${item.business_address.bldg_name}, `,
        `${item.business_address.street}, `,
        `${item.business_address.brgy}, `,
        `${item.business_address.city}, `,
        `${item.business_address.province}, `,
        `${item.business_address.zip_code ? item.business_address.country+", " : item.business_address.country}`,
        `${item.business_address.zip_code ? item.business_address.zip_code : ""}`        
      ].join("");

      let address = fullAddress.toLowerCase();

      let newSearchValue = searchValue;
      let values = newSearchValue.toLowerCase();

      if (
        code.includes(values) ||
        name.includes(values) ||
        contact_person.includes(values) ||
        contact_number.includes(values) ||
        address.includes(values)
      ) {
        return true;
      }
      return false;
    }

    let item = [];
    if (searchValue) {
      dispatch(setIsSearching(true));
      item = contactData.filter(filterBy);
      setSearchResult(item);
    } else {
      dispatch(setIsSearching(false));
    }
  };

  const checkToken = (accessToken: any) => {
    let URL = apiURL.userManagement + '/underwriting/user/check'

    return fetch(URL, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + accessToken,
        }}).then(async (res: any) => {
            if (!res.ok) {
                let respjson = await res.json()
                let errormessage = respjson.error.message
                throw Error(errormessage)
            }
            return res ? res.json() : null
        })
  }

  const isTokenValid = async () => {
    let isValid = true,
    isPmValid = true,
    isApiValid = true;

    if (token) {
        if (!localStorage.getItem('api_token') || !localStorage.getItem('pm_token')) {
          isValid = false;
        } else if (
          localStorage.getItem('api_token') &&
          localStorage.getItem('pm_token')
        ) {
          try {
            let api_token = await checkToken(localStorage.getItem('api_token'));
            let pm_token = await checkToken(localStorage.getItem('pm_token'));
            if (!api_token.status) {
              isApiValid = false;
            }
            if (!pm_token.status) {
              isPmValid = false;
            }
          } catch (error) {
            console.log(error, 'error');
            isPmValid = false;
            isApiValid = false;
          }
        }
    
        console.log('TOKEN VALIDITY', isValid, isPmValid, isApiValid);
        if (!isValid || !isPmValid || !isApiValid) {
          // console.log('INVALID TOKEN');
          // var a = '../customer-care/index.html#/franchising' + '/invalidSession';
          // window.location.href = a;
          //onLogout();
          setViewExpiredModal(true);
        }
      };
    }
    

  useEffect(() => {
    isTokenValid()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token])

  console.log('expired modal', viewExpiredModal)
  
  return (
    <Fragment>
      <Box className="main vni-py-8 vni-px-12">
        <div className="vni-block vni-text-right">
          <button className="vni-text-ocean_green">
              <NavLink
                data-cy="create-frachise-request"
                className="vni-text-ocean_green vni-no-underline vni-flex vni-items-center"
                to={createUrl}
              >
                <i className="fas fa-plus vni-pr-2"></i>
                <span className="text-ocean_green">Add Contact</span>
              </NavLink>
          </button>
        </div>
        <h1 className="vni-font-bold vni-text-3xl">Agents and Brokers</h1>
          <Box className="vni-search">
            <form className="vni-flex vni-w-2/3" onSubmit={handleSearch}>
              <Box className="add-icon vni-flex-grow">
                <TextField
                  data-cy="search-franchise"
                  className={"w-100"}
                  value={searchValue}
                  variant="outlined"
                  onChange={handleSearchTextbox}
                />
                <small className="vni-absolute vni-mt-1 vni-italic vni-text-gray-500 vni-block">
                    Search for Code, Company, Contact Person, Contact Number or an Address.
                  </small>
              </Box>
              <button
                type="submit"
                data-cy="search-franchise-btn"
                className="CustomPrimaryButton"
                disabled={(searchValue === "")}
              >
                Search
              </button>
            </form>
          </Box>
        <Box className="vni-flex vni-items-center" style={{float:"right"}}>
          <Box className="vni-flex vni-self-end vni-items-center vni-w-64" 
              style={{width: "20rem", marginRight: "50px"}}>
            <Typography
              className="CustomLabel vni-inline-block vni-w-32"
              component="p"
              style={{width: "14rem"}}
            >
              Filter by Status:
            </Typography>
            <Select
              native
              inputProps={{
                "name": 'status',
                "id": 'requestStatus',
                "data-cy": "select-status"
              }}
              onChange={handleSelectStatus}
              style={{ width: "22rem" }}
            >
              {filterOptionsStatus}
            </Select>
          </Box>
          <Box className="vni-flex vni-self-end vni-items-center vni-w-64"
              style={{width: "20rem"}}>
            <Typography
              className="CustomLabel vni-inline-block vni-w-32"
              component="p"
              style={{width: "12rem"}}
            >
              Filter by Type:
            </Typography>
            <Select
              native
              inputProps={{
                "name": 'status',
                "id": 'requestStatus',
                "data-cy": "select-status"
              }}
              onChange={handleSelectType}
              style={{ width: "22rem" }}
            >
              {filterOptionsType}
            </Select>
          </Box>
        </Box>
        <Box className="vni-table-container">
          <ContactList searchResult={searchResult} result={searchValue} />
        </Box>
      </Box>

      {viewExpiredModal === true && (

        <Dialog 
            id="session-modal-expired"
            maxWidth='xs'
            open={viewExpiredModal === true}
            onClose={() => {
            dispatch(logout())
            window.location.replace('../index.html#/')
            }}
            className="vni-m-auto"
        >
            <DialogTitle className="vni-px-5 vni-pt-3 vni-mb-8">
              <h2 className="title">Session Expired</h2>
            </DialogTitle>
            <DialogContent>
              <p className="vni-mb-8">
              Your account has been logged out. Please try logging in again.
              </p>
            </DialogContent>
            <DialogActions className="vni-flex d-flex-center">
              <button className="CustomPrimaryButton vni-mr-5" data-cy="session-timeout-logout-btn" onClick={() => {
                  dispatch(logout())
                  window.location.replace('../index.html#/')

                  }}>Okay</button>
            </DialogActions>
        </Dialog>
      )};
    </Fragment>
  );
};

export default ContactContainer;
