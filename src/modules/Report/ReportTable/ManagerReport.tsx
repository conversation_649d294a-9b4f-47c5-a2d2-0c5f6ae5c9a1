import React, { useEffect, useState } from "react";
import { useHistory } from "react-router-dom";
import { TableComponent } from "../../../shared/components/TableComponent";
import {
  columns,
  columnExtensions,
  filteringStateColumnExtensions,
  columnsHigherManagement,
  columnExtensionsHigherManagement,
  filteringStateColumnExtensions2,
} from "../reports.const";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { Dialog, DialogActions, DialogContent, DialogTitle, Grid, Typography } from "@mui/material";
import { PageSubHeader } from "../../../shared/components/PageSubHeader";
import { faBorderAll, faDownload } from "@fortawesome/free-solid-svg-icons";
import { Modal } from "../../../shared/components/Modal";
import { ViewReport } from "../../../shared/components/ViewReport";
import { RootState } from "../../../shared/reducers/rootReducer";
import { useSelector, useDispatch } from "react-redux";
import {
  fetchFranchiseRequestLogs,
  generateDailyReport,
  fetchGeneratedReports,
  setpreviewData,
  fetchFranchiseReportsBySalesHead,
} from "../../../shared/reducers/ReportSlice";
import {
  setIsRequesting,
  fetchBDOUsersRaw,
} from "../../../shared/reducers/FranchiseSlice";
import moment from "moment";
import { getReasonRemarkForReport } from "../../../utils/DataHelper";

import { frsRole } from "../../../shared/components/Layout/constants";
import { apiURL } from "../../../utils/Environment";
import { logout } from "../../../shared/reducers/LoginSlice";
import { decryptCredentials } from "../../../utils/LoginService";

interface IProps {
  role: string
}

const ManagerReport: React.FC<IProps> = (props: IProps) => {
  const history = useHistory();
  const dispatch = useDispatch();
  const { role } = props;

  const userId = useSelector((state: RootState) => state.login.user_id);
  const token = useSelector((state: RootState) => state.login.token);
  // const role = useSelector((state: RootState) => state.login.roleAPI);
  const userRoles = useSelector((state: RootState) => state.login.userRoles);
  const mainRole = useSelector((state: RootState) => state.login.main_role);
  const mainModule = useSelector((state: RootState) => state.login.main_module);
  const subRoles = useSelector((state: RootState) => state.login.role);
  const { isSalesHead } =
    useSelector((state: RootState) => state.login.userRoles);
  const isRequesting = useSelector(
    (state: RootState) => state.franchise.isRequesting
  );
  const reportList = useSelector((state: RootState) => state.report.reportList);
  const [openModal, setOpenModal] = useState<boolean>(
    isSalesHead ? false : false || (reportList && reportList.length > 0)
  );
  const { canGenerateReport } = useSelector(
    (state: RootState) => state.login.userPermissions
  );

  const bdoUserList = useSelector(
    (state: RootState) => state.franchise.bdoUserListData
  );

  const [reportRole, setReportRole] = useState<string>("");
  const multipleRoles = Object.values(userRoles).reduce(
    (a, userRole) => a + userRole,
    0
  );
  const [isMultipleRole, setIsMultipleRoles] = useState<any>(false);
  const [viewExpiredModal, setViewExpiredModal] = React.useState(false);

  useEffect(() => {
    if (reportRole && token) {
      if (reportRole === "sales_head") {
        dispatch(fetchBDOUsersRaw());
      } 
      dispatch(fetchFranchiseRequestLogs(role));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token, reportRole]);

  useEffect(() => {
    if (mainRole && role && token) {
      frsRole["FRF_SALES_HEAD"] = ["Sales Team Lead", "sales_head"];
      const _role = Object.keys(frsRole);

      //* For FRS main users
      if (mainModule === "Franchising" || _role.includes(mainRole)) {
        setReportRole(role);
      } else {
        //* For NON FRS users
        const nonFrsUserRoles = subRoles
          .filter(
            (i: any) => i.module === "Franchising" && _role.includes(i.name)
          )
          .map((r: any) => r.name);
        if (nonFrsUserRoles.length >= 1) {
          if (nonFrsUserRoles.some((i: any) => i === "FRF_SALES_HEAD"))
            setReportRole(frsRole["FRF_SALES_HEAD"][1]);
          else setReportRole(frsRole[nonFrsUserRoles[0]][1]);
        }
      }
    }
    if (multipleRoles > 1) {
      console.log('multipleRoles', multipleRoles, mainRole);
      setIsMultipleRoles(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mainRole, role, mainModule, token, multipleRoles, isMultipleRole]);

  useEffect(() => {
    if (token && role) {
      if (bdoUserList && reportRole === "sales_head") {
        const bdoListBySales = bdoUserList.filter(
          (m: any) => m.reports_to?._id === userId
        );
        const memberIds: string[] = bdoListBySales.map((m: any) => m._id);
        dispatch(fetchFranchiseReportsBySalesHead(memberIds));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bdoUserList, role, token]);

  const isDTR = useSelector(
    (state: RootState) => state.report.previewData.isDTR
  );
  const isGDTR = useSelector(
    (state: RootState) => state.report.previewData.isGDTR
  );
  const previousDateFromFilter = useSelector(
    (state: RootState) => state.report.previewData.DTRfrom
  );
  const previousDateToFilter = useSelector(
    (state: RootState) => state.report.previewData.DTRto
  );
  const [date_from, setDateFrom] = useState<any>();
  const [date_to, setDateTo] = useState<any>();

  useEffect(() => {
    let dateToday = moment()
      // .set({ hour: 16, minute: 0, second: 0, ms: 0 })
      // .format("MM/DD/YYYY");

    setDateFrom(
      // !isDTR && reportRole !== "validator"
      !isDTR
        ? dateToday
        : previousDateFromFilter === ""
        ? dateToday
        : moment(previousDateFromFilter)
    );
    setDateTo(
      // !isDTR && reportRole !== "validator"
      !isDTR
        ? dateToday
        : previousDateToFilter === ""
        ? dateToday
        : moment(previousDateToFilter)
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDTR, isGDTR]);

  const handleDateFromChange = (e: any) => {
    setDateFrom(e);
  };

  const handleDateToChange = (e: any) => {
    setDateTo(e);
  };

  const handleClickPreview = () => {
    setOpenModal(false);
    dispatch(setIsRequesting(true));
  };

  const handleCloseReportModal = () => {
    setOpenModal(false);
  };

  const handleOpenReportModal = () => {
    dispatch(fetchGeneratedReports());
    dispatch(
      setpreviewData({
        isDTR: false,
        isGDTR: true,
        DTRfrom: moment(date_from).format("MM/DD/YYYY"),
        DTRto: moment(date_to).format("MM/DD/YYYY"),
        GTRfrom: "",
        GTRto: "",
      })
    );
    setOpenModal(true);
  };

  const handleGenerateReport = () => {
    if (reportRole === "sales_head") {
      dispatch(generateDailyReport(date_from, date_to, "manager"));
      history.push(`/franchising/report/preview`);
    } else {
      dispatch(generateDailyReport(date_from, date_to, "manager"));
      history.push(`/franchising/report/preview`);
    }
  };

  const flex = {
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-end",
  };

  const divider = {
    display: "inline-block",
    padding: "0 20px",
  };

  const headerLinks = canGenerateReport
    ? [
        {
          label: "View Generated Reports",
          icon: faBorderAll,
          action: handleOpenReportModal,
        },
        {
          label:
            reportRole === "validator"
              ? "Generate Daily Transaction Report"
              : "Generate Report",
          icon: faDownload,
          action: handleGenerateReport,
        },
      ]
    : [
        {
          label: "View Generated Reports",
          icon: faBorderAll,
          action: handleOpenReportModal,
        },
      ];

  let transactionLog = useSelector(
    (state: RootState) => state.report.transactionLogs
  );
  let isError = useSelector((state: RootState) => state.report.error);
  const [tableRows, setTableRows] = React.useState<any>([]);

  // let time = "16:00";

  let dateFrom = moment(date_from, "DD/MM/YYYY");
  let dateTo = moment(date_to, "DD/MM/YYYY");
  let maxDate = moment().endOf("day");

  //* 3601
  // let previousDate = dateFrom.subtract(1, "days");
  let dateStart = dateFrom.startOf("day");
  let dateEnd = dateTo.endOf("day");

  useEffect(() => {
    let tableContents: any = [];
    if (["supervisor", "manager", "sales_head"].includes(reportRole)) {
      // eslint-disable-next-line react-hooks/exhaustive-deps
      transactionLog = transactionLog.filter(({ submitted_at }) => submitted_at)
    } else {
      // eslint-disable-next-line react-hooks/exhaustive-deps
      transactionLog = transactionLog.filter(({ dtr_date }) => dtr_date);

      // Date&Time
      // eslint-disable-next-line react-hooks/exhaustive-deps
      dateStart = moment(date_from).subtract(1, "days").hours(16).minutes(1).seconds(0).milliseconds(0);
      // eslint-disable-next-line react-hooks/exhaustive-deps
      dateEnd = moment(date_to).hours(16).minutes(0).seconds(0).milliseconds(0);
    }
    
    if (!isError && transactionLog) {
      if (transactionLog.length > 0) {
        if (dateFrom.isSame(dateTo)) {
          if (reportRole === "validator") {
            tableContents = transactionLog.filter(
              ({ dtr_date }) =>
              moment(dtr_date).isBetween(dateStart, dateEnd) || moment(dtr_date).format("MM/DD/YYYY") ===
                dateFrom.format("MM/DD/YYYY")
            );
          } else if (reportRole === "supervisor" || reportRole === "manager") {
            tableContents = transactionLog.filter(
              ({ submitted_at }) =>
              moment(submitted_at).isBetween(dateStart, dateEnd) || moment(submitted_at).format("MM/DD/YYYY") ===
                dateFrom.format("MM/DD/YYYY")
            );
          }
        } else {
          if (
            (reportRole === "validator" || reportRole === "encoder") &&
            moment(date_from) <= moment().hours(16).minutes(0).seconds(0).milliseconds(0) &&
            moment(date_to) <= moment().hours(16).minutes(0).seconds(0).milliseconds(0) &&
            moment(date_from) <= moment(date_to)
          ) {
            tableContents = transactionLog.filter(({ dtr_date }) => moment(dtr_date).isBetween(dateStart, dateEnd));
          } else if (
            (reportRole === "supervisor" || reportRole === "manager") &&
            dateFrom <= maxDate &&
            dateTo <= maxDate &&
            dateFrom <= dateTo
          ) {
            tableContents = transactionLog.filter(
              ({ submitted_at }) =>
                moment(submitted_at).isBetween(dateStart, dateEnd) ||
                moment(submitted_at).format("MM/DD/YYYY") ===
                  dateFrom.format("MM/DD/YYYY") ||
                moment(submitted_at).format("MM/DD/YYYY") ===
                  dateTo.format("MM/DD/YYYY")
            );
          }
        }
      } else {
        tableContents = transactionLog;
      }
    }

    if (tableContents.length > 0) {
      if (reportRole === "validator" || reportRole === "encoder") {
        tableContents = tableContents.map((content: any) => {
          let actualStatus: string;
          let { status, franchise } = content;
          let {
            sales_user_id,
            // sales_first_name,
            // sales_last_name,
            reasons,
            remarks,
            frf_id,
            applicant_name,
            corporate_name,
            brand_name,
            client_id,
            tax_number,
          } = franchise;

          let newDate = new Date(content.dtr_date);
          newDate.setHours(0o0, 0o0, 0o0);
          switch (status) {
            case "RECEIVED":
              actualStatus = "Validation In-Process";
              break;
            case "VALIDATED":
              actualStatus = "Validation Complete";
              break;
            case "FOR_APPROVAL":
              actualStatus = "For Approval";
              break;
            case "FINAL_APPROVAL":
              actualStatus = "For Final Approval";
              break;
            case "APPROVAL_IN_PROCESS":
              actualStatus = "Approval In Process";
              break;
            case "RESUBMITTED_TO_SUPERVISOR":
              actualStatus = "Resubmitted To Supervisor";
              break;
            case "RETURNED_TO_VALIDATOR":
              actualStatus = "Returned";
              break;
            case "RETURNED":
              actualStatus = "Returned to Sales User";
              break;
            case null:
              actualStatus = "";
              break;
            default:
              actualStatus =
                status[0].toUpperCase() + status.slice(1).toLowerCase();
              break;
          }
          let { reason, remark } = getReasonRemarkForReport(
            status,
            reasons ?? [],
            remarks ?? [],
            userRoles,
            false
          );
            
          let decryptedFirstName, decryptedLastName;
          
          const chkData  = franchise.sales_first_name.includes('|');
          if(!chkData){
            decryptedFirstName = franchise.sales_first_name.trim()
            decryptedLastName = franchise.sales_last_name.trim()
          }
          else {
            decryptedFirstName = decryptCredentials(franchise.sales_first_name.trim());
            decryptedLastName = decryptCredentials(franchise.sales_last_name.trim());
          }
          

          console.log(`sales_user: ${decryptedFirstName} ${decryptedLastName}`);
          return {
            date: newDate,
            frf_no: String(frf_id).padStart(6, "0"),
            user: `${decryptedFirstName} ${decryptedLastName}`,
            franchisee_name: applicant_name,
            reg_corp_name: corporate_name,
            trade: brand_name,
            status_update: actualStatus,
            client_id: client_id ? client_id : "--",
            tin: tax_number,
            reason: reason,
            remarks: remark,
            sales_user_id,
          };
        });
        setTableRows(tableContents);
      } else if (
        reportRole === "supervisor" ||
        reportRole === "manager" ||
        reportRole === "sales_head"
      ) {
        tableContents = tableContents.map((content: any) => {
          let actualStatus: string;
          let {
            sales_user_id,
            sales_first_name,
            sales_last_name,
            reasons,
            remarks,
            frf_id,
            applicant_name,
            corporate_name,
            brand_name,
            client_id,
            industry_class_id,
            tax_number,
            status,
            is_refranchise,
            // status_updated_at,
            submitted_at,
          } = content;

          // let newDate = new Date(status_updated_at);
          let newDate = new Date(submitted_at);
          newDate.setHours(0o0, 0o0, 0o0);

          if (status) {
            actualStatus =
              status[0].toUpperCase() + status.slice(1).toLowerCase();
          } else {
            actualStatus = "";
          }

          let { reason, remark } = getReasonRemarkForReport(
            status,
            reasons ?? [],
            remarks ?? [],
            userRoles,
            false
          );

          function toTitleCase(str: string) {
            return str.replace(/\w\S*/g, function (txt: string) {
              return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
            });
          }
          let industryType = toTitleCase(industry_class_id);

          const decryptedFirstName = decryptCredentials(sales_first_name.trim());
          const decryptedLastName = decryptCredentials(sales_last_name.trim());

          return {
            date: newDate,
            frf_no: String(frf_id).padStart(6, "0"),
            user: `${decryptedFirstName} ${decryptedLastName}`,
            franchisee_name: applicant_name,
            reg_corp_name: corporate_name,
            trade: brand_name,
            status: actualStatus,
            new_or_returning: is_refranchise === "No" ? "New" : "Returning",
            client_id: client_id ? client_id : "--",
            industry: industryType,
            tin: tax_number,
            reason: reason,
            remarks: remark,
            sales_user_id,
          };
        });
        setTableRows(tableContents);
      }
    } else {
      if (reportRole === "sales_head") {
        const records = reportList.flatMap((content: any) => {
          return content.franchises.map((c: any) => {
            let actualStatus: string;
            let {
              sales_user_id,
              sales_first_name,
              sales_last_name,
              reasons,
              remarks,
              frf_id,
              applicant_name,
              corporate_name,
              brand_name,
              client_id,
              industry_class_id,
              tax_number,
              status,
              is_refranchise,
              // status_updated_at,
              submitted_at
            } = c;

            let newDate = new Date(submitted_at);
            newDate.setHours(0o0, 0o0, 0o0);

            if (status) {
              actualStatus =
                status[0].toUpperCase() + status.slice(1).toLowerCase();
            } else {
              actualStatus = "";
            }

            let { reason, remark } = getReasonRemarkForReport(
              status,
              reasons ?? [],
              remarks ?? [],
              userRoles,
              false
            );

            function toTitleCase(str: string) {
              return str.replace(/\w\S*/g, function (txt: string) {
                return (
                  txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
                );
              });
            }
            let industryType = toTitleCase(industry_class_id);

            return {
              date: newDate,
              frf_no: String(frf_id).padStart(6, "0"),
              user: `${sales_first_name.trim()} ${sales_last_name.trim()}`,
              franchisee_name: applicant_name,
              reg_corp_name: corporate_name,
              trade: brand_name,
              status: actualStatus,
              new_or_returning: is_refranchise === "No" ? "New" : "Returning",
              client_id: client_id ? client_id : "--",
              industry: industryType,
              tin: tax_number,
              reason: reason,
              remarks: remark,
              sales_user_id,
            };
          });
        });

        let results = records.filter(
          ({ date }) => moment(date).isBetween(dateStart, dateEnd) || moment(date).format("MM/DD/YYYY") === dateFrom.format("MM/DD/YYYY")
        );
 
        setTableRows(results);
      } else setTableRows([]);
    }
  }, [transactionLog, date_from, date_to, userRoles, reportRole]);

  const checkToken = (accessToken: any) => {
    let URL = apiURL.userManagement + '/underwriting/user/check'

    return fetch(URL, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + accessToken,
        }}).then(async (res: any) => {
            if (!res.ok) {
                let respjson = await res.json()
                let errormessage = respjson.error.message
                throw Error(errormessage)
            }
            return res ? res.json() : null
        })
  }

  const isTokenValid = async () => {
    let isValid = true,
    isPmValid = true,
    isApiValid = true;
    
    if (!localStorage.getItem('api_token') || !localStorage.getItem('pm_token')) {
      isValid = false;
    } else if (
      localStorage.getItem('api_token') &&
      localStorage.getItem('pm_token')
    ) {
      try {
        let api_token = await checkToken(localStorage.getItem('api_token'));
        let pm_token = await checkToken(localStorage.getItem('pm_token'));
        if (!api_token.status) {
          isApiValid = false;
        }
        if (!pm_token.status) {
          isPmValid = false;
        }
      } catch (error) {
        console.log(error, 'error');
        isPmValid = false;
        isApiValid = false;
      }
    }

    console.log('TOKEN VALIDITY', isValid, isPmValid, isApiValid);
    if (!isValid || !isPmValid || !isApiValid) {
      // console.log('INVALID TOKEN');
      // var a = '../customer-care/index.html#/franchising' + '/invalidSession';
      // window.location.href = a;
      //onLogout();
      setViewExpiredModal(true);
    }
  };

  React.useEffect(() => {
    isTokenValid();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token, reportRole])

  return (
    <>
      {/* {!isRequesting &&
        (reportRole === "validator" ||
          reportRole === "manager" ||
          reportRole === "supervisor" ||
          reportRole === "sales_head") && (
          <Modal
            fullWidth={false}
            maxWidth="md"
            open={openModal}
            onClose={handleCloseReportModal}
          >
            <ViewReport onClickView={handleClickPreview} role="manager" />
          </Modal>
        )} */}
      {(reportRole === "encoder" && (
        <>
          <PageSubHeader title="Franchise Report"></PageSubHeader>
          <ViewReport onClickView={handleClickPreview} role="manager" />
        </>
      )) ||
        ((reportRole === "sales_head" ||
          reportRole === "validator" ||
          reportRole === "supervisor" ||
          reportRole === "manager") && (
          <>
            <PageSubHeader
              title={
                reportRole === "validator"
                  ? "Daily Transaction Report"
                  : "Franchise Report"
              }
              buttons={headerLinks}
            ></PageSubHeader>
            {["encoder", "validator"].includes(reportRole) ? (
              <Typography
                className="text-16"
                style={{
                  padding: "0rem 3rem",
                  backgroundColor: "white",
                  marginTop: -10,
                  fontSize: 16,
                  color: "#272E4C",
                  fontFamily: "usual, sans-serif",
                }}
              >
                Daily Transaction Report captures all transactions from 4:01PM
                of previous day until 4:00PM of the same day. Transactions
                received beyond the cut off time which is 4PM shall be
                considered as next day transaction.
              </Typography>
            ) : null}

            <div className="main vni-pb-8 vni-px-12 tempFont vni-bg-white">
              <Grid
                container
                justifyContent={"flex-end"}
                alignItems="center"
                style={{ padding: "20px 0" }}
              >
                <Grid item xs={12} style={{ textAlign: "right" }}>
                  <div style={flex}>
                    <span style={divider}>Select Date from</span>
                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <DatePicker
                        className="vni-date-picker"
                        format="MM/dd/yyyy"
                        maxDate={date_to ?? undefined}
                        value={date_from}
                        onChange={handleDateFromChange}
                        slotProps={{
                          textField: {
                            variant: 'outlined',
                            placeholder: "mm/dd/yyyy",
                          },
                        }}
                      />
                    </LocalizationProvider>

                    <span style={divider}>to</span>

                    <LocalizationProvider dateAdapter={AdapterDateFns}>
                      <DatePicker
                        className="vni-date-picker"
                        format="MM/dd/yyyy"
                        value={date_to}
                        minDate={date_from ?? undefined}
                        maxDate={new Date()}
                        onChange={handleDateToChange}
                        slotProps={{
                          textField: {
                            variant: 'outlined',
                            placeholder: "mm/dd/yyyy",
                          },
                        }}
                      />
                    </LocalizationProvider>
                  </div>
                </Grid>
              </Grid>

              <div className="vni-table">
                <div className="vni-flex-grow" style={{ maxWidth: "100%" }}>
                  <div className="scrollable">
                    <div
                      className="vni-relative vni-last-cell-index consistent-date"
                      style={{ width: "calc(100vw - 152px)", maxWidth: "100%" }}
                    >
                      <TableComponent
                        rows={tableRows}
                        columns={
                          reportRole === "validator"
                            ? columns
                            : columnsHigherManagement
                        }
                        columnExtensions={
                          reportRole === "validator"
                            ? columnExtensions
                            : columnExtensionsHigherManagement
                        }
                        filteringStateColumnExtensions={
                          reportRole === "validator"
                            ? filteringStateColumnExtensions
                            : filteringStateColumnExtensions2
                        }
                        enableColumnFilter
                        enablePaging
                        customNoDataMessage={"No Records Found"}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        ))}

      {viewExpiredModal === true && (
        <Dialog 
            id="session-modal-expired"
            maxWidth='xs'
            open={viewExpiredModal}
            onClose={() => {
            dispatch(logout())
            window.location.replace('../index.html#/')
            }}
            className="vni-m-auto"
        >
            <DialogTitle className="vni-px-5 vni-pt-3 vni-mb-8">
              <h2 className="title">Session Expired</h2>
            </DialogTitle>
            <DialogContent>
              <p className="vni-mb-8">
              Your account has been logged out. Please try logging in again.
              </p>
            </DialogContent>
            <DialogActions className="vni-flex d-flex-center">
              <button className="CustomPrimaryButton vni-mr-5" data-cy="session-timeout-logout-btn" onClick={() => {
                  dispatch(logout())
                  window.location.replace('../index.html#/')

                  }}>Okay</button>
            </DialogActions>
        </Dialog>
        )};
    </>
  );
};

export default ManagerReport;
