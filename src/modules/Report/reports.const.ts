const columns = [
	{
		name: "frf_no",
		title: "FRF No.",
		filterEnabled: true,
	},
	{
		name: "date",
		title: "Date",
		filterEnabled: true,
	},
	{
		name: "user",
		title: "Sales User",
		filterEnabled: true,
	},
	{
		name: "franchisee_name",
		title: "Franchisee Name",
		filterEnabled: true,
	},
	{
		name: "reg_corp_name",
		title: "Registered Corporate Name",
		filterEnabled: true,
	},
	{
		name: "trade",
		title: "Trade/Brand Name",
		filterEnabled: true,
	},
	{
		name: "status_update",
		title: "Status Update",
		filterEnabled: true,
		sortEnabled: false,
	},
	{
		name: "client_id",
		title: "Client ID",
		filterEnabled: true,
		sortEnabled: false,
	},
	{
		name: "tin",
		title: "Tax Identification Number",
		filterEnabled: true,
		sortEnabled: false,
	},
	{
		name: "reason",
		title: "Reason",
		filterEnabled: true,
		sortEnabled: false,
	},
	{
		name: "remarks",
		title: "Remarks",
		filterEnabled: true,
		sortEnabled: false,
	},
]

const columnExtensions = [
	{
		columnName: "frf_no",
		wordWrapEnabled: true,
	},
	{
		columnName: "date",
		width: "550",
	},
	{
		columnName: "user",
		wordWrapEnabled: true,
	},
	{
		columnName: "franchisee_name",
		wordWrapEnabled: true,
	},
	{
		columnName: "reg_corp_name",
	},
	{
		columnName: "trade",
	},
	{
		columnName: "status_update",
	},
	{
		columnName: "client_id",
	},
	{
		columnName: "tin",
	},
	{
		columnName: "reason",
	},
	{
		columnName: "remarks",
	},
]

const columnsHigherManagement = [
	{
		name: "frf_no",
		title: "FRF No.",
		filterEnabled: true,
	},
	{
		name: "date",
		title: "Date",
		filterEnabled: true,
	},
	{
		name: "user",
		title: "Sales User",
		filterEnabled: true,
	},
	{
		name: "franchisee_name",
		title: "Franchisee Name",
		filterEnabled: true,
	},
	{
		name: "reg_corp_name",
		title: "Registered Corporate Name",
		filterEnabled: true,
	},
	{
		name: "trade",
		title: "Trade/Brand Name",
		filterEnabled: true,
	},
	{
		name: "status",
		title: "Status",
		filterEnabled: true,
		sortEnabled: false,
	},
	{
		name: "new_or_returning",
		title: "New/Returning",
		filterEnabled: true,
		sortEnabled: false,
	},
	{
		name: "client_id",
		title: "Client ID",
		filterEnabled: true,
		sortEnabled: false,
	},
	{
		name: "industry",
		title: "Industry Type",
		filterEnabled: true,
		sortEnabled: false,
	},
	{
		name: "tin",
		title: "Tax Identification Number",
		filterEnabled: true,
		sortEnabled: false,
	},
	{
		name: "reason",
		title: "Reason",
		filterEnabled: true,
		sortEnabled: false,
	},
	{
		name: "remarks",
		title: "Remarks",
		filterEnabled: true,
		sortEnabled: false,
	},
]

const columnExtensionsHigherManagement = [
	{
		columnName: "frf_no",
		wordWrapEnabled: true,
	},
	{
		columnName: "date",
		width: "550",
	},
	{
		columnName: "user",
		wordWrapEnabled: true,
	},
	{
		columnName: "franchisee_name",
		wordWrapEnabled: true,
	},
	{
		columnName: "reg_corp_name",
	},
	{
		columnName: "trade",
	},
	{
		columnName: "status",
	},
	{
		columnName: "new_or_returning",
	},
	{
		columnName: "client_id",
	},
	{
		columnName: "industry",
	},
	{
		columnName: "tin",
	},
	{
		columnName: "reason",
	},
	{
		columnName: "remarks",
	},
]

const customDateSearchFilter = (value: any, filter: any) => {
	const toLowerCase = (data: any) => String(data).toLowerCase()
	return toLowerCase(value).startsWith(toLowerCase(filter.value))
}

const customStatusFilter = (value: any, filter: any) => {
	return value.toLowerCase().includes(filter.value.toLowerCase())
}

// const customUserSearchFilter = (value: any, filter: any) => {
// 	console.log("user search", value, "user filter", filter)
// 	const toLowerCase = (data: any) => String(data).toLowerCase()
// 	return toLowerCase(value).startsWith(toLowerCase(filter.value))
// }

const filteringStateColumnExtensions = [
	{
		columnName: "date",
		predicate: customDateSearchFilter,
		filteringEnabled: true,
	},
	// {
	// 	columnName: "user",
	// 	predicate: customUserSearchFilter,
	// 	filteringEnabled: true,
	// },
	{
		columnName: "status_update",
		predicate: customStatusFilter,
		filteringEnabled: true,
	},
	{
		columnName: "reason",
		filteringEnabled: false,
	},
	{
		columnName: "remarks",
		filteringEnabled: false,
	},
]

const filteringStateColumnExtensions2 = [
	{
		columnName: "date",
		predicate: customDateSearchFilter,
		filteringEnabled: true,
	},
	{
		columnName: "status",
		predicate: customStatusFilter,
		filteringEnabled: true,
	},
	{
		columnName: "remarks",
		filteringEnabled: false,
	},
]

export {
	columns,
	columnExtensions,
	columnsHigherManagement,
	columnExtensionsHigherManagement,
	filteringStateColumnExtensions,
	filteringStateColumnExtensions2,
}
