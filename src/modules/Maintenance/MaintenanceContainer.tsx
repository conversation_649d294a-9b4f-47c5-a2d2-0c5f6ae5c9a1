import React, { Fragment, useEffect, useState } from "react";
import { useLocation, useHistory } from "react-router-dom";
import { useDispatch } from "react-redux";
import { ReasonForReturnList } from "./ReasonForReturnList";
import { ReasonForDisapprovalList } from "./ReasonForDisapprovalList";
import { Modal } from '../../shared/components/Modal'
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  Typography,
} from "@mui/material";
import { RootState } from "../../shared/reducers/rootReducer";
import { useSelector } from "react-redux";
import { PreviewStyles } from "../../shared/components/ViewPDFReport/style";
import {
  fetchReturnReasonList,
  fetchDisapproveReasonList,
  deleteReasonItem,
  clearModalContent,
  setSelectedID,
  setReasonDesc,
  setIsReasonExist
} from '../../shared/reducers/MaintenanceSlice'
import { apiURL } from "../../utils/Environment";
import { logout } from "../../shared/reducers/LoginSlice";

const Maintenance: React.FC = () => {

  const classes = PreviewStyles()
  const returnReasonListData = useSelector((state: RootState) => state.maintenance.returnReasonList);
  const disapproveReasonListData = useSelector((state: RootState) => state.maintenance.disapproveReasonList);
  const validationContent = useSelector((state: RootState) => state.maintenance.modalContent);
  const selectedID = useSelector((state: RootState) => state.maintenance.selectedID);
  const isReasonExist = useSelector((state: RootState) => state.maintenance.isReasonExist);

  const [openModal, setOpenModal] = useState(false);
  const { isMaintainer } = useSelector((state: RootState) => state.login.userRoles);
  const token = useSelector((state: RootState) => state.login.token);
  const role = useSelector((state: RootState) => state.login.roleAPI);

  const dispatch = useDispatch();
  const history = useHistory();
  let location = useLocation();

  const [modalContent, setModalContent] = React.useState<any>({
    title: "",
    description: "",
    button: <></>
  });
  const { button } = modalContent;
  const [viewExpiredModal, setViewExpiredModal] = React.useState(false);


  useEffect(() => {
    window.scrollTo(0, 0);
    if (token && role) {
      dispatch(fetchReturnReasonList());
      dispatch(fetchDisapproveReasonList());
      if (!isMaintainer) {
        history.push("/franchising/")
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location, token, role]);

  useEffect(() => {
    if (validationContent.desc) {
      setModalContent(
        {
          title: validationContent.title,
          description: validationContent.desc,
          button: <Button data-cy="okay-btn" className={classes.rightButton} onClick={handleCloseModal}>Okay</Button>
        }
      )
      setOpenModal(true)
    } else if (selectedID !== "") {
      setModalContent(
        {
          title: "Remove Reason",
          description: "Are you sure you want to remove this reason?",
          button: <>
            <Button data-cy="no-btn"className={classes.leftButtonOutline} onClick={handleCloseModal}>No</Button>
            <Button data-cy="yes-btn"className={classes.rightButton} onClick={() => handleRemove(selectedID)}>Yes</Button>
          </>
        }
      )
    } else if (isReasonExist) {
      setModalContent(
        {
          title: "Reason Already Exists",
          description: "The reason that you want to add already exists in the list.",
          button: <Button data-cy="okay-btn" className={classes.rightButton} onClick={handleCloseModal}>Okay</Button>
        }
      )
      setOpenModal(true)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [validationContent, selectedID, isReasonExist])


  const handleCloseModal = () => {
    dispatch(setIsReasonExist(false))
    dispatch(fetchReturnReasonList());
    dispatch(clearModalContent());
    setModalContent({
      title: "",
      description: "",
      button: <></>
    })
    setOpenModal(false)
  }

  const handleDeleteItemID = (id: string, reasonDesc: Object) => {
    dispatch(setSelectedID(id))
    setOpenModal(true)
    dispatch(setReasonDesc(reasonDesc));
  }

  const handleRemove = (item: any) => {
    dispatch(deleteReasonItem(item))
    setModalContent({
      title: "",
      description: "",
      button: <></>
    })
    setOpenModal(false)
  }

  const checkToken = (accessToken: any) => {
    let URL = apiURL.userManagement + '/underwriting/user/check'

    return fetch(URL, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + accessToken,
        }}).then(async (res: any) => {
            if (!res.ok) {
                let respjson = await res.json()
                let errormessage = respjson.error.message
                throw Error(errormessage)
            }
            return res ? res.json() : null
        })
  }

  const isTokenValid = async () => {
    let isValid = true,
    isPmValid = true,
    isApiValid = true;
    
    if (!localStorage.getItem('api_token') || !localStorage.getItem('pm_token')) {
      isValid = false;
    } else if (
      localStorage.getItem('api_token') &&
      localStorage.getItem('pm_token')
    ) {
      try {
        let api_token = await checkToken(localStorage.getItem('api_token'));
        let pm_token = await checkToken(localStorage.getItem('pm_token'));
        if (!api_token.status) {
          isApiValid = false;
        }
        if (!pm_token.status) {
          isPmValid = false;
        }
      } catch (error) {
        console.log(error, 'error');
        isPmValid = false;
        isApiValid = false;
      }
    }

    console.log('TOKEN VALIDITY', isValid, isPmValid, isApiValid);
    if (!isValid || !isPmValid || !isApiValid) {
      // console.log('INVALID TOKEN');
      // var a = '../customer-care/index.html#/franchising' + '/invalidSession';
      // window.location.href = a;
      //onLogout();
      setViewExpiredModal(true);
    }
  };

  React.useEffect(() => {
    isTokenValid()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token])

  return (
    <Fragment>
      {
        isMaintainer &&
        <>
          <Modal
            fullWidth={false}
            maxWidth='md'
            open={openModal}
            onClose={handleCloseModal}
          >
            <Grid>
              <Typography variant="h5" className={classes.modalTitle}>{modalContent.title}</Typography>
              <Typography className={classes.modalMessage}>{modalContent.description}</Typography>
              <Grid className="btn-group" style={{ textAlign: 'center' }}>
                {button}
              </Grid>
            </Grid>
          </Modal>

          <Box className="main vni-py-8 vni-px-12">
            <h1 className="vni-font-bold vni-text-3xl">Maintenance</h1>
            <ReasonForReturnList
              selectedID={selectedID}
              handleDeleteItemID={handleDeleteItemID}
              reasonList={returnReasonListData}
            />
            <ReasonForDisapprovalList
              selectedID={selectedID}
              handleDeleteItemID={handleDeleteItemID}
              reasonList={disapproveReasonListData}
            />
          </Box>
        </>
      }

      
      {viewExpiredModal === true && (
        <Dialog 
            id="session-modal-expired"
            maxWidth='xs'
            open={viewExpiredModal}
            onClose={() => {
            dispatch(logout())
            window.location.replace('../index.html#/')
            }}
            className="vni-m-auto"
        >
            <DialogTitle className="vni-px-5 vni-pt-3 vni-mb-8">
              <h2 className="title">Session Expired</h2>
            </DialogTitle>
            <DialogContent>
              <p className="vni-mb-8">
              Your account has been logged out. Please try logging in again.
              </p>
            </DialogContent>
            <DialogActions className="vni-flex d-flex-center">
              <button className="CustomPrimaryButton vni-mr-5" data-cy="session-timeout-logout-btn" onClick={() => {
                  dispatch(logout())
                  window.location.replace('../index.html#/')

                  }}>Okay</button>
            </DialogActions>
        </Dialog>
        )};
    </Fragment>
  );
};

export default Maintenance;
