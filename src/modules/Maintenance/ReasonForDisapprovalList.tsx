import React, { useEffect, useState, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "../../shared/reducers/rootReducer";
import { Props } from '../../shared/models/Maintenance';
import {
  Box,
  TextField,
  ListItem,
  ListItemIcon,
  ListItemText
} from "@mui/material";

import ClearIcon from '@mui/icons-material/Clear';
import EditIcon from '@mui/icons-material/Edit';
import {
  saveNewReason,
  setReasonDesc,
  setIsDisapproveEdit,
  setDisapproveEditID,
  updateReasonItem,
  setSelectedID,
  setIsReasonExist,
  setIsEditSuccess,
  setIsAddSuccess,
  setIsDeleteSuccess,
  setDisapproveInputs
} from '../../shared/reducers/MaintenanceSlice'


export const ReasonForDisapprovalList: React.FC<Props> = (props: Props) => {

  const { selectedID, handleDeleteItemID, reasonList} = props

  const isAddSuccess = useSelector((state: RootState) => state.maintenance.isAddSuccess);
  const isEditSuccess = useSelector((state: RootState) => state.maintenance.isEditSuccess);
  const isDeleteSuccess = useSelector((state: RootState) => state.maintenance.isDeleteSuccess);
  const disapproveEditID = useSelector((state: RootState) => state.maintenance.disapproveEditID);
  const isDisapproveEdit = useSelector((state: RootState) => state.maintenance.isDisapproveEdit);
  const reasonDesc = useSelector((state: RootState) => state.maintenance.reasonDesc);
  const {canAddSetting, canEditSetting, canArchiveSetting} = useSelector((state: RootState) => state.login.userPermissions);
  
  let initialState: any = {
    reason_for_disapprove: ""
  }
  const [disapproveReason, setDisapproveReason] = useState<any>(initialState);
  const [reasonItems, setReasonItems] = useState<any>([]);
  const [itemToEdit, setItemToEdit] = useState<any>(null);
  const inputRef = useRef<HTMLInputElement>(null)
  const dispatch = useDispatch();

  const clearValues = () => {
    setDisapproveReason(initialState)
    dispatch(setIsDisapproveEdit(false))
    setItemToEdit(null)
    dispatch(setDisapproveEditID(""))
    dispatch(setSelectedID(""))
    dispatch(setDisapproveInputs(""))
  };

  //CLEAR INPUT FIELD
  useEffect(() => {
    if (reasonDesc === "disapprove") {
      if (isAddSuccess) {
        setDisapproveReason(initialState)
        setItemToEdit(null)
        dispatch(setIsAddSuccess(false))
      } else if (isEditSuccess) {
        clearValues()
        dispatch(setIsEditSuccess(false))
      } else if (isDeleteSuccess && selectedID === disapproveEditID) {
        clearValues()
        dispatch(setIsDeleteSuccess(false))
      } else if (isDeleteSuccess) {
        dispatch(setSelectedID(""))
        dispatch(setIsDeleteSuccess(false))
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAddSuccess, isEditSuccess, isDeleteSuccess]);

  const handleAddTextbox = (e: any) => {
    const text = e.target.value
    setDisapproveReason({ ...disapproveReason, reason_for_disapprove: text })
    dispatch(setDisapproveInputs(text.trim()))
  };

  //MATCH FILTER
  let newList = reasonList.filter((item: any) => {
    let existingValue = item.reason_for_disapprove
    let newValue = disapproveReason.reason_for_disapprove
    let result = RegExp('^' + newValue + '$', 'gi').test(existingValue)
    if (result) {
      return true
    } else {
      return false
    }
  })

  //SAVE FUNCTION 
  const handleSaveClick = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    dispatch(setReasonDesc("disapprove"));

    let reason = disapproveReason.reason_for_disapprove.trim().split(" ")
    let reasonItem1 = reason.filter((item: any) => item !== "").length
    if (newList.length === 0) {
      if (disapproveReason.reason_for_disapprove !== "" && reasonItem1 !== 0) {
        dispatch(saveNewReason(disapproveReason));
      }
    } else {
      dispatch(setIsReasonExist(true))
    }
  }

  //EDIT FUNCTION 
  const handleEdit = (i: number, id: string) => {
    let data = reasonList[i]
    const { reason_for_disapprove } = data
    setDisapproveReason({ ...disapproveReason, _id: id, reason_for_disapprove: reason_for_disapprove })

    if (inputRef.current) {
      inputRef.current.focus();
    }

    setItemToEdit(id)
    dispatch(setIsDisapproveEdit(true))
    dispatch(setDisapproveEditID(id))
  }

  //UPDATE FUNCTION 
  const handleUpdate = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    dispatch(setReasonDesc("disapprove"));

    let reasonFilter = reasonList.filter((item: any) => {
      let existingValue = item.reason_for_disapprove
      let newValue = disapproveReason.reason_for_disapprove
      return (
        item._id === disapproveReason._id && RegExp('^' + newValue + '$', 'gi').test(existingValue)
      )
    })

    let reason = disapproveReason.reason_for_disapprove.trim().split(" ")
    let reasonItem1 = reason.filter((item: any) => item !== "").length

    if (reasonFilter.length === 0 && isDisapproveEdit ) {
      if (newList.length === 0) {
        if (disapproveReason.reason_for_disapprove !== "" && reasonItem1 !== 0) {
          dispatch(updateReasonItem(disapproveReason));
        }
      } else {
        dispatch(setIsReasonExist(true))
      }
    }
  }

  const handleCancelBtn = () => {
    clearValues()
    dispatch(setIsDisapproveEdit(false))
  }


  useEffect(() => {
    const items = reasonList.map((item: any, i: number) => {
      let reasonDesc = "disapprove"
      let newIndex = reasonList.findIndex((list: any) => list._id === itemToEdit)
      return (
        <ListItem color="grey" className={newIndex === i ? `vni-reason-list selected-item` : "vni-reason-list"} key={item._id} >
          <ListItemText className="vni-reason-text"
            primary={item.reason_for_disapprove}
          />
          {canEditSetting && 
              <ListItemIcon className="vni-reason-edit-icon" data-cy={`disapprove-edit-icon-${i+1}`} onClick={() => handleEdit(i, item._id)}>
                <EditIcon />
              </ListItemIcon>
          }
          {canArchiveSetting && 
              <ListItemIcon className="vni-reason-close-icon" data-cy={`disapprove-dekete-icon-${i+1}`} onClick={() => handleDeleteItemID(item._id, reasonDesc)}>
                <ClearIcon />
              </ListItemIcon>
          }
        </ListItem>
      )
    }
    )
    setReasonItems(items)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [reasonList, itemToEdit]);


  return (
      <>
        <Box component="span">
          <strong className="vni-block vni-mt vni-mb">
            Reason for Disapproval
          </strong>
        </Box>
          { (canAddSetting || canEditSetting) &&
              <>
                <Box className="vni-search vni-mTop">
                  <form className="vni-flex vni-w-1/2" onSubmit={isDisapproveEdit && canEditSetting ? handleUpdate : canAddSetting ? handleSaveClick : handleUpdate}>
                    <Box className="add-icon vni-flex-grow">
                      <TextField
                        name="reason"
                        data-cy="disapprove-reason-input"
                        className={"w-100"}
                        value={disapproveReason.reason_for_disapprove}
                        variant="outlined"
                        onChange={handleAddTextbox}
                        inputRef={inputRef}
                      />
                    </Box>
                    <button
                      type="submit"
                      data-cy="disapprove-add-update-btn"
                      className="CustomPrimaryButton vni-maintenance-btn"
                      disabled={false}
                    >
                      {isDisapproveEdit && canEditSetting ? "Update" : canAddSetting ? "Add" : "Update"}
                    </button>
                  </form>

                  {isDisapproveEdit && canEditSetting && <>
                                  <button
                                  type="submit"
                                  data-cy="disapprove-cancel-button"
                                  className="CustomPrimaryButton vni-maintenance-btn"
                                  disabled={false}
                                  onClick={handleCancelBtn}
                                  >
                                  Cancel
                                </button>
                              </>
                    }
                </Box>
              </>
            }

      <Box className="vni-list-container">
        {reasonItems}
      </Box>
    </>);
};