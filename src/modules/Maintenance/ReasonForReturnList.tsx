import React, { useEffect, useState, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "../../shared/reducers/rootReducer";
import { Props } from '../../shared/models/Maintenance';
import {
  Box,
  TextField,
  ListItem,
  ListItemIcon,
  ListItemText
} from "@mui/material";

import ClearIcon from '@mui/icons-material/Clear';
import EditIcon from '@mui/icons-material/Edit';
import {
  saveNewReason,
  setReasonDesc,
  setIsReturnEdit,
  setReturnEditID,
  updateReasonItem,
  setSelectedID,
  setIsReasonExist,
  setIsEditSuccess,
  setIsAddSuccess,
  setIsDeleteSuccess,
  setReturnInputs,
} from '../../shared/reducers/MaintenanceSlice'


export const ReasonForReturnList: React.FC<Props> = (props: Props) => {

  const { selectedID, handleDeleteItemID, reasonList} = props

  const isAddSuccess = useSelector((state: RootState) => state.maintenance.isAddSuccess);
  const isEditSuccess = useSelector((state: RootState) => state.maintenance.isEditSuccess);
  const isDeleteSuccess = useSelector((state: RootState) => state.maintenance.isDeleteSuccess);
  const returnEditID = useSelector((state: RootState) => state.maintenance.returnEditID);
  const isReturnEdit = useSelector((state: RootState) => state.maintenance.isReturnEdit);
  const reasonDesc = useSelector((state: RootState) => state.maintenance.reasonDesc);
  const {canAddSetting, canEditSetting, canArchiveSetting} = useSelector((state: RootState) => state.login.userPermissions);

  let initialState: any = {
    reason_for_return: ""
  }
  const [returnReason, setReturnReason] = useState<any>(initialState);
  const [reasonItems, setReasonItems] = useState<any>([]);
  const [itemToEdit, setItemToEdit] = useState<any>(null);
  const inputRef = useRef<HTMLInputElement>(null)
  const dispatch = useDispatch();

  const clearValues = () => {
    setReturnReason(initialState)
    dispatch(setIsReturnEdit(false))
    setItemToEdit(null)
    dispatch(setReturnEditID(""))
    dispatch(setSelectedID(""))
    dispatch(setReturnInputs(""))
  };

  //CLEAR INPUT FIELD
  useEffect(() => {
    if (reasonDesc === "return") {
      if (isAddSuccess) {
        setReturnReason(initialState)
        setItemToEdit(null)
        dispatch(setIsAddSuccess(false))
      } else if (isEditSuccess) {
        clearValues()
        dispatch(setIsEditSuccess(false))
      } else if (isDeleteSuccess && selectedID === returnEditID) {
        clearValues()
        dispatch(setIsDeleteSuccess(false))
      } else if (isDeleteSuccess) {
        dispatch(setSelectedID(""))
        dispatch(setIsDeleteSuccess(false))
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAddSuccess, isEditSuccess, isDeleteSuccess]);

  const handleAddTextbox = (e: any) => {
    const text = e.target.value
    setReturnReason({ ...returnReason, reason_for_return: text })
    dispatch(setReturnInputs(text.trim()))
  };

  //MATCH FILTER
  let newList = reasonList.filter((item: any) => {
    let existingValue = item.reason_for_return
    let newValue = returnReason.reason_for_return
    let result = RegExp('^' + newValue + '$', 'gi').test(existingValue)
    if (result) {
      return true
    } else {
      return false
    }
  })

  //SAVE FUNCTION 
  const handleSaveClick = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    dispatch(setReasonDesc("return"));

    let reason = returnReason.reason_for_return.trim().split(" ")
    let reasonItem1 = reason.filter((item: any) => item !== "").length
    if (newList.length === 0) {
      if (returnReason.reason_for_return !== "" && reasonItem1 !== 0) {
        dispatch(saveNewReason(returnReason));
      }
    } else {
      dispatch(setIsReasonExist(true))
    }
  }

  //EDIT FUNCTION 
  const handleEdit = (i: number, id: string) => {
    let data = reasonList[i]
    const { reason_for_return } = data
    setReturnReason({ ...returnReason, _id: id, reason_for_return: reason_for_return })

    if (inputRef.current) {
      inputRef.current.focus();
    }

    setItemToEdit(id)
    dispatch(setIsReturnEdit(true))
    dispatch(setReturnEditID(id))
  }

  //UPDATE FUNCTION 
  const handleUpdate = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    dispatch(setReasonDesc("return"));

    let reasonFilter = reasonList.filter((item: any) => {
      let existingValue = item.reason_for_return
      let newValue = returnReason.reason_for_return
      return (
        item._id === returnReason._id && RegExp('^' + newValue + '$', 'gi').test(existingValue)
      )
    })

    let reason = returnReason.reason_for_return.trim().split(" ")
    let reasonItem1 = reason.filter((item: any) => item !== "").length

    if (reasonFilter.length === 0 && isReturnEdit) {
      if (newList.length === 0) {
        if (returnReason.reason_for_return !== "" && reasonItem1 !== 0) {
          dispatch(updateReasonItem(returnReason));
        }
      } else {
        dispatch(setIsReasonExist(true))
      }
    }
  }

  const handleCancelBtn = () => {
    clearValues()
    dispatch(setIsReturnEdit(false))
  }

  useEffect(() => {
    const items = reasonList.map((item: any, i: number) => {
      let reasonDesc = "return"
      let newIndex = reasonList.findIndex((list: any) => list._id === itemToEdit)
      return (
        <ListItem color="grey" className={newIndex === i ? `vni-reason-list selected-item` : "vni-reason-list"} key={item._id} >
          <ListItemText className="vni-reason-text"
            primary={item.reason_for_return}
          />
            {canEditSetting && 
                <ListItemIcon className="vni-reason-edit-icon" data-cy={`return-edit-icon-${i+1}`} onClick={() => handleEdit(i, item._id)}>
                  <EditIcon />
                </ListItemIcon>
            }
            {canArchiveSetting && 
                <ListItemIcon className="vni-reason-close-icon" data-cy={`return-delete-icon-${i+1}`} onClick={() => handleDeleteItemID(item._id, reasonDesc)}>
                  <ClearIcon />
                </ListItemIcon>
            }
        </ListItem>
      )
    }
    )
    setReasonItems(items)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [reasonList, itemToEdit]);


  return (
    <>
      <Box component="span">
        <strong className="vni-block vni-mt vni-mb">
          Reason for Return
        </strong>
      </Box>

      { (canAddSetting || canEditSetting) && 
          <>
            <Box className="vni-search vni-mTop">
              <form className="vni-flex vni-w-1/2" onSubmit={isReturnEdit && canEditSetting ? handleUpdate : canAddSetting ? handleSaveClick : handleUpdate}>
                <Box className="add-icon vni-flex-grow">
                  <TextField
                    name="reason"
                    data-cy="return-reason-input"
                    className={"w-100"}
                    value={returnReason.reason_for_return}
                    variant="outlined"
                    onChange={handleAddTextbox}
                    inputRef={inputRef}
                  />
                </Box>
                <button
                  type="submit"
                  data-cy="return-add-update-btn"
                  className="CustomPrimaryButton vni-maintenance-btn"
                  disabled={false}
                >
                  {isReturnEdit && canEditSetting ? "Update" : canAddSetting ? "Add" : "Update"}
                </button>
              </form>
              {isReturnEdit && canEditSetting && <>
                              <button
                              type="submit"
                              data-cy="return-cancel-button"
                              className="CustomPrimaryButton vni-maintenance-btn"
                              disabled={false}
                              onClick={handleCancelBtn}
                            >
                              Cancel
                            </button>
                          </>
                }
            </Box>
          </>
      }

      <Box className="vni-list-container">
        {reasonItems}
      </Box>
    </>);
};