import React from 'react'
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../shared/reducers/rootReducer';
import { CircularProgress} from '@mui/material';
import { logout } from '../../shared/reducers/LoginSlice';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from "@mui/material";

interface SessionExpired {
    token: string;
}

const SessionExpired = (props: SessionExpired) => {
  const dispatch = useDispatch();
  const token = props.token;
  const session = useSelector((state:RootState) => state.login.auth.sessionExpired)

    return (
        <React.Fragment>
            <Dialog 
                    id="session-modal"
                    maxWidth='xs'
                    open={!token? true : false}
                    onClose={() => {
                    dispatch(logout())
                    window.location.replace('../index.html#/')
                    }}
                    className="vni-m-auto"
                >
                    <DialogTitle className="vni-px-5 vni-pt-3 vni-mb-8">
                    <h2 className="title">Session Expired</h2>
                    </DialogTitle>
                    <DialogContent>
                    <p className="vni-mb-8">
                    Your account has been logged out. Please try logging in again.
                    </p>
                    </DialogContent>
                    <DialogActions className="vni-flex d-flex-center">
                    <button className="CustomPrimaryButton vni-mr-5" data-cy="session-timeout-logout-btn" onClick={() => {
                        dispatch(logout())
                        window.location.replace('../index.html#/')
              
                        }}>Okay</button>
                    </DialogActions>
                </Dialog>
        </React.Fragment>
    )
}

export default SessionExpired