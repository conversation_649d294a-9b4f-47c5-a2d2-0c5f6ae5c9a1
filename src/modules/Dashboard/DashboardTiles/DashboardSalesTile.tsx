import React, { useEffect, useState } from "react"
import { useLocation } from "react-router-dom"
import { useSelector, useDispatch } from "react-redux"
import { RootState } from "../../../shared/reducers/rootReducer"
import { changeTileSales } from "../../../shared/reducers/DashboardSlice"
import { salesUserStatuses } from "../../../shared/models/Franchise"
import moment from "moment"

interface CounterProps {
	role: any
}

interface TileProps {
	indexSales: number
	onClick: any
	isActiveSales: any
	name: string
	count: number
	role: any
}

export const DashboardSalesTile: React.FC<CounterProps> = (
	props: CounterProps,
)=> {
	// get user roles.
	// const userRoles = useSelector((state: RootState) => state.login.userRoles)
	const { role } = props

	// get franchise list
	const franchiseList = useSelector(
		(state: RootState) => state.franchise.franchiseList,
	)

	const currentTileSales = useSelector(
		(state: RootState) => state.dashboard.activeSalesTile,
	)

	const dispatch = useDispatch()

	// to reset dashboard tile back to "Total Request" when current location changes
	let location = useLocation()

	useEffect(() => {
		dispatch(
			changeTileSales({
				tileIndexSales: 0,
				name: "Total Requests",
				role: "encoder",
			}),
		)
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [location])

	// check user role
	let tiles: Array<string> = []

	tiles = salesUserStatuses

	// get tileName
	let tileName: string

	// for total request tile (separated tile from array tiles)
	const [isActiveTotalTile, setIsActiveTotalTile] = useState(true)
	let totalCount = franchiseList.length

	const handleSalesTotalClicked = () => {
		setIsActiveTotalTile(true)
		tileName = "Total Requests"
		dispatch(
			changeTileSales({ tileIndexSales: 0, name: tileName, role: "encoder" }),
		)
	}

	//for other tiles
	const handleSelectTile = (index: any) => {
		setIsActiveTotalTile(false)
		tileName = tiles[index]
		dispatch(
			changeTileSales({
				tileIndexSales: index,
				name: tileName,
				role: "encoder",
			}),
		)
	}

	let list: any
	let listCount: number

	// remove content of index 0 for array tiles but retain index
	delete tiles[0]
	// for total count per status and actual tile layout per user role
	let content: any
	if (franchiseList.length > 0) {
		content = tiles.map((tile, index) => {
			if (tile === "Saved Requests") {
				list = franchiseList.filter(({ status }) => status === "SAVED")
				listCount = list.length
			} else if (tile === "For Approval") {
				list = franchiseList.filter(
					({ status }) =>
						status === "FOR_APPROVAL" ||
						status === "APPROVAL_IN_PROCESS" ||
						status === "RETURNED_TO_VALIDATOR" ||
						status === "RESUBMITTED_TO_SUPERVISOR" ||
						status === "FINAL_APPROVAL" ||
						status === "FINAL_APPROVAL_IN_PROCESS",
				)
				listCount = list.length
			} else if (tile === "Submitted") {
				list = franchiseList.filter(
					({ status }) => status === "SUBMITTED" || status === "RESUBMITTED",
				)
				listCount = list.length
			} else if (tile === "Expiring Franchise") {
				list = franchiseList.filter(
					({ status, expiry_date, account_status }) => {
						let actualExpiryDate = expiry_date
							? moment(expiry_date)
							: moment().startOf("day")
						let expiryCounter = actualExpiryDate.diff(
							moment().startOf("day"),
							"days",
						)
						return (
							status === "APPROVED" &&
							account_status !== "ACTIVE" &&
							expiryCounter <= 30 && 
							expiryCounter >= 0
						)
					},
				)
				listCount = list.length
			} else if (tile === "Expired Franchise") {
				list = franchiseList.filter(
				  ({ status, expiry_date, account_status }) => {
					let actualExpiryDate = expiry_date
					  ? moment(expiry_date)
					  : moment().startOf("day");
					let expiryCounter = actualExpiryDate.diff(
					  moment().startOf("day"),
					  "days"
					);
					return (status === "APPROVED" && expiryCounter < 0) || status === "EXPIRED";
				  }
				);
				listCount = list.length
			  } else {
				list = franchiseList.filter(
					({ status }) => status === tile.toUpperCase(),
				)
				listCount = list.length
			}
			return (
				<span key={index}>
					<ClickableTileSales
						name={tile}
						count={listCount}
						indexSales={index}
						isActiveSales={currentTileSales === index}
						onClick={handleSelectTile}
						role={role}
					/>
				</span>
			)
		})
	} else {
		content = tiles.map((tile, index) => {
			return (
				<span key={index}>
					<ClickableTileSales
						name={tile}
						count={0}
						indexSales={index}
						isActiveSales={currentTileSales === index}
						onClick={handleSelectTile}
						role={role}
					/>
				</span>
			)
		})
	}

	const wholeTileHeader = (
		<>
			<span>
				<button
					data-cy="total-requests"
					className={isActiveTotalTile ? "vni-tab-btn active" : "vni-tab-btn"}
					onClick={handleSalesTotalClicked}
				>
					<strong>{totalCount}</strong> Total Requests
				</button>
			</span>
			<div className="vni-flex vni-flex-wrap">
				<>{content}</>
			</div>
		</>
	)

	return wholeTileHeader
}

export const ClickableTileSales: React.FC<TileProps> = (props) => {
	const handleClick = () => {
		props.onClick(props.indexSales)
	}

	return (
		<button
			data-cy={"filter-tile" + props.indexSales}
			className={
				props.isActiveSales && props.role === "encoder"
					? "vni-tab-btn active"
					: "vni-tab-btn"
			}
			onClick={handleClick}
		>
			<strong>{props.count}</strong>
			{props.name}
		</button>
	)
}
