import React, { useEffect, useState } from "react"
import { useLocation } from "react-router-dom"
import { useSelector, useDispatch } from "react-redux"
import { RootState } from "../../../shared/reducers/rootReducer"
import { changeTileSupervisor } from "../../../shared/reducers/DashboardSlice"
import { supervisorUserTileStatuses } from "../../../shared/models/Franchise"
import moment from "moment"


interface CounterProps {
	role: any
}

interface TileProps {
	indexSupervisor: number
	onClick: any
	isActiveSupervisor: any
	name: string
	count: number
	role: any
}

export const DashboardSupervisorTile: React.FC<CounterProps> = (
	props: CounterProps,
)=> {
	// get user roles.
	// const userRoles = useSelector((state: RootState) => state.login.userRoles)
	const { role } = props

	// get franchise list
	const franchiseList = useSelector(
		(state: RootState) => state.franchise.franchiseList,
	)

	const currentTileSupervisor = useSelector(
		(state: RootState) => state.dashboard.activeSupervisorTile,
	)

	const dispatch = useDispatch()

	// to reset dashboard tile back to "Total Request" when current location changes
	let location = useLocation()
	useEffect(() => {
		dispatch(
			changeTileSupervisor({
				tileIndexSupervisor: 0,
				name: "Total Requests",
				role: "supervisor",
			}),
		)
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [location])

	// check user role
	let tiles: Array<string> = []

	tiles = supervisorUserTileStatuses

	// get tileName
	let tileName: string

	// for total request tile (separated tile from array tiles)
	const [isActiveTotalTile, setIsActiveTotalTile] = useState(true)
	let totalCount = franchiseList.length

	const handleTotalClicked = () => {
		setIsActiveTotalTile(true)
		tileName = "Total Requests"
		dispatch(
			changeTileSupervisor({
				tileIndexSupervisor: 0,
				name: tileName,
				role: "supervisor",
			}),
		)
	}

	//for other tiles
	const handleSelectTile = (index: any) => {
		setIsActiveTotalTile(false)
		tileName = tiles[index]
		dispatch(
			changeTileSupervisor({
				tileIndexSupervisor: index,
				name: tileName,
				role: "supervisor",
			}),
		)
	}

	let list: any
	let listCount: number

	// remove content of index 0 for array tiles but retain index
	delete tiles[0]
	// for total count per status and actual tile layout per user role
	let content: any

	if (franchiseList.length > 0) {
		content = tiles.map((tile, index) => {
			if (tile === "For Approval") {
				list = franchiseList.filter(
					({ status }) =>
						status === "FOR_APPROVAL" || status === "RESUBMITTED_TO_SUPERVISOR",
				)
				listCount = list.length
			} else if (tile === "Approval In Process") {
				list = franchiseList.filter(
					({ status }) => status === "APPROVAL_IN_PROCESS",
				)
				listCount = list.length
			} else if (tile === "Returned") {
				list = franchiseList.filter(
					({ status }) =>
						status === "RETURNED_TO_VALIDATOR" ||
						status === "RESUBMITTED" ||
						status === "RECEIVED" ||
						status === "RETURNED",
				)
				listCount = list.length
			} else if (tile === "For Final Approval") {
				list = franchiseList.filter(
					({ status }) =>
						status === "FINAL_APPROVAL" ||
						status === "FINAL_APPROVAL_IN_PROCESS",
				)
				listCount = list.length
			} else if (tile === "Expiring Franchise") {
				list = franchiseList.filter(
					({ status, expiry_date, account_status }) => {
						let actualExpiryDate = expiry_date
							? moment(expiry_date)
							: moment().startOf("day")
						let expiryCounter = actualExpiryDate.diff(
							moment().startOf("day"),
							"days",
						)
						return (
							status === "APPROVED" &&
							account_status !== "ACTIVE" &&
							expiryCounter <= 30 &&
							expiryCounter >= 0
						)
					},
				)
				listCount = list.length
			} else if (tile === "Expired Franchise") {
				list = franchiseList.filter(
				  ({ status, expiry_date, account_status }) => {
					let actualExpiryDate = expiry_date
					  ? moment(expiry_date)
					  : moment().startOf("day");
					let expiryCounter = actualExpiryDate.diff(
					  moment().startOf("day"),
					  "days"
					);
					return (status === "APPROVED" && expiryCounter < 0) || status === "EXPIRED";
				  }
				);
				listCount = list.length
			  } else {
				list = franchiseList.filter(
					({ status }) => status === tile.toUpperCase(),
				)
				listCount = list.length
			}
			return (
				<span key={index} className="vni-supervisor-filter-button">
					<ClickableTileSupervisor
						name={tile}
						count={listCount}
						indexSupervisor={index}
						isActiveSupervisor={currentTileSupervisor === index}
						onClick={handleSelectTile}
						role={role}
					/>
				</span>
			)
		})
	} else {
		content = tiles.map((tile, index) => {
			return (
				<span key={index}>
					<ClickableTileSupervisor
						name={tile}
						count={0}
						indexSupervisor={index}
						isActiveSupervisor={currentTileSupervisor === index}
						onClick={handleSelectTile}
						role={role}
					/>
				</span>
			)
		})
	}

	const wholeTileHeader = (
		<>
			<span>
				<button
					data-cy="total-requests"
					className={isActiveTotalTile ? "vni-tab-btn active" : "vni-tab-btn"}
					onClick={handleTotalClicked}
				>
					<strong>{totalCount}</strong> Total Requests
				</button>
			</span>
			<div className="vni-flex vni-flex-wrap">
				<>{content}</>
			</div>
		</>
	)

	return wholeTileHeader
}

export const ClickableTileSupervisor: React.FC<TileProps> = (props) => {
	const handleClick = () => props.onClick(props.indexSupervisor)
	return (
		<button
			data-cy={"filter-tile" + props.indexSupervisor}
			className={
				props.isActiveSupervisor && props.role === "supervisor"
					? "vni-tab-btn active"
					: "vni-tab-btn"
			}
			onClick={handleClick}
		>
			<strong>{props.count}</strong>
			{props.name}
		</button>
	)
}
