import React, { useEffect, useState } from "react"
import { useLocation } from "react-router-dom"
import { useSelector, useDispatch } from "react-redux"
import { RootState } from "../../../shared/reducers/rootReducer"
import { changeTileSpecialist } from "../../../shared/reducers/DashboardSlice"
import { specialistUserTileStatuses } from "../../../shared/models/Franchise"
import moment from "moment"

interface CounterProps {
	role: any
}

interface TileProps {
	indexSpecialist: number
	onClick: any
	isActiveSpecialist: any
	name: string
	count: number
	role: any
}

export const DashboardSpecialistTile: React.FC<CounterProps> = (
	props: CounterProps,
)=> {
	// get user roles.
	// const userRoles = useSelector((state: RootState) => state.login.userRoles)
	const { role } = props

	// get franchise list
	const franchiseList = useSelector(
		(state: RootState) => state.franchise.franchiseList,
	)

	const currentTileSpecialist = useSelector(
		(state: RootState) => state.dashboard.activeSpecialistTile,
	)

	const dispatch = useDispatch()

	// to reset dashboard tile back to "Total Request" when current location changes
	let location = useLocation()

	useEffect(() => {
		dispatch(
			changeTileSpecialist({
				tileIndexSpecialist: 0,
				name: "Total Requests",
				role: "validator",
			}),
		)
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [location])

	// check user role
	let tiles: Array<string> = []

	tiles = specialistUserTileStatuses

	// get tileName
	let tileName: string

	// for total request tile (separated tile from array tiles)
	const [isActiveTotalTile, setIsActiveTotalTile] = useState(true)
	let totalCount = franchiseList.length

	const handleTotalClicked = () => {
		setIsActiveTotalTile(true)
		tileName = "Total Requests"
		dispatch(
			changeTileSpecialist({
				tileIndexSpecialist: 0,
				name: tileName,
				role: "validator",
			}),
		)
	}

	//for other tiles
	const handleSelectTile = (index: any) => {
		setIsActiveTotalTile(false)
		tileName = tiles[index]
		dispatch(
			changeTileSpecialist({
				tileIndexSpecialist: index,
				name: tileName,
				role: "validator",
			}),
		)
	}

	let list: any
	let listCount: number

	// remove content of index 0 for array tiles but retain index
	delete tiles[0]
	// for total count per status and actual tile layout per user role
	let content: any
	// if (role === "validator") {
	if (franchiseList.length > 0) {
		content = tiles.map((tile, index) => {
			if (tile === "Submitted Requests") {
				list = franchiseList.filter(
					({ status }) => status === "SUBMITTED" || status === "RESUBMITTED",
				)
				listCount = list.length
			} else if (tile === "Validation In-Process") {
				list = franchiseList.filter(({ status }) => status === "RECEIVED")
				listCount = list.length
			} else if (tile === "Validation Complete") {
				list = franchiseList.filter(({ status }) => status === "VALIDATED")
				listCount = list.length
			} else if (tile === "For Approval") {
				list = franchiseList.filter(
					({ status }) =>
						status === "FOR_APPROVAL" ||
						status === "APPROVAL_IN_PROCESS" ||
						status === "RESUBMITTED_TO_SUPERVISOR" ||
						status === "FINAL_APPROVAL" ||
						status === "FINAL_APPROVAL_IN_PROCESS",
				)
				listCount = list.length
			} else if (tile === "Returned to Sales User") {
				list = franchiseList.filter(({ status }) => status === "RETURNED")
				listCount = list.length
			} else if (tile === "Returned") {
				list = franchiseList.filter(
					({ status }) => status === "RETURNED_TO_VALIDATOR",
				)
				listCount = list.length
			} else if (tile === "Expiring Franchise") {
				list = franchiseList.filter(
					({ status, expiry_date, account_status }) => {
						let actualExpiryDate = expiry_date
							? moment(expiry_date)
							: moment().startOf("day")
						let expiryCounter = actualExpiryDate.diff(
							moment().startOf("day"),
							"days",
						)
						return (
							status === "APPROVED" &&
							account_status !== "ACTIVE" &&
							expiryCounter <= 30 &&
							expiryCounter >= 0
						)
					},
				)
				listCount = list.length
			} else if (tile === "Expired Franchise") {
				list = franchiseList.filter(
				  ({ status, expiry_date, account_status }) => {
					let actualExpiryDate = expiry_date
					  ? moment(expiry_date)
					  : moment().startOf("day");
					let expiryCounter = actualExpiryDate.diff(
					  moment().startOf("day"),
					  "days"
					);
					return (status === "APPROVED" && expiryCounter < 0) || status === "EXPIRED";
				  }
				);
				listCount = list.length
			  } else {
				list = franchiseList.filter(
					({ status }) => status === tile.toUpperCase(),
				)
				listCount = list.length
			}
			return (
				<span key={index}>
					<ClickableTileSpecialist
						name={tile}
						count={listCount}
						indexSpecialist={index}
						isActiveSpecialist={currentTileSpecialist === index}
						onClick={handleSelectTile}
						role={role}
					/>
				</span>
			)
		})
	} else {
		content = tiles.map((tile, index) => {
			return (
				<span key={index}>
					<ClickableTileSpecialist
						name={tile}
						count={0}
						indexSpecialist={index}
						isActiveSpecialist={currentTileSpecialist === index}
						onClick={handleSelectTile}
						role={role}
					/>
				</span>
			)
		})
	}
	// }

	const wholeTileHeader = (
		<>
			<span>
				<button
					data-cy="total-requests"
					className={isActiveTotalTile ? "vni-tab-btn active" : "vni-tab-btn"}
					onClick={handleTotalClicked}
				>
					<strong>{totalCount}</strong> Total Requests
				</button>
			</span>
			<div className="vni-flex vni-flex-wrap">
				<>{content}</>
			</div>
		</>
	)

	return wholeTileHeader
}

export const ClickableTileSpecialist: React.FC<TileProps> = (props) => {
	const handleClick = () => {
		props.onClick(props.indexSpecialist)
	}
	return (
		<button
			data-cy={"filter-tile" + props.indexSpecialist}
			className={
				props.isActiveSpecialist && props.role === "validator"
					? "vni-tab-btn active"
					: "vni-tab-btn"
			}
			onClick={handleClick}
		>
			<strong>{props.count}</strong>
			{props.name}
		</button>
	)
}
