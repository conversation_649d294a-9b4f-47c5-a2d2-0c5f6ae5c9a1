import React from "react";

import { DashboardTile } from "./DashboardTile";
import { DashboardSalesTile } from "./DashboardTiles/DashboardSalesTile";
import { DashboardSpecialistTile } from "./DashboardTiles/DashboardSpecialistTile";
import { DashboardSupervisorTile } from "./DashboardTiles/DashboardSupervisorTile";
import { DashboardManagerTile } from "./DashboardTiles/DashboardManagerTile";

interface IProps {
  userRole: any;
  isMultipleRole: any;
}

// export default class DashboardCounter extends Component {
//   render() {
//     return (
//       <div className="vni-tab-btn-container no-wrap">
//         <DashboardTile />
//       </div>
//     );
//   }
// }
export const DashboardCounter: React.FC<IProps> = (
  props: IProps
)=> {
  const { userRole, isMultipleRole } = props;



  // const renderDashboardCounter = (role: string) => {
	// console.log(role, 888)
  //   switch (role) {
  //     case "encoder":
  //       return (
  //         <div className="vni-tab-btn-container no-wrap">
  //           <DashboardSalesTile role={role} />
  //         </div>
  //       );

  //     case "validator":
  //       return (
  //         <div className="vni-tab-btn-container no-wrap">
  //           <DashboardSpecialistTile role={role} />
  //         </div>
  //       );

  //     case "supervisor":
  //       return (
  //         <div className="vni-tab-btn-container no-wrap">
  //           <DashboardSupervisorTile role={role} />
  //         </div>
  //       );

  //     case "manager":
  //       return (
  //         <div className="vni-tab-btn-container no-wrap">
  //           <DashboardManagerTile role={role} />
  //         </div>
  //       );

  //     default:
  //       return (
  //         <div className="vni-tab-btn-container no-wrap">
  //           <DashboardTile />
  //         </div>
  //       );
  //   }
  // };

  return (
    <>
      {/* {renderDashboardCounter(userRole)} */}
      {isMultipleRole ? (
        <>
          {userRole === "encoder" && (
            <div className="vni-tab-btn-container no-wrap">
              <DashboardSalesTile role="encoder" />
            </div>
          )}
          {userRole === "validator" && (
            <div className="vni-tab-btn-container no-wrap">
              <DashboardSpecialistTile role="validator" />
            </div>
          )}
          {userRole === "supervisor" && (
            <div className="vni-tab-btn-container no-wrap">
              <DashboardSupervisorTile role="supervisor" />
            </div>
          )}
          {userRole === "manager" && (
            <div className="vni-tab-btn-container no-wrap">
              <DashboardManagerTile role="manager" />
            </div>
          )}
        </>
      ) : (
        <>
          <div className="vni-tab-btn-container no-wrap">
            <DashboardTile />
          </div>
        </>
      )}
    </>
  );
};

export default DashboardCounter;
