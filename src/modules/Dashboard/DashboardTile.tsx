import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "../../shared/reducers/rootReducer";
import { changeTile } from "../../shared/reducers/DashboardSlice";
import {
  salesUserStatuses,
  specialistUserTileStatuses,
  supervisorUserTileStatuses,
  managerUserTileStatuses,
} from "../../shared/models/Franchise";
import moment from "moment";

interface TileProps {
  index: number;
  onClick: any;
  isActive: any;
  name: string;
  count: number;
}

export const DashboardTile: React.FC = () => {
  // get user roles.
  const userRoles = useSelector((state: RootState) => state.login.userRoles);
  // get franchise list
  const franchiseList = useSelector(
    (state: RootState) => state.franchise.franchiseList
  );

  const currentTile = useSelector(
    (state: RootState) => state.dashboard.activeTile
  );

  const dispatch = useDispatch();

  // to reset dashboard tile back to "Total Request" when current location changes
  let location = useLocation();
  useEffect(() => {
    dispatch(changeTile({ tileIndex: 0, name: "Total Requests" }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location]);

  // check user role
  let tiles: Array<string> = [];
  if (userRoles.isSalesUser) {
    tiles = salesUserStatuses;
  } else if (userRoles.isSpecialist) {
    tiles = specialistUserTileStatuses;
  } else if (userRoles.isSupervisor) {
    tiles = supervisorUserTileStatuses;
  } else if (userRoles.isManager) {
    tiles = managerUserTileStatuses;
  }

  // get tileName
  let tileName: string;

  // for total request tile (separated tile from array tiles)
  const [isActiveTotalTile, setIsActiveTotalTile] = useState(true);
  let totalCount = franchiseList.length;

  const handleTotalClicked = () => {
    setIsActiveTotalTile(true);
    tileName = "Total Requests";
    dispatch(changeTile({ tileIndex: 0, name: tileName }));
  };

  //for other tiles
  const handleSelectTile = (index: any) => {
    setIsActiveTotalTile(false);
    tileName = tiles[index];
    dispatch(changeTile({ tileIndex: index, name: tileName }));
  };

  let list: any;
  let listCount: number;

  // remove content of index 0 for array tiles but retain index
  delete tiles[0];
  // for total count per status and actual tile layout per user role

  let content: any;
  if (franchiseList.length > 0) {
    if (userRoles.isSalesUser) {
      content = tiles.map((tile, index) => {
        if (tile === "Saved Requests") {
          list = franchiseList.filter(({ status }) => status === "SAVED");
          listCount = list.length;
        } else if (tile === "For Approval") {
          list = franchiseList.filter(
            ({ status }) =>
              status === "FOR_APPROVAL" ||
              status === "APPROVAL_IN_PROCESS" ||
              status === "RETURNED_TO_VALIDATOR" ||
              status === "RESUBMITTED_TO_SUPERVISOR" ||
              status === "FINAL_APPROVAL" ||
              status === "FINAL_APPROVAL_IN_PROCESS"
          );
          listCount = list.length;
        } else if (tile === "Submitted") {
          list = franchiseList.filter(
            ({ status }) => status === "SUBMITTED" || status === "RESUBMITTED"
          );
          listCount = list.length;
        } else if (tile === "Expiring Franchise") {
          list = franchiseList.filter(
            ({ status, expiry_date, account_status }) => {
              let actualExpiryDate = expiry_date
                ? moment(expiry_date)
                : moment().startOf("day");
              let expiryCounter = actualExpiryDate.diff(
                moment().startOf("day"),
                "days"
              );
              return (
                status === "APPROVED" &&
                account_status !== "ACTIVE" &&
                expiryCounter <= 30 &&
                expiryCounter >= 0
              );
            }
          );
          listCount = list.length;
        } else if (tile === "Expired Franchise") {
          list = franchiseList.filter(
            ({ status, expiry_date, account_status }) => {
              let actualExpiryDate = expiry_date
                ? moment(expiry_date)
                : moment().startOf("day");
              let expiryCounter = actualExpiryDate.diff(
                moment().startOf("day"),
                "days"
              );
               return (status === "APPROVED" && expiryCounter < 0) || status === "EXPIRED";
            }
          );
          listCount = list.length
        } else if (tile === "Expiring Returned Franchise") {
          list = franchiseList.filter(
            ({ status, expiry_date, account_status }) => {
              let actualExpiryDate = expiry_date
                ? moment(expiry_date)
                : moment().startOf("day");
              let expiryCounter = actualExpiryDate.diff(
                moment().startOf("day"),
                "days"
              );
              return (
                status === "RETURNED" &&
                expiryCounter <= 30 &&
                expiryCounter >= 0
              );
            }
          );
          listCount = list.length
        } else if (tile === "Expired Returned Franchise") {
          list = franchiseList.filter(
            ({ status, expiry_date, account_status }) => {
              let actualExpiryDate = expiry_date
                ? moment(expiry_date)
                : moment().startOf("day");
              let expiryCounter = actualExpiryDate.diff(
                moment().startOf("day"),
                "days"
              );
              return (
                status === "RETURNED" && 
                expiryCounter < 0
              );
            }
          );
          listCount = list.length
        } else {
          list = franchiseList.filter(
            ({ status }) => status === tile.toUpperCase()
          );
          listCount = list.length;
        }
        return (
          <span key={index}>
            <ClickableTile
              name={tile}
              count={listCount}
              index={index}
              isActive={currentTile === index}
              onClick={handleSelectTile}
            />
          </span>
        );
      });
    } else if (userRoles.isSpecialist) {
      content = tiles.map((tile, index) => {
        if (tile === "Submitted Requests") {
          list = franchiseList.filter(
            ({ status }) => status === "SUBMITTED" || status === "RESUBMITTED"
          );
          listCount = list.length;
        } else if (tile === "Validation In-Process") {
          list = franchiseList.filter(({ status }) => status === "RECEIVED");
          listCount = list.length;
        } else if (tile === "Validation Complete") {
          list = franchiseList.filter(({ status }) => status === "VALIDATED");
          listCount = list.length;
        } else if (tile === "For Approval") {
          list = franchiseList.filter(
            ({ status }) =>
              status === "FOR_APPROVAL" ||
              status === "APPROVAL_IN_PROCESS" ||
              status === "RESUBMITTED_TO_SUPERVISOR" ||
              status === "FINAL_APPROVAL" ||
              status === "FINAL_APPROVAL_IN_PROCESS"
          );
          listCount = list.length;
        } else if (tile === "Returned to Sales User") {
          list = franchiseList.filter(({ status }) => status === "RETURNED");
          listCount = list.length;
        } else if (tile === "Returned") {
          list = franchiseList.filter(
            ({ status }) => status === "RETURNED_TO_VALIDATOR"
          );
          listCount = list.length;
        } else if (tile === "Expiring Franchise") {
          list = franchiseList.filter(
            ({ status, expiry_date, account_status }) => {
              let actualExpiryDate = expiry_date
                ? moment(expiry_date)
                : moment().startOf("day");
              let expiryCounter = actualExpiryDate.diff(
                moment().startOf("day"),
                "days"
              );
              return (
                status === "APPROVED" &&
                account_status !== "ACTIVE" &&
                expiryCounter <= 30 && 
                expiryCounter >= 0
              );
            }
          );
          listCount = list.length;
        } else if (tile === "Expired Franchise") {
          list = franchiseList.filter(
            ({ status, expiry_date, account_status }) => {
              let actualExpiryDate = expiry_date
                ? moment(expiry_date)
                : moment().startOf("day");
              let expiryCounter = actualExpiryDate.diff(
                moment().startOf("day"),
                "days"
              );
              return (status === "APPROVED" && expiryCounter < 0) || status === "EXPIRED";
            }
          );
          listCount = list.length
        } else {
          list = franchiseList.filter(
            ({ status }) => status === tile.toUpperCase()
          );
          listCount = list.length;
        }
        return (
          <span key={index}>
            <ClickableTile
              name={tile}
              count={listCount}
              index={index}
              isActive={currentTile === index}
              onClick={handleSelectTile}
            />
          </span>
        );
      });
    } else if (userRoles.isSupervisor) {
      content = tiles.map((tile, index) => {
        if (tile === "For Approval") {
          list = franchiseList.filter(
            ({ status }) =>
              status === "FOR_APPROVAL" ||
              status === "RESUBMITTED_TO_SUPERVISOR"
          );
          listCount = list.length;
        } else if (tile === "Approval In Process") {
          list = franchiseList.filter(
            ({ status }) => status === "APPROVAL_IN_PROCESS"
          );
          listCount = list.length;
        } else if (tile === "Returned") {
          list = franchiseList.filter(
            ({ status }) =>
              status === "RETURNED_TO_VALIDATOR" ||
              status === "RESUBMITTED" ||
              status === "RECEIVED" ||
              status === "RETURNED"
          );
          listCount = list.length;
        } else if (tile === "For Final Approval") {
          list = franchiseList.filter(
            ({ status }) =>
              status === "FINAL_APPROVAL" ||
              status === "FINAL_APPROVAL_IN_PROCESS"
          );
          listCount = list.length;
        } else if (tile === "Expiring Franchise") {
          list = franchiseList.filter(
            ({ status, expiry_date, account_status }) => {
              let actualExpiryDate = expiry_date
                ? moment(expiry_date)
                : moment().startOf("day");
              let expiryCounter = actualExpiryDate.diff(
                moment().startOf("day"),
                "days"
              );
              return (
                status === "APPROVED" &&
                account_status !== "ACTIVE" &&
                expiryCounter <= 30 && 
                expiryCounter >= 0
              );
            }
          );
          listCount = list.length;
        } else if (tile === "Expired Franchise") {
          list = franchiseList.filter(
            ({ status, expiry_date, account_status }) => {
              let actualExpiryDate = expiry_date
                ? moment(expiry_date)
                : moment().startOf("day");
              let expiryCounter = actualExpiryDate.diff(
                moment().startOf("day"),
                "days"
              );
              return (status === "APPROVED" && expiryCounter < 0) || status === "EXPIRED";
            }
          );
          listCount = list.length
        } else {
          list = franchiseList.filter(
            ({ status }) => status === tile.toUpperCase()
          );
          listCount = list.length;
        }
        return (
          <span key={index} className="vni-supervisor-filter-button">
            <ClickableTile
              name={tile}
              count={listCount}
              index={index}
              isActive={currentTile === index}
              onClick={handleSelectTile}
            />
          </span>
        );
      });
    } else if (userRoles.isManager) {
      content = tiles.map((tile, index) => {
        if (tile === "For Approval") {
          list = franchiseList.filter(
            ({ status }) => status === "FINAL_APPROVAL"
          );
          listCount = list.length;
        } else if (tile === "Approval In Process") {
          list = franchiseList.filter(
            ({ status }) => status === "FINAL_APPROVAL_IN_PROCESS"
          );
          listCount = list.length;
        } else if (tile === "Approved") {
          list = franchiseList.filter(({ status }) => status === "APPROVED");
          listCount = list.length;
        } else if (tile === "Disapproved") {
          list = franchiseList.filter(({ status }) => status === "DISAPPROVED");
          listCount = list.length;
        } else if (tile === "Expiring Franchise") {
          list = franchiseList.filter(
            ({ status, expiry_date, account_status }) => {
              let actualExpiryDate = expiry_date
                ? moment(expiry_date)
                : moment().startOf("day");
              let expiryCounter = actualExpiryDate.diff(
                moment().startOf("day"),
                "days"
              );
              return (
                status === "APPROVED" &&
                account_status !== "ACTIVE" &&
                expiryCounter <= 30 && 
                expiryCounter >= 0
              );
            }
          );
          listCount = list.length;
        } else if (tile === "Expired Franchise") {
          list = franchiseList.filter(
            ({ status, expiry_date, account_status }) => {
              let actualExpiryDate = expiry_date
                ? moment(expiry_date)
                : moment().startOf("day");
              let expiryCounter = actualExpiryDate.diff(
                moment().startOf("day"),
                "days"
              );
              return (status === "APPROVED" && expiryCounter < 0) || status === "EXPIRED";
            }
          );
          listCount = list.length
        } else {
          list = franchiseList.filter(
            ({ status }) => status === tile.toUpperCase()
          );
          listCount = list.length;
        }
        return (
          <span key={index}>
            <ClickableTile
              name={tile}
              count={listCount}
              index={index}
              isActive={currentTile === index}
              onClick={handleSelectTile}
            />
          </span>
        );
      });
    }
  } else {
    content = tiles.map((tile, index) => {
      return (
        <span key={index}>
          <ClickableTile
            name={tile}
            count={0}
            index={index}
            isActive={currentTile === index}
            onClick={handleSelectTile}
          />
        </span>
      );
    });
  }

  const wholeTileHeader = (
    <>
      <span>
        <button
          data-cy="total-requests"
          className={isActiveTotalTile ? "vni-tab-btn active" : "vni-tab-btn"}
          onClick={handleTotalClicked}
        >
          <strong>{totalCount}</strong> Total Requests
        </button>
      </span>
      <div
        className={
          userRoles.isSupervisor
            ? "vni-flex vni-flex-wrap new-max-width"
            : "vni-flex vni-flex-wrap"
        }
      >
        {content}
      </div>
    </>
  );

  return wholeTileHeader;
};

export const ClickableTile: React.FC<TileProps> = (props) => {
  const handleClick = () => props.onClick(props.index);
  return (
    <button
      data-cy={"filter-tile" + props.index}
      className={props.isActive ? "vni-tab-btn active" : "vni-tab-btn"}
      onClick={handleClick}
    >
      <strong>{props.count}</strong>
      {props.name}
    </button>
  );
};
