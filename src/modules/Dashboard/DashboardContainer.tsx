import React, { useEffect } from "react";
import { Switch, Route } from "react-router-dom";
import { useSelector } from "react-redux";
import { RootState } from "../../shared/reducers/rootReducer";
import moment from "moment";

// Dashboards
import SalesDashboard from "./DashboardTable/SalesDashboard";
import SpecialistDashboard from "./DashboardTable/SpecialistDashboard";
import SupervisorDashboard from "./DashboardTable/SupervisorDashboard";
import ManagerDashboard from "./DashboardTable/ManagerDashboard";

const DashboardContainer: React.FC = () => {
  const token = useSelector((state: RootState) => state.login.token);
  const currentTile = useSelector(
    (state: RootState) => state.dashboard.tileName
  );

  const franchiseList = useSelector(
    (state: RootState) => state.franchise.franchiseList
  );
  const userRoles = useSelector((state: RootState) => state.login.userRoles);

  const { isManager } = userRoles;
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  let count = 0;
  let filteredContent = [];

  if (franchiseList.length > 0) {
    switch (currentTile) {
      case "Total Requests":
        count = franchiseList.length;
        break;
      case "Saved Requests":
        filteredContent = franchiseList.filter(
          ({ status }) => status === "SAVED"
        );
        count = filteredContent.length;
        break;
      case "Submitted Requests":
        filteredContent = franchiseList.filter(
          ({ status }) => status === "SUBMITTED" || status === "RESUBMITTED"
        );
        count = filteredContent.length;
        break;
      case "Submitted":
        filteredContent = franchiseList.filter(
          ({ status }) => status === "SUBMITTED" || status === "RESUBMITTED"
        );
        count = filteredContent.length;
        break;
      case "Validation In-Process":
        filteredContent = franchiseList.filter(
          ({ status }) => status === "RECEIVED"
        );
        count = filteredContent.length;
        break;
      case "Validation Complete":
        filteredContent = franchiseList.filter(
          ({ status }) => status === "VALIDATED"
        );
        count = filteredContent.length;
        break;
      case "Returned to Sales User":
        filteredContent = franchiseList.filter(
          ({ status }) => status === "RETURNED"
        );
        count = filteredContent.length;
        break;
      case "For Approval":
        if (userRoles.isSalesUser) {
          filteredContent = franchiseList.filter(
            ({ status }) =>
              status === "FOR_APPROVAL" ||
              status === "APPROVAL_IN_PROCESS" ||
              status === "RETURNED_TO_VALIDATOR" ||
              status === "RESUBMITTED_TO_SUPERVISOR" ||
              status === "FINAL_APPROVAL" ||
              status === "FINAL_APPROVAL_IN_PROCESS"
          );
          count = filteredContent.length;
        } else if (userRoles.isSpecialist) {
          filteredContent = franchiseList.filter(
            ({ status }) =>
              status === "FOR_APPROVAL" ||
              status === "APPROVAL_IN_PROCESS" ||
              status === "RESUBMITTED_TO_SUPERVISOR" ||
              status === "FINAL_APPROVAL" ||
              status === "FINAL_APPROVAL_IN_PROCESS"
          );
          count = filteredContent.length;
        } else if (userRoles.isSupervisor) {
          filteredContent = franchiseList.filter(
            ({ status }) =>
              status === "FOR_APPROVAL" ||
              status === "RESUBMITTED_TO_SUPERVISOR"
          );
          count = filteredContent.length;
        } else {
          filteredContent = franchiseList.filter(
            ({ status }) => status === "FINAL_APPROVAL"
          );
          count = filteredContent.length;
        }
        break;
      case "Approval In Process":
        if (isManager) {
          filteredContent = franchiseList.filter(
            ({ status }) => status === "FINAL_APPROVAL_IN_PROCESS"
          );
        } else {
          filteredContent = franchiseList.filter(
            ({ status }) => status === "APPROVAL_IN_PROCESS"
          );
        }
        count = filteredContent.length;
        break;
      case "For Final Approval":
        filteredContent = franchiseList.filter(
          ({ status }) =>
            status === "FINAL_APPROVAL" ||
            status === "FINAL_APPROVAL_IN_PROCESS"
        );
        count = filteredContent.length;
        break;
      case "Expiring Franchise":
        filteredContent = franchiseList.filter(
          ({ status, expiry_date, account_status }) => {
            let actualExpiryDate = expiry_date
              ? moment(expiry_date)
              : moment().startOf("day");
            let expiryCounter = actualExpiryDate.diff(
              moment().startOf("day"),
              "days"
            );
            return (
              status === "APPROVED" &&
              account_status !== "ACTIVE" &&
              expiryCounter <= 30
            );
          }
        );
        count = filteredContent.length;
        break;

      case "Expired Franchise":
        filteredContent = franchiseList.filter(
          ({ status, expiry_date, account_status }) => {
            let actualExpiryDate = expiry_date
              ? moment(expiry_date)
              : moment().startOf("day");
            let expiryCounter = actualExpiryDate.diff(
              moment().startOf("day"),
              "days"
            );
            return (status === "APPROVED" && expiryCounter < 0) || status === "EXPIRED";
          }
        );
        count = filteredContent.length;
        break;
      case "Returned":
        if (userRoles.isSalesUser) {
          filteredContent = franchiseList.filter(
            ({ status }) => status === currentTile.toUpperCase()
          );
          count = filteredContent.length;
        } else if (userRoles.isSpecialist) {
          filteredContent = franchiseList.filter(
            ({ status }) => status === "RETURNED_TO_VALIDATOR"
          );
          count = filteredContent.length;
        } else if (userRoles.isSupervisor) {
          filteredContent = franchiseList.filter(
            ({ status }) =>
              status === "RETURNED_TO_VALIDATOR" ||
              status === "RESUBMITTED" ||
              status === "RECEIVED" ||
              status === "RETURNED"
          );
          count = filteredContent.length;
        }
        break;
      default:
        filteredContent = franchiseList.filter(
          ({ status }) => status === currentTile.toUpperCase()
        );
        count = filteredContent.length;
        break;
    }
  } else {
    filteredContent = [];
    count = 0;
  }

  return (
    <div className="main vni-py-8 vni-px-12">
      <Switch>
        {userRoles.isSalesUser && (
          <Route
            path="/franchising/dashboard/encoder"
            render={(props) => (
              <SalesDashboard {...props} token={token} count={count} />
            )}
          />
        )}
        {userRoles.isSpecialist && (
          <Route
            path="/franchising/dashboard/validator"
            render={(props) => (
              <SpecialistDashboard {...props} token={token} count={count} />
            )}
          />
        )}
        {userRoles.isSupervisor && (
          <Route
            path="/franchising/dashboard/supervisor"
            render={(props) => (
              <SupervisorDashboard {...props} token={token} count={count} />
            )}
          />
        )}
        {userRoles.isManager && (
          <Route
            path="/franchising/dashboard/manager"
            render={(props) => (
              <ManagerDashboard {...props} token={token} count={count} />
            )}
          />
        )}
      </Switch>
    </div>
  );
};
export default DashboardContainer;
