import React, { useState, useEffect } from "react";
// import { useDispatch } from "react-redux";
import { Text<PERSON>ield, Input<PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
// import { useHistory } from "react-router-dom";
import LoginStorageService from './LoginStorageService';
import * as crypto from 'crypto';
import { decryptCredentials, fieldsToEncrypt } from "../../utils/LoginService";

interface LoginDataType {
  username: string;
  password: string;
}

// const encodeFormData = (data: any) => {
//   return Object.keys(data)
//     .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(data[key]))
//     .join('&');
// };

// Module URL
const customerCareUrl = 'customer-care/index.html#/customer-care/';
const membershipUrl = 'membership/index.html#/membership/';
const systemAdminUrl = 'system-admin/index.html#/system-admin/';
const underwritingUrl = 'underwriting/index.html#/underwriting/';
const franchisingUrl = 'franchising/index.html#/franchising/';
const claimsUrl = 'claims';

let mainModule = '';
let cptFetchDone = false;
let icd10FetchDone = false;

//Claims URL
// const claimsPageURL = "claims/index.html";

//@ts-ignore
const loginStorageService = new LoginStorageService();

const key = crypto
  .createHash('sha256', {})
  .update(String(process.env.REACT_APP_HASHING_PASSWORD))
  .digest('base64')
  .substring(0, 32)
const cipherAlgorithm = 'aes-256-gcm'


const LoginContainer: React.FC = () => {
  // redux
  // const dispatch = useDispatch();
  // let history = useHistory();

  // hooks for input
  const [isFetching, setFetchingState] = useState(false);
  const [loginData, setLoginData] = useState<LoginDataType>({
    username: '',
    password: '',
  });
  const [eLoginData, setELoginData] = useState<LoginDataType>({
    username: '',
    password: '',
  });
  let cptFetched = 0;
  let cptList: any = [];

  let icdFetched = 0;
  let icd10List: any = [];

  const encryptCredentials = (text: any) => {
    const initVector = crypto.randomBytes(16)
      const initVectorHex = initVector.toString('hex')
      const cipher = crypto.createCipheriv(cipherAlgorithm, key, initVector)
      const encoded = cipher.update(text, 'utf8', 'hex') + cipher.final('hex')
      const authTag = cipher.getAuthTag().toString('hex')
      const metaAndEncoded = [authTag, initVectorHex, encoded].join('|')
      return metaAndEncoded
  };

  // events
  const handleTextFieldOnChange = (name: string) => (event: any) => {
    const data = { ...loginData, [name]: event.target.value };
    const data2 = { ...loginData, [name]: event.target.value };
    setLoginData(data);
    data2.username = encryptCredentials(data2.username);
    data2.password = encryptCredentials(data2.password);
    setELoginData(data2);
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    if (loginData.username.length === 0) {
      alert('Username is required.');
      window.location.reload();
      return;
    }

    if (loginData.password.length === 0) {
      alert('Password is required.');
      window.location.reload();
      return;
    }

    await login();
  };

  const login = async () => {
    localStorage.setItem('XDEV_CLIENT_URL', process.env.REACT_APP_BASE_URL || '');
    localStorage.setItem('XDEV_PMAKER_BASE_URL', process.env.REACT_APP_PMAKER_BASE_URL || '');
    localStorage.setItem('XDEV_CLAIMS_URL', process.env.REACT_APP_CLAIMS_URL || '');
    localStorage.setItem('XDEV_OCP_URL', process.env.REACT_APP_OCP_BASE_URL || '');
    localStorage.setItem('XDEV_DDS_URL', process.env.REACT_APP_DDS_BASE_URL || '');
    localStorage.setItem('XDEV_PARTNER_URL', process.env.REACT_APP_PARTNER_URL || '');
    localStorage.setItem('HIMS_TITLE', process.env.REACT_APP_HIMS_TITLE || '');
    localStorage.setItem('HIMS_ICON', process.env.REACT_APP_ICON || '');

    let url = `${process.env.REACT_APP_BASE_URL}login`;
    let options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(eLoginData)
    }

    setFetchingState(true);

    await fetch(url, options)
      .then((resp: any) => resp.json())
      .then(async (data: any) => {
        if (!data.error) {
          console.log('data', data);
          // JULY 7, 2020: MVP-MS823 (Clear localStorage for retainable filters)
          
          fieldsToEncrypt.forEach((field: string) => {
              if (data.login !== null && field in data.login) {
                data.login[field] = decryptCredentials(data.login[
                  field
                ] as string);
              }
            });


          if (localStorage.getItem('client_filters') !== null) {
            localStorage.removeItem('client_filters');
          }
          if (localStorage.getItem('client_statusfilters') !== null) {
            localStorage.removeItem('client_statusfilters');
          }
          if (localStorage.getItem('member_filters') !== null) {
            localStorage.removeItem('member_filters');
          }
          if (localStorage.getItem('member_statusfilters') !== null) {
            localStorage.removeItem('member_statusfilters');
          }
          if (localStorage.getItem('ticketlist_defaultfilters') !== null) {
            localStorage.removeItem('ticketlist_defaultfilters');
          }
          if (localStorage.getItem('ticketlist_backendfilters') !== null) {
            localStorage.removeItem('ticketlist_backendfilters');
          }
          if (localStorage.getItem('ticketlist_officefilters') !== null) {
            localStorage.removeItem('ticketlist_officefilters');
          }
          // 

          localStorage.setItem('api_token', data.login['access_token']);
          localStorage.setItem('pm_token', data.login['access_token']);
          localStorage.setItem('user_id', data.login.user_id);
          localStorage.setItem('employee_id', data.login.employee_id);
          localStorage.setItem('first_name', data.login.first_name);
          localStorage.setItem('last_name', data.login.last_name);
          if (data.login.main_module === 'Underwriting') {
            localStorage.setItem('sidebar', 'dashboard');
            // window.location.replace(underwritingUrl);
          } else if (data.login.main_module === 'Customer Care') {
            // window.location.replace(customerCareUrl);
          } else if (data.login.main_module === 'Membership') {
            localStorage.setItem('sidebar', 'dashboard');
            // window.location.replace(membershipUrl);
          } else if (data.login.main_module.toLowerCase() === 'franchising') {
            localStorage.setItem('sidebar', 'dashboard');
            // window.location.replace(franchisingUrl);
          } else {
            // window.location.replace(systemAdminUrl);
          }

          mainModule = data.login.main_module;

          data.login.pm_token = data.login['access_token'];
          await saveToIndexedDB(data);
        } else {
          setFetchingState(false);
          alert(data.error.message)
          window.location.reload();
        }

      })
      .catch((err: any) => {
        alert(err.message)
        window.location.reload();
      })
  }

  useEffect(() => {
    let urls = {
      PARTNER_URL: process.env.REACT_APP_HIMS_API_PARTNER_URL,
      CLIENT_URL: process.env.REACT_APP_HIMS_API_CLIENT_URL
    }

    let configToSave = Object.entries(urls).map(entry => {
      return { key: entry[0], value: entry[1] }
    });

    loginStorageService.initStorage('himsDb');

    loginStorageService.saveEntry(configToSave, 'config').then((res) => console.log(res)).catch(err => console.log(err))

    loginStorageService.clearUser('himsDb').then((res) => {
      console.log(res);
    }).catch((err) => console.log(err));
  }, [])

  const redirect = () => {
    console.log(mainModule);
    if (mainModule === 'Underwriting') {
      localStorage.setItem('sidebar', 'dashboard');
      window.location.replace(underwritingUrl);
    } else if (mainModule === 'Customer Care') {
      window.location.replace(customerCareUrl);
    } else if (mainModule === 'Membership') {
      localStorage.setItem('sidebar', 'dashboard');
      window.location.replace(membershipUrl);
    } else if (mainModule === 'Claims') {
      localStorage.setItem('sidebar', 'dashboard');
      window.location.replace(claimsUrl);
    } else if (mainModule.toLowerCase() === 'franchising') {
      localStorage.setItem('sidebar', 'dashboard');
      window.location.replace(franchisingUrl);
    } else {
      window.location.replace(systemAdminUrl);
    }
  }

  // Fetch Icd10 systematically
  const fetchIcd10 = async () => {
    let filter = {
      limit: 20000,
      skip: icdFetched
    }
    let url = `${process.env.REACT_APP_BASE_URL}icd10-codes?filter=${JSON.stringify(filter)}`;
    let options = {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    }

    await fetch(url, options)
      .then((res) => res.json())
      .then((data) => {
        if (data.length > 0) {
          icdFetched = icdFetched + data.length;
          icd10List.push(...data);
          fetchIcd10();
        } else {
          loginStorageService.saveEntry(icd10List, 'icd10_list').then(() => {
            icd10FetchDone = true;
            if (cptFetchDone) {
              redirect();
            }
          }).catch((err) => console.log(err));
        }
      })
      .catch((err) => console.log(err))
  }

  // Fetch Icd10 updates
  const fetchIcd10Update = async () => {
    let query = await loginStorageService.getSingleEntryByKeyReturnValue('icd10', 'date_updated');
    let url = `${process.env.REACT_APP_BASE_URL}icd10-codes/latest/${query.result}`;
    let options = {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    }

    await fetch(url, options)
      .then((res) => res.json())
      .then((data) => {
        let newItems: Array<any> = [];

        data.forEach((i: any) => {
          let item = { key: '', value: {} };

          item.key = i._id;
          item.value = i;

          newItems.push(item);

          loginStorageService.saveEntry(newItems, 'icd10_list').then((res) => {
            console.log(res);
          }).catch((err) => console.log(err));
        })
      })
      .catch((err) => console.log(err));

    icd10FetchDone = true;
    if (cptFetchDone) {
      redirect();
    }
  }

  // Fetch Cpt systematically
  const fetchCpt = async () => {
    let filter = {
      limit: 1000,
      skip: cptFetched
    }
    let url = `${process.env.REACT_APP_BASE_URL}cpts?filter=${JSON.stringify(filter)}`;
    let options = {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    }

    await fetch(url, options)
      .then((res) => res.json())
      .then((data) => {
        if (data.length > 0) {
          cptFetched = cptFetched + data.length;
          cptList.push(...data);
          fetchCpt();
        } else {
          loginStorageService.saveEntry(cptList, 'cpt_list').then(() => {
            cptFetchDone = true;
            if (icd10FetchDone) {
              redirect();
            }
          }).catch((err) => console.log(err));
        }
      })
      .catch((err) => console.log(err))
  }

  // Fetch Cpt updates
  const fetchCptUpdate = async () => {
    let query = await loginStorageService.getSingleEntryByKeyReturnValue('cpt', 'date_updated');
    let url = `${process.env.REACT_APP_BASE_URL}cpts/latest/${query.result}`;
    let options = {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    }

    await fetch(url, options)
      .then((res) => res.json())
      .then((data) => {
        let newItems: Array<any> = [];

        data.forEach((i: any) => {
          let item = { key: '', value: {} };

          item.key = i._id;
          item.value = i;

          newItems.push(item);

          loginStorageService.saveEntry(newItems, 'cpt_list').then((res) => {
            console.log(res);
          }).catch((err) => console.log(err));
        })
      })
      .catch((err) => console.log(err));

    cptFetchDone = true;
    if (icd10FetchDone) {
      redirect();
    }
  }

  // Process saving to indexedDB
  const saveToIndexedDB = async (data: any) => {

    let userDataToSave = Object.entries(data.login).map(entry => {
      return { key: entry[0], value: entry[1] }
    });

    let icd10ToSave = Object.entries(data.icd10).map(entry => {
      return { key: entry[0], value: entry[1] }
    });

    let cptToSave = Object.entries(data.cpt).map(entry => {
      return { key: entry[0], value: entry[1] }
    });

    loginStorageService.saveEntry(userDataToSave, 'user_data').then((res) => {
      console.log(res);
    }).catch((err) => console.log(err));

    loginStorageService.getSingleEntryByKeyReturnValue('icd10', 'date_updated').then((du) => {
      if (du.result === data.icd10.date_updated) {
        loginStorageService.validateStoreCount('himsDb', 'icd10_list').then((res: number) => {
          console.log(res);
          if (res === 0) {
            fetchIcd10();
          } else {
            redirect();
          }
        })
      } else {
        fetchIcd10Update();
        loginStorageService.updateEntry('icd10', 'date_updated', data.icd10.date_updated).then(() => {
        }).catch(err => console.log(err));
      }
    }).catch(() => {
      loginStorageService.saveEntry(icd10ToSave, 'icd10').then((res) => {
        console.log(res);
        fetchIcd10();
      }).catch((err) => console.log(err));
    });

    loginStorageService.getSingleEntryByKeyReturnValue('cpt', 'date_updated').then((du) => {
      if (du.result === data.cpt.date_updated) {

        loginStorageService.validateStoreCount('himsDb', 'cpt_list').then((res: number) => {
          if (res === 0) {
            fetchCpt();
          } else {
            redirect();
          }
        })
      } else {
        fetchCptUpdate();
        loginStorageService.updateEntry('cpt', 'date_updated', data.cpt.date_updated).then((r) => {
          console.log(r);
        }).catch(err => console.log(err));
      }
    }).catch(() => {
      loginStorageService.saveEntry(cptToSave, 'cpt').then(() => {
        fetchCpt();
      }).catch((err) => console.log(err));
    });
  }

  return (
    <div className="bg-login d-flex vni-items-center vni-min-h-screen vni-m-auto">
      <div className="vni-mx-auto vni-bg-white vni-p-16 vni-shadow-md">
        <h1 className="vni-text-3xl vni-font-medium vni-mb-6">
          Intellicare Franchising
        </h1>
        <form className="login-form" onSubmit={handleSubmit}>
          <div className="vni-mb-6">
            <InputLabel className="vni-uppercase vni-mb-1 vni-block loginLabel grey">
              Username
            </InputLabel>
            <TextField
              placeholder="Salesuser1"
              variant="outlined"
              className="w-100 CustomInput h50x"
              type="text"
              name=""
              onChange={handleTextFieldOnChange("username")}
            />
          </div>
          <div className="vni-mb-8">
            <InputLabel className="vni-uppercase vni-mb-1 vni-block loginLabel grey">
              Password
            </InputLabel>
            <TextField
              placeholder="********"
              variant="outlined"
              type="password"
              className="vni-w-full CustomInput h50x"
              name=""
              onChange={handleTextFieldOnChange("password")}
            />
          </div>
          <Button
            className="w-100 CustomPrimaryButton caps"
            variant="contained"
            type="submit"
            name="button"
            color="primary"
            disabled={isFetching ? true : false}
            disableElevation
          >
            {isFetching ? "Initializing..." : "LOG IN"}
          </Button>
        </form>
      </div>
    </div>
  );
};

export default LoginContainer;
