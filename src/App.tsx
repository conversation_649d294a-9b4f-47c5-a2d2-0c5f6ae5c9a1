 import React from 'react';
 import { Provider } from 'react-redux';
 import { ThemeProvider } from '@mui/styles';
 import { createTheme, StyledEngineProvider } from '@mui/material/styles';
 import AppContainer from './AppContainer';
 import store from './store';

const theme = createTheme();

function App() {
  return (
    <Provider store={store}>
      <StyledEngineProvider injectFirst>
        <ThemeProvider theme={theme}>
          <AppContainer />
        </ThemeProvider>
      </StyledEngineProvider>
    </Provider>
  );
}

export default App;
