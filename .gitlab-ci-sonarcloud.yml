stages:
  - fetch
review_code:
  image:
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [""]
  stage: fetch
  tags:
    - rhel8-fr-sonar
  variables:
    GIT_STRATEGY: fetch
    SONAR_HOST_URL: https://sonarcloud.io
  script:
    - whoami
    - export NVM_DIR=""$HOME/.nvm"" && . ""$NVM_DIR/nvm.sh"" --no-use #load nvm
    - eval [ -s ""$NVM_DIR/nvm.sh"" ] && \. ""$NVM_DIR/nvm.sh""
    - nvm use 20
    - ~/sonar-scanner-5.0.1.3006-linux/bin/sonar-scanner -Dsonar.sources=src -Dsonar.organization=veridata-hims-app -Dsonar.projectKey=hims-code-review_hims-app-dev -Dsonar.verbose=true
  only:
    - upgrade-fr-dev2
